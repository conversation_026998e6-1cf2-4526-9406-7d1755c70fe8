/*
 * Copyright (c) 2025 Verosint, Inc
 */

const DEFAULT_BASE_URL = "https://api.verosint.com";
const INTEGRATION_IDENTIFIER = "verosint-auth0-int/1.2.2";
const MAX_RETRY_COUNT = 1;
const RETRY_WAIT = 1500;
const AXIOS_TIMEOUT = 4000;

const axios = require("axios");

function validateNumbers(event) {
  const numberKeys = ["MFA_REPROMPT_FREQUENCY"];
  const notNumbers = numberKeys
    .filter((key) => key in event.secrets)
    .map((key) => ({ key, val: parseInt(event.secrets[key], 10) }))
    .filter(({ val }) => Number.isNaN(val))
    .map(({ key }) => key);
  return notNumbers;
}

function hasInvalidConfig(event) {
  if (!event?.secrets?.API_KEY || !event?.secrets?.WORKFLOW_UUID) {
    console.log("Missing required configuration for Verosint action");
    return true;
  }
  const invalidNumberSettings = validateNumbers(event);
  if (invalidNumberSettings.length > 0) {
    console.log(`Invalid numbers found in configuration: ${invalidNumberSettings.join(", ")}`);
    return true;
  }
  return false;
}

function checkForRecentMFA(event) {
  const mfaMethods = event.authentication?.methods?.filter((m) => m.name === "mfa");
  if (!mfaMethods || mfaMethods.length === 0) return false;
  const recentlyMFAed = mfaMethods.filter((m) => {
    const lastMFA = Date.parse(m.timestamp);
    const secondsSinceMFA = Math.floor((Date.now() - lastMFA) / 1000);
    const mfaRepromptFrequency = parseInt(event.secrets.MFA_REPROMPT_FREQUENCY, 10);
    const recentlyPrompted = secondsSinceMFA < mfaRepromptFrequency;
    return recentlyPrompted;
  });
  return recentlyMFAed.length > 0;
}

function buildRuleEvaluationRequestBody(event) {
  const { email } = event.user;
  const phone = event.user.phone_number;
  const deviceId = event?.request?.query?.verosint_deviceid;
  let sessionId = event?.session?.id;
  const targetApp = event?.client?.name;

  if (event?.transaction?.protocol === "oauth2-refresh-token") {
    sessionId = event?.refresh_token?.session_id
  }

  return {
    identifiers: Object.fromEntries(
      Object.entries({
        email,
        phone,
        ip: event.request.ip,
        userAgent: event.request.user_agent,
        accountId: event.user.user_id,
        givenName: event.user.given_name,
        familyName: event.user.family_name,
        deviceId,
        sessionId,
        targetApp,
      }).filter(([k, v]) => k && v) // Filter out any null or undefined signals so they are not included on the request
    ),
    parameters: Object.fromEntries(
      Object.entries(event).filter(([k, v]) => k !== "secrets" && k !== "configuration") // Filter out secrets and configuration
    ),
    ruleSetUuid: event.secrets.WORKFLOW_UUID,
    redirectUrl: `https://${event.request.hostname}/continue`
  }
}

function buildRuleEvaluationRequestParameters(event) {
  const requestBody = buildRuleEvaluationRequestBody(event);
  const baseUrl = DEFAULT_BASE_URL;
  const userAgent = `${INTEGRATION_IDENTIFIER} (axios/${axios.VERSION} node/${process.version})`;
  return {
    method: "POST",
    url: `${baseUrl}/v1/rules/evaluate`,
    data: requestBody,
    timeout: AXIOS_TIMEOUT,
    headers: {
      Authorization: `Bearer ${event.secrets.API_KEY}`,
      "Content-Type": "application/json",
      "User-Agent": userAgent,
    },
  };
}

function buildGetEvaluationRequestParameters(event, evaluationId) {;
  const baseUrl = DEFAULT_BASE_URL;
  const userAgent = `${INTEGRATION_IDENTIFIER} (axios/${axios.VERSION} node/${process.version})`;
  return {
    method: "GET",
    url: `${baseUrl}/v1/rules/evaluate/${evaluationId}`,
    timeout: AXIOS_TIMEOUT,
    headers: {
      Authorization: `Bearer ${event.secrets.API_KEY}`,
      "Content-Type": "application/json",
      "User-Agent": userAgent,
    },
  };
}

function retryPromise(retryIn) {
  return new Promise((resolve) => {
    setTimeout(async () => {
      resolve();
    }, retryIn);
  });
}

function configureAxiosRetry() {
  let retryCount = 0;
  axios.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      if (retryCount === MAX_RETRY_COUNT || error.response.status < 500) {
        return Promise.reject(error);
      }
      retryCount += 1;
      // eslint-disable-next-line no-param-reassign
      const requestParameters = error.config;
      requestParameters.headers["X-Retry-Attempt"] = retryCount;
      console.log(`5xx returned. Retrying ${retryCount}`);
      return retryPromise(RETRY_WAIT).then(() => axios.request(requestParameters));
    }
  );
}

function deny(event, api, msg) {
  console.log(`Verosint action denied access: ${msg}`);
  if (event.secrets.LOG_ONLY !== "true") {
    api.access.deny(msg);
  }
}

function triggerMFA(event, api, msg) {
  const recentMFA = checkForRecentMFA(event);
  if (recentMFA) {
    allow(event, api, `User completed MFA within the last ${event.secrets.MFA_REPROMPT_FREQUENCY}s`);
    return;
  }
  console.log(`Verosint action triggered MFA: ${msg}`);
  if (event.secrets.LOG_ONLY !== "true") {
    const canEnrollMFA = event.stats?.logins_count <= 1 && event.secrets.REQUIRE_MFA_ENROLLMENT !== "false";
    const canPromptMFA = event.user?.multifactor?.length > 0;

    if (canPromptMFA || canEnrollMFA) {
      api.multifactor.enable("any");
    } else {
      deny(event, api, "No enrolled MFA factors");
    }
  }
}

function allow(event, api, msg) {
  console.log(`Verosint action allowed access: ${msg}`);
}

function enrollMFA(event, api) {
  if (event.secrets.LOG_ONLY !== 'true') {
    api.multifactor.enable('any');
  }
}

function triggerEmailChallenge(event, api, msg) {
  console.log(`Verosint action triggered email challenge: ${msg}`);
  if (event.secrets.LOG_ONLY !== "true") {
    if (event.user.multifactor && event.user.email_verified) {
      api.authentication.challengeWith({type: 'email'});
    } else {
      console.log("user could not be prompted with an email");
    }
  }
}

async function makeRuleRequest(event, api, ruleEvaluationRequest) {
  let response
  try {
    response = await axios.request(ruleEvaluationRequest);
  } catch (error) {
    let message
    if (error?.response?.data?.error) {
      message = `Error returned from Verosint RuleSet Evaluate API: ${error.response.data.error}`;
    } else {
      message = `Error calling Verosint RuleSet Evaluate API: ${error?.message}`;
    }
    if (event.secrets.ON_ERROR === "continue") {
      allow(event, api, message)
      return;
    }
    if (event.secrets.ON_ERROR === "mfa") {
      triggerMFA(event, api, message)
      return;
    }
    deny(event, api, message);
    return;
  }


  const reason = response.data?.reasons?.[0] || "unexpected outcome"

  if (response.data.outcomes.includes("ALLOW")) {
    allow(event, api, reason);
    return;
  } 
  if ((response.data.outcomes.includes("ALLOW_WITH_MFA")) || (response.data.outcomes.includes("CHALLENGE"))){
    // check if require_mfa_enrollment set, if not trigger email
    if (event.secrets.REQUIRE_MFA_ENROLLMENT !== "false") {
      triggerMFA(event, api, reason);
    } else {
      triggerEmailChallenge(event, api, reason);
    }
    return;
  } 
  if (ruleEvaluationRequest.method === "POST" && response.data.outcomes.includes("PROOF")) {
    api.user.setUserMetadata("pendingEvaluationId", response.data.evaluationId)
    api.redirect.sendUserTo(response.data.verifyLink);
    return;
  }
  deny(event, api, reason);  
}


/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  /**
   * Required Configuration (Action will fail if any of the below is not defined)
   * - `API_KEY` The API key from your Verosint account (Risk Scoring -> Policies).
   * - `RULE_SET_UUID` The rule set uuid from your Verosint account's ruleset of choice.
   */

  // Merge all config props into event.secrets for easy testing when action is not yet published to the marketplace
  event.secrets = { ...event.configuration, ...event.secrets };
  
  const isInvalidConfig = hasInvalidConfig(event);
  if (isInvalidConfig) {
    api.access.deny("Invalid configuration for Verosint action");
    return;
  }

  // MFA enrollment
  const canPromptMFA = event?.user?.multifactor?.length > 0; // some multifactor configured for user
  const firstLogin = event.stats?.logins_count <= 1; 
  const shouldEnrollMFA = !canPromptMFA && event.secrets.REQUIRE_MFA_ENROLLMENT !== 'false' && firstLogin;
  if (shouldEnrollMFA) {
    console.log('User not enrolled in MFA. Enrolling MFA');
    enrollMFA(event, api);
    return;
  }

  // if bypass = true AND prompt = none (indicates silent auth) then allow
  if ((event.secrets.BYPASS_FOR_SILENT_AUTH === 'true') && (event.request.query.prompt === 'none')) {
    allow(event, api, `Silent Authentication`);
  } else {
    configureAxiosRetry();

    // Setup AXIOS HTTP Call for the Verosint API RuleSet Evaluate endpoint
    const ruleEvaluationRequest = buildRuleEvaluationRequestParameters(event);

    await makeRuleRequest(event, api, ruleEvaluationRequest);
  }
};

exports.onContinuePostLogin = async (event, api) => {
  const pendingEvaluationId = event.user?.user_metadata?.pendingEvaluationId
  if (!pendingEvaluationId) {
    api.access.deny("Error getting evaluation ID from user_metadata");
    return;
  }
  api.user.setUserMetadata("pendingEvaluationId", null)

  // Merge all config props into event.secrets for easy testing when action is not yet published to the marketplace
  event.secrets = { ...event.configuration, ...event.secrets };

  const isInvalidConfig = hasInvalidConfig(event);
  if (isInvalidConfig) {
    api.access.deny("Invalid configuration for Verosint action");
    return;
  }

  configureAxiosRetry();

  // Setup AXIOS HTTP Call for the Verosint API RuleSet Get Evaluation endpoint
  const ruleEvaluationRequest = buildGetEvaluationRequestParameters(event, pendingEvaluationId);

  await makeRuleRequest(event, api, ruleEvaluationRequest);
};
