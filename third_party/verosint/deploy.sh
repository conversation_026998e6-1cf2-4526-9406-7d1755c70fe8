#!/bin/sh

die() {
  echo "$1" >&2
  exit 1
}

mkdir build
cp integration.action.js build/integration.action.js
patch build/integration.action.js < grandfather.diff || die "Failed to apply patch"
#auth0 actions create --name 'Verosint Workflows fork' --trigger post-login --code "$(cat integration.action.js)" --dependency "axios==1.7.7" --secret "LOG_ONLY=false" --secret "ON_ERROR=deny" --secret "MFA_REPROMPT_FREQUENCY=36000" --secret "REQUIRE_MFA_ENROLLMENT=false" --secret "BYPASS_FOR_SILENT_AUTH=true" --secret "API_KEY=$1" --secret "WORKFLOW_UUID=$2"
auth0 actions update --name 'Verosint Workflows fork' --code "$(cat build/integration.action.js)" --dependency "axios==1.7.7" --secret "LOG_ONLY=false" --secret "ON_ERROR=deny" --secret "MFA_REPROMPT_FREQUENCY=36000" --secret "REQUIRE_MFA_ENROLLMENT=false" --secret "BYPASS_FOR_SILENT_AUTH=true" --secret "API_KEY=$1" --secret "WORKFLOW_UUID=$2"
