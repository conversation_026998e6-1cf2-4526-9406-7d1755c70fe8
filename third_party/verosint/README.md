This is the Verosint auth0 action along with our patches to it. The patch
grandfathers existing users, exempting them from the Verosint workflow
DENY's.

The source of the verosint script is this repository:
https://gitlab.com/verosint/public/integration-auth0/-/tree/main/actions/rules/integration?ref_type=heads

There is a lot of room for improvement:
 - there are no tests. Verosint repository has tests but the mocks are missing. They are likely in the
   auth0/marketplace-integration-tools docker container.
 - CI is outside of Bazel
 - API key and workflow UUID must be speicifed on command line while deploying
 - Deployment is not integrated into our deployment system (true of all Auth0 configuration)

I'm not doing the clean-up right now because I'm not sure we want to block users at sign-up, and
I estimate fixing the first two items will consume a couple days.

If we end up getting comfortable with blocking users at sign up, we will need to fix these problems.
Longer term, we should consider creating our own action that runs against our own infrastructure.
Our infrastructure could then call out to Verosint / Verisoul / whatever as needed.

Priority would be to get the build into <PERSON>zel, move to our own infrastructure for providing ALLOW/DENY
judgements, then create our own hook with our own tests.

# Manual deployment

```
    ./deploy.sh <api-key> <workflow-uuid>
```

Need to provide API key and workflow UUID on the command line. These can be found in the
Verosint console.

Auth0 CLI tool will prompt you for which action to update. If an action doesn't exist, you
need to create one in the Auth0 console.

Even after updating, you'll need to "deploy" your update for it to take effect.
You can use `auth0 actions list` to get the ID of the action, followed by `auth0 actions deploy`
to actually do the deploy.
