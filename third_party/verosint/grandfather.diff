--- integration.action.js.orig	2025-06-10 13:02:28.533772130 +0000
+++ integration.action.js	2025-06-10 13:10:33.024358807 +0000
@@ -150,6 +150,7 @@
 function triggerMFA(event, api, msg) {
   const recentMFA = checkForRecentMFA(event);
   if (recentMFA) {
+    api.user.setUserMetadata("grandfathered", true);
     allow(event, api, `User completed MFA within the last ${event.secrets.MFA_REPROMPT_FREQUENCY}s`);
     return;
   }
@@ -187,7 +188,7 @@
   }
 }
 
-async function makeRuleRequest(event, api, ruleEvaluationRequest) {
+async function makeRuleRequest(event, api, ruleEvaluationRequest, grandfathered) {
   let response
   try {
     response = await axios.request(ruleEvaluationRequest);
@@ -199,6 +200,7 @@
       message = `Error calling Verosint RuleSet Evaluate API: ${error?.message}`;
     }
     if (event.secrets.ON_ERROR === "continue") {
+      api.user.setUserMetadata("grandfathered", true);
       allow(event, api, message)
       return;
     }
@@ -214,6 +216,8 @@
   const reason = response.data?.reasons?.[0] || "unexpected outcome"
 
   if (response.data.outcomes.includes("ALLOW")) {
+    api.user.setUserMetadata("grandfathered", true);
+
     allow(event, api, reason);
     return;
   } 
@@ -231,6 +235,10 @@
     api.redirect.sendUserTo(response.data.verifyLink);
     return;
   }
+  if (grandfathered) {
+    allow(event, api, "User grandfathered");
+    return;
+  }
   deny(event, api, reason);  
 }
 
@@ -257,9 +265,15 @@
     return;
   }
 
+  const firstLogin = event.stats?.logins_count <= 1; 
+  var grandfathered = event.user?.user_metadata?.grandfathered;
+  if (grandfathered === undefined) {
+    grandfathered = !firstLogin;
+    api.user.setUserMetadata("grandfathered", grandfathered);
+  }
+
   // MFA enrollment
   const canPromptMFA = event?.user?.multifactor?.length > 0; // some multifactor configured for user
-  const firstLogin = event.stats?.logins_count <= 1; 
   const shouldEnrollMFA = !canPromptMFA && event.secrets.REQUIRE_MFA_ENROLLMENT !== 'false' && firstLogin;
   if (shouldEnrollMFA) {
     console.log('User not enrolled in MFA. Enrolling MFA');
@@ -269,6 +283,8 @@
 
   // if bypass = true AND prompt = none (indicates silent auth) then allow
   if ((event.secrets.BYPASS_FOR_SILENT_AUTH === 'true') && (event.request.query.prompt === 'none')) {
+    // NOTE: we do not set grandfathered here for silent authentication because there was no rule evaluation
+
     allow(event, api, `Silent Authentication`);
   } else {
     configureAxiosRetry();
@@ -276,7 +292,7 @@
     // Setup AXIOS HTTP Call for the Verosint API RuleSet Evaluate endpoint
     const ruleEvaluationRequest = buildRuleEvaluationRequestParameters(event);
 
-    await makeRuleRequest(event, api, ruleEvaluationRequest);
+    await makeRuleRequest(event, api, ruleEvaluationRequest, grandfathered);
   }
 };
 
@@ -302,5 +318,7 @@
   // Setup AXIOS HTTP Call for the Verosint API RuleSet Get Evaluation endpoint
   const ruleEvaluationRequest = buildGetEvaluationRequestParameters(event, pendingEvaluationId);
 
-  await makeRuleRequest(event, api, ruleEvaluationRequest);
+  // undefined -> grandfathered, true -> grandfathered, false -> not grandfathered
+  const grandfathered = !(event.user?.user_metadata?.grandfathered === false);
+  await makeRuleRequest(event, api, ruleEvaluationRequest, grandfathered);
 };
