load("@rules_oci//oci:defs.bzl", "oci_push")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")

py_library(
    name = "slack_utils",
    srcs = ["slack_utils.py"],
    deps = [
        requirement("slack_sdk"),
    ],
)

py_library(
    name = "slack_feedback_summarizer_lib",
    srcs = ["slack_feedback_summarizer.py"],
    deps = [
        ":slack_utils",
        "//base/third_party_clients:clients",
        "//research/environments",
        requirement("google-cloud-storage"),
        requirement("structlog"),
    ],
)

py_binary(
    name = "slack_feedback_summarizer",
    srcs = ["slack_feedback_summarizer.py"],
    deps = [
        ":slack_feedback_summarizer_lib",
    ],
)

py_binary(
    name = "all_channels_report",
    srcs = ["slack_feedback_summarizer.py"],
    args = [
        "--all-channels-report",
        "--output=gs://gcp-us1-public-html/feedback-reports",
    ],
    deps = [
        ":slack_feedback_summarizer_lib",
    ],
)

py_oci_image(
    name = "slack_feedback_summarizer_oci",
    package_name = package_name(),
    binary = ":slack_feedback_summarizer",
)

oci_push(
    name = "push_slack_feedback_summarizer",
    image = ":slack_feedback_summarizer_oci",
    repository = "__CHANGE_ME__",  # A default repository, overridden as part of deployment.
)
