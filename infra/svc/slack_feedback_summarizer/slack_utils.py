"""Utilities for some common tasks when working with Slack."""

import datetime
import logging
import time
from dataclasses import dataclass, field
from functools import lru_cache
from typing import List

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple class to limit the number of calls per second to an API."""

    def __init__(self, max_calls_per_second: int = 100):
        self.last_call = 0
        self._interval = 1.0 / max_calls_per_second

    def acquire(self):
        now = time.time()
        sleep_time = max(0, self._interval - (now - self.last_call))
        time.sleep(sleep_time)
        self.last_call = now
        return sleep_time


@dataclass
class SlackMessage:
    """Represents a Slack message with its metadata."""

    text: str
    user: str
    ts: str
    permalink: str
    thread_ts: str | None = None
    replies: list["SlackMessage"] = field(default_factory=list)


class SlackClient:
    """Wrapper around the Slack WebClient to handle rate limiting."""

    def __init__(self, token: str, max_calls_per_second: int = 100):
        self.client = WebClient(token=token)
        self.rate_limiter = RateLimiter(max_calls_per_second)

    def conversations_list(self, **kwargs):
        self.rate_limiter.acquire()
        return self.client.conversations_list(**kwargs)

    @lru_cache(maxsize=1)
    def list_available_channels(self) -> list[dict]:
        """List all available channels.

        Returns:
            List of channel objects with 'id' and 'name' keys
        """
        channels = []
        next_cursor = None

        try:
            # Get public channels with pagination
            while True:
                params = {"limit": 1000}
                if next_cursor:
                    params["cursor"] = next_cursor

                # Add a delay to avoid rate limiting
                self.rate_limiter.acquire()

                response = self.client.conversations_list(**params)  # type: ignore
                if response and "channels" in response:
                    channels.extend(response["channels"])  # type: ignore

                # Check if there are more pages
                next_cursor = response.get("response_metadata", {}).get("next_cursor")
                if not next_cursor:
                    break
            return channels
        except SlackApiError:
            logger.exception("Error listing channels")
            return []

    def get_channel_id(self, channel_name: str) -> str:
        """Get the channel ID from the channel name.

        Args:
            channel_name: Name of the channel

        Returns:
            Channel ID

        Raises:
            ValueError: If the channel is not found
        """
        channels = self.list_available_channels()
        for channel in channels:
            if channel["name"] == channel_name:
                return channel["id"]
        # If channel not found, list available channels
        logger.info("Listing available channels...")
        for channel in channels:
            logger.info(f"- {channel['name']} (id: {channel['id']})")

        raise ValueError(f"Channel '{channel_name}' not found")

    def get_messages(
        self,
        channel_id: str,
        start_date: datetime.datetime,
        end_date: datetime.datetime,
    ) -> List[SlackMessage]:
        """Get messages from the channel within the specified date range.

        Args:
            start_date: Start date for messages
            end_date: End date for messages

        Returns:
            List of SlackMessage objects
        """
        messages = []

        # Convert dates to Unix timestamps
        oldest = start_date.timestamp()
        latest = end_date.timestamp()

        try:
            # Add a delay to avoid rate limiting
            self.rate_limiter.acquire()

            # Get conversation history
            result = self.client.conversations_history(
                channel=channel_id,
                oldest=str(oldest),
                latest=str(latest),
                limit=1000,  # Maximum allowed by Slack API
            )

            # Convert to SlackMessage objects
            if not result or "messages" not in result:
                return []

            for msg in result["messages"]:  # type: ignore
                # Skip bot messages and system messages
                if "subtype" in msg and msg["subtype"] in [
                    "bot_message",
                    "channel_join",
                    "channel_leave",
                ]:
                    continue

                text = ""
                for block in msg.get("blocks", []):
                    if block["type"] == "section" and "text" in block:
                        text += block["text"].get("text", "")

                message = SlackMessage(
                    text=text,
                    user=msg.get("user", "Unknown"),
                    ts=msg["ts"],
                    permalink=self._get_permalink(channel_id, msg["ts"]),
                    thread_ts=msg.get("thread_ts"),
                )

                # If this is a thread parent, get the replies
                if msg.get("reply_count", 0) > 0:
                    self._add_thread_replies(channel_id, message)
                messages.append(message)

            return messages

        except SlackApiError:
            logger.exception("Error getting messages")
            return []

    def _get_permalink(self, channel_id: str, ts: str) -> str:
        """Get the permalink for a message.

        Args:
            ts: Timestamp of the message

        Returns:
            Permalink URL
        """
        try:
            # Add a delay to avoid rate limiting
            self.rate_limiter.acquire()

            response = self.client.chat_getPermalink(channel=channel_id, message_ts=ts)
            if response and isinstance(response, dict) and "permalink" in response:
                return response["permalink"]  # type: ignore
            return f"https://slack.com/archives/{channel_id}/p{ts.replace('.', '')}"
        except SlackApiError:
            logger.exception("Error getting permalink")
            return f"https://slack.com/archives/{channel_id}/p{ts.replace('.', '')}"

    def _add_thread_replies(self, channel_id: str, message: SlackMessage) -> None:
        """Get replies for a thread and add them to the message.

        Args:
            message: The parent message to get replies for
        """
        try:
            # Add a delay to avoid rate limiting
            self.rate_limiter.acquire()

            # Get thread replies
            result = self.client.conversations_replies(
                channel=channel_id,
                ts=message.ts,
                limit=1000,  # Maximum allowed by Slack API
            )

            # Skip the first message (it's the parent)
            if result and "messages" in result:
                messages_list = result.get("messages", [])
                if len(messages_list) > 1:
                    for reply in messages_list[1:]:
                        reply_msg = SlackMessage(
                            text=reply.get("text", ""),
                            user=reply.get("user", "Unknown"),
                            ts=reply["ts"],
                            permalink="",
                            thread_ts=reply.get("thread_ts"),
                        )

                        message.replies.append(reply_msg)

        except SlackApiError:
            logger.exception("Error getting thread replies")

    @lru_cache(maxsize=1000)
    def _get_user_name(self, user_id: str) -> str:
        """Get the user's name from their ID.

        Args:
            user_id: User ID

        Returns:
            User's name or the ID if not found
        """
        try:
            # Add a delay to avoid rate limiting
            self.rate_limiter.acquire()

            result = self.client.users_info(user=user_id)
            if result and isinstance(result, dict) and "user" in result:
                user_data = result["user"]
                if isinstance(user_data, dict) and "real_name" in user_data:
                    return user_data["real_name"]
            return user_id
        except SlackApiError:
            return user_id

    def send_message(
        self, channel_id: str, text: str, blocks: list | None = None
    ) -> bool:
        """Send a message to a Slack channel.

        Args:
            channel_id: ID of the channel to send the message to
            text: Text of the message (fallback for notifications)
            blocks: Optional blocks for rich formatting

        Returns:
            True if successful, False otherwise
        """
        try:
            # Add a delay to avoid rate limiting
            self.rate_limiter.acquire()

            # Send the message
            result = self.client.chat_postMessage(
                channel=channel_id,
                text=text,
                blocks=blocks if blocks else None,
            )

            return bool(result and result.get("ok", False))
        except SlackApiError:
            logger.exception("Error sending message to Slack")
            return False
