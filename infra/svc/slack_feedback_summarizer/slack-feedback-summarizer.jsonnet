local clusters = import '../../../research/infra/cfg/clusters/clusters.jsonnet';

function(
  cluster='gcp-us1',
  name='slack-feedback-summarizer',
  namespace=null,
  image=null,
  cpu='4',
  ram='16G',
  version='latest',
  output_path='gs://gcp-us1-public-html/feedback-reports',
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  BaseObject+:: {
    name:: name,
  },

  Object+:: {
    namespace:: if namespace != null then namespace else C.main_namespace,
  },

  GCP+:: {
    Object+:: {
      project_id:: C.gcp_project,
      namespace:: 'cc-' + C.name,
    },
  },

  // The K8s SA has read access to the Anhtropic and Slack secrets. It also is paired
  // with a Google SA for GCS Access.
  ksa: $.ServiceAccount + {
    name:: name,
    metadata+: {
      annotations+: {
        'iam.gke.io/gcp-service-account': $.gsa.email,
      },
    },
  },

  // The Google SA has write access to the public-html bucket.
  gsa: $.GCP.IAM.ServiceAccount + {
    name:: name,
  },

  wi_binding: $.GCP.IAM.PolicyMember + {
    name:: $.gsa.metadata.name + '.workload-identity-user',
    spec+: {
      resourceRef: $.gsa.localKindRef,
      member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, $.ksa.metadata.namespace, $.ksa.metadata.name],
      role: 'roles/iam.workloadIdentityUser',
    },
  },

  gcs_acl: $.GCP.IAM.PolicyMember + {
    name:: $.gsa.metadata.name + '.public-html.storage-objectuser',
    spec+: {
      resourceRef: {
        kind: 'StorageBucket',
        external: C.name + '-public-html',
      },
      member: 'serviceAccount:' + $.gsa.email,
      role: 'roles/storage.objectUser',
    },
  },

  rb: $.RoleBinding + {
    name+:: '-eng-secrets-reader',
    role_name:: 'aug:eng-secrets-reader-role',
    namespace:: 'eng-secrets',
    sas:: [$.ksa],
  },

  job: $.CronJob + {
    // Every sunday at noon. Try 5 times, every 2 hours.
    spec+: {
      schedule: '0 12 * * 0',
      timeZone: 'US/Pacific',
      concurrencyPolicy: $.CRONJOB_POLICY.REPLACE,

      jobTemplate+: {
        spec+: {
          parallelism: 1,
          completions: 1,
          backoffLimit: 5,
          activeDeadlineSeconds: 2 * 60 * 60,
        },
      },
    },

    podspec:: {
      local pod = self,
      serviceAccountName: $.ksa.metadata.name,
      restartPolicy: $.RESTART_POLICY.NEVER,
      containers: [
        $.Container + {
          name: name,
          image: image,
          volumeMounts: pod.volmount_mounts,
          resources: {
            limits: {
              cpu: cpu,
              memory: ram,
            },
          },
          args: [
            '--all-channels-report',
            '--output=' + output_path,
            '--slack-token=/run/secrets/' + $.sec.name + '/slack-bot-token',
          ],
        },
      ],
      volmounts:: [
        {
          name: $.sec.name,
          volume: {
            secret: {
              secretName: $.sec.name,
            },
          },
          mount: {
            mountPath: '/run/secrets/' + $.sec.name,
            readOnly: true,
          },
        },
      ],
    },
  },

  sec: $.SealedSecret + {
    encryptedData: {
      'slack-bot-token': 'AgA6FvFdRUHij8PuXfr00SS520QhUmrTF5OVikooBiUKj2gBgBEVQ7dknGPD865O3Pc+vuJqW3EqYtwOBNrnn8onmDR47fDW66+lLlIAVxaMFD906MlKH5Nlkzr7FjqrXArp+SmkQRLZopX5sb66kpBNJcgtMBzgK7gZBTbjQCyH8QOkPOez3AhRpMj860BOlSaRASUvAV00F7rWvZvKNCMxaq0sEdDFf2GZZu2ddGHzE+97Cqsu1TZA3Hpj3iQn3AHkXbCQ7LQR4oUIfgyWdnIq8mKfTZFAr40dpG7Ot3QDGQJ3qq7/+YbtDfzbcmDxgsPelSAgWN9D8ryoVkzbPDhUA1fg6wAwX+uug+cCZkqc4p8tFqGOtCIeTWt5d/ogBUNXRsHe+6tD/hzmKQwWB0HVWuWzlLBG08LiyYZTVGBdcrdnSXX/c/RZARnP2WEoA8GaYHM6MAMqlNRw6+u0Lpd8khoWzuKkpEAkIpD4lYaK93WHIvmZ9lmy4d0qiKdBgwlTPPa3bxL5Sohuhlu1Cs63LjA/Lj+kid2hx5fPbHmCbkiR0SYtXjgFt415owmhKMTiYh7AkoiKGVXntmHOZxPDsMwBw4lFuI8U195OjfKYuXUzCtnyQQ/bGGvHIDw9irXF+AoNpFIxt/+tTVe/UEAvOSQ8Tfo1FY9Mjrj7rL+LoyyXMVvg1Ge9PKbqSnWLqWfTG3LU5i29eHu7RI0Bw5u4WoEsTX66MrXjdZjtc4Ki1GNbX3bCEynWpvgOrBibJR8NaaF82unNUjf4R5xfJ4h5SouUD9vmS+WGQYHUfxPe',
    },
  },
}
