load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")

package(default_visibility = ["//infra:internal"])

_JSONNET_SRC = glob(["*.jsonnet"])

exports_files(_JSONNET_SRC)

filegroup(
    name = "jsonnet_files",
    srcs = _JSONNET_SRC,
)

jsonnet_library(
    name = "enums",
    srcs = ["augment-enums.jsonnet"],
)

# NOTE(mattm): This isn't important, it's just a PoC.
jsonnet_to_json(
    name = "enums_json",
    src = "augment-enums.jsonnet",
    outs = ["augment-enums.json"],
    visibility = ["//visibility:private"],
)
