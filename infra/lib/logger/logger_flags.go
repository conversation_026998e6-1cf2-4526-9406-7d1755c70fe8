package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/mattn/go-isatty"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/spf13/pflag"
)

func IsTTY(w io.Writer) bool {
	if f, ok := w.(*os.File); ok {
		return isatty.IsTerminal(f.Fd())
	}
	return false
}

// GlobalLoggerFromFlags sets the global `zerolog/log.Logger` (and returns it, for convenience). Users can get the logger from the
// returned value, from the `zerolog/log.Logger` "global logger", from this package's `New()` function, or from the nil `*Logger`.
//
// The supported flags and behavior is meant to be useful for both servers/daemons and CLIs. The default behaviors are crontrolled
// by TTY detection but can each be set explicitly.
func GlobalLoggerFromFlags(f *pflag.FlagSet) func(w io.Writer) (*Logger, error) {
	v := struct {
		level string
		json  bool
		color bool
		utc   bool
		unix  bool
	}{}

	f.StringVar(&v.level, "log-level", zerolog.InfoLevel.String(), "Minimum log level.")
	f.BoolVar(&v.json, "log-json", false, "Log in JSON format. Defaults to unstructured, colored text on TTY and JSON otherwise.")
	f.BoolVar(&v.color, "log-color", true, "When logging as text, disable terminal colors. Default is to include colors on a TTY.")
	f.BoolVar(&v.utc, "log-utc", false, "Log in UTC time, the default is to use the local timezone on a TTY and UTC otherwise.")
	f.BoolVar(&v.unix, "log-unix", false, "Log timestamp as unix seconds rather than a human-friendly format.")

	return func(w io.Writer) (*Logger, error) {
		// Defaults

		if w == nil {
			w = os.Stderr
		}
		tty := IsTTY(w)
		if !f.Changed("log-json") {
			v.json = !tty
		}
		if !f.Changed("log-color") {
			v.color = tty
		}
		if !f.Changed("log-utc") {
			v.utc = !tty
		}

		// Time Format

		if v.unix {
			zerolog.TimeFieldFormat = zerolog.TimeFormatUnixMs
		} else {
			zerolog.TimeFieldFormat = time.RFC3339
		}

		if v.utc {
			// This is redundant for the ConsoleLogger, but needed for normal json logging.
			cur := zerolog.TimestampFunc
			zerolog.TimestampFunc = func() time.Time {
				return cur().UTC()
			}
		} else {
			// This is redundant for the ConsoleLogger, but needed for normal json logging.
			cur := zerolog.TimestampFunc
			zerolog.TimestampFunc = func() time.Time {
				return cur().Local()
			}
		}

		// Output Writer (Format)

		zerolog.CallerMarshalFunc = func(pc uintptr, file string, line int) string {
			if !v.json {
				file = filepath.Base(file)
			}
			return file + ":" + strconv.Itoa(line)
		}

		if !v.json {
			zerolog.FormattedLevels[zerolog.TraceLevel] = "TRACE"
			zerolog.FormattedLevels[zerolog.DebugLevel] = "DEBUG"
			zerolog.FormattedLevels[zerolog.InfoLevel] = "INFO "
			zerolog.FormattedLevels[zerolog.WarnLevel] = "WARN "
			zerolog.FormattedLevels[zerolog.ErrorLevel] = "ERROR"
			zerolog.FormattedLevels[zerolog.FatalLevel] = "FATAL"
			zerolog.FormattedLevels[zerolog.PanicLevel] = "PANIC"
			w = zerolog.ConsoleWriter{
				Out:        w,
				NoColor:    !v.color,
				PartsOrder: []string{zerolog.TimestampFieldName, zerolog.LevelFieldName, zerolog.MessageFieldName, zerolog.CallerFieldName},
				TimeLocation: func() *time.Location {
					if v.utc {
						return time.UTC
					}
					return time.Local
				}(),
				TimeFormat: time.RFC3339,
				FormatTimestamp: func() func(i interface{}) string {
					if v.unix {
						return func(i interface{}) string {
							if s, ok := i.(string); ok {
								if parsed, err := time.Parse(time.RFC3339, s); err == nil {
									return fmt.Sprintf("%d", parsed.Unix())
								}
							}
							return fmt.Sprintf("%v", i)
						}
					}
					return nil
				}(),
				FormatCaller: func(i interface{}) string {
					if !v.color {
						return fmt.Sprintf("%v", i)
					}
					return fmt.Sprintf("\033[2;34m%v\033[0m", i)
				},
			}
		}

		// Create / Set Global Logger

		log.Logger = zerolog.New(w).With().Timestamp().Caller().Logger()

		// Log Level

		if lvl, err := zerolog.ParseLevel(v.level); err != nil {
			return nil, err
		} else {
			log.Logger = log.Logger.Level(lvl)
			zerolog.SetGlobalLevel(lvl)
		}

		return New(nil), nil
	}
}
