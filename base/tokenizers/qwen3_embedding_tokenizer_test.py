"""Tests for the Qwen3-Embedding tokenizers."""

from base.tokenizers.qwen3_tokenizer import (
    Qwen3Tokenizer,
    Qwen3SpecialTokens,
    Qwen3EmbeddingSpecialTokens,
    Qwen3EmbeddingTokenizer,
)
from base.tokenizers.tokenizer import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_qwen3_embedding_06b_tokenizer():
    """Test Qwen3-Embedding-0.6B tokenizer."""
    tokenizer = Qwen3EmbeddingTokenizer(model_variant="0.6b")

    # Test basic tokenization - should auto-append EOS
    tokens = tokenizer.tokenize_unsafe("Hello, world!")
    assert len(tokens) > 0
    assert tokens[-1] == 151643  # <|endoftext|> token

    # Test that EOS is not duplicated
    tokens2 = tokenizer.tokenize_unsafe("Hello, world!<|endoftext|>")
    assert tokens2[-1] == 151643
    assert tokens2[-2] != 151643  # Should not duplicate

    # Test detokenization
    text = tokenizer.detokenize(tokens)
    assert text == "Hello, world!<|endoftext|>"

    # Test special tokens - should have all tokens including think/tool_response
    special_tokens = tokenizer.special_tokens
    assert special_tokens.eos == 151643
    assert special_tokens.im_start == 151644
    assert special_tokens.im_end == 151645
    assert special_tokens.think == 151667
    assert special_tokens.think_end == 151668
    assert special_tokens.tool_response == 151665
    assert special_tokens.tool_response_end == 151666

    # Test vocab size
    assert tokenizer.vocab_size == 151696  # 151643 base + 26 special + 27 additional


def test_qwen3_embedding_4b8b_tokenizer():
    """Test Qwen3-Embedding-4B/8B tokenizer."""
    tokenizer = Qwen3EmbeddingTokenizer(model_variant="4b")

    # Test basic tokenization - should auto-append EOS
    tokens = tokenizer.tokenize_unsafe("Hello, world!")
    assert len(tokens) > 0
    assert tokens[-1] == 151643  # <|endoftext|> token

    # Test that EOS is not duplicated
    tokens2 = tokenizer.tokenize_unsafe("Hello, world!<|endoftext|>")
    assert tokens2[-1] == 151643
    assert tokens2[-2] != 151643  # Should not duplicate

    # Test detokenization
    text = tokenizer.detokenize(tokens)
    assert text == "Hello, world!<|endoftext|>"

    # Test special tokens - should have sentinel values for missing tokens
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, Qwen3EmbeddingSpecialTokens)
    assert special_tokens.eos == 151643
    assert special_tokens.im_start == 151644
    assert special_tokens.im_end == 151645

    # Test vocab size - should be smaller due to missing tokens
    assert tokenizer.vocab_size == 151692  # 151643 base + 22 special + 27 additional


def test_qwen3_embedding_tokenizer_registry():
    """Test that all Qwen3-Embedding tokenizers are properly registered."""
    # Check all variants are registered
    assert "qwen3-embedding-0.6b" in dict(REGISTRY)
    assert "qwen3-embedding-4b" in dict(REGISTRY)
    assert "qwen3-embedding-8b" in dict(REGISTRY)
    assert "qwen3-embedding" in dict(REGISTRY)  # Default alias

    # Test 0.6B variant
    tokenizer_06b = REGISTRY.get("qwen3-embedding-0.6b")()
    # Now returns unified tokenizer, but check it's configured for 0.6b
    assert isinstance(tokenizer_06b, Qwen3EmbeddingTokenizer)
    assert tokenizer_06b._model_variant == "0.6b"
    # Check it has the correct vocab size for 0.6B
    assert tokenizer_06b.vocab_size == 151696

    # Test 4B variant
    tokenizer_4b = REGISTRY.get("qwen3-embedding-4b")()
    assert isinstance(tokenizer_4b, Qwen3EmbeddingTokenizer)
    assert tokenizer_4b._model_variant == "4b"
    # Check it has the correct vocab size for 4B/8B
    assert tokenizer_4b.vocab_size == 151692

    # Test 8B variant
    tokenizer_8b = REGISTRY.get("qwen3-embedding-8b")()
    assert isinstance(tokenizer_8b, Qwen3EmbeddingTokenizer)
    assert tokenizer_8b._model_variant == "8b"
    # Check it has the correct vocab size for 4B/8B
    assert tokenizer_8b.vocab_size == 151692

    # Test default alias points to 4B tokenizer
    tokenizer_default = REGISTRY.get("qwen3-embedding")()
    assert isinstance(tokenizer_default, Qwen3EmbeddingTokenizer)
    assert tokenizer_default._model_variant == "4b"
    # Check it has the correct vocab size for 4B/8B
    assert tokenizer_default.vocab_size == 151692


def test_missing_tokens_handling():
    """Test that missing tokens are handled correctly in 4B/8B models."""
    tokenizer = Qwen3EmbeddingTokenizer(model_variant="4b")

    # These tokens should not be in the vocab for 4B/8B models
    # Trying to tokenize them should just tokenize as regular text
    text_with_missing = "<think>This is a test</think>"
    tokens = tokenizer.tokenize_unsafe(text_with_missing)

    # The special tokens should be tokenized as regular text, not as special tokens
    decoded = tokenizer.detokenize(tokens)
    assert decoded == text_with_missing + "<|endoftext|>"  # EOS is auto-added

    # Verify the tokens don't include the special token IDs
    assert 151667 not in tokens  # <think> ID
    assert 151668 not in tokens  # </think> ID


def test_embedding_tokenizer_post_processing():
    """Test that embedding tokenizers correctly implement post-processing."""
    # Test 0.6B tokenizer
    tokenizer_06b = Qwen3EmbeddingTokenizer(model_variant="0.6b")

    # Single text
    tokens1 = tokenizer_06b.tokenize_safe("Hello")
    assert tokens1[-1] == 151643  # Should end with <|endoftext|>

    # Empty text should not add EOS
    tokens_empty = tokenizer_06b.tokenize_safe("")
    assert len(tokens_empty) == 0

    # Test 4B/8B tokenizer
    tokenizer_4b8b = Qwen3EmbeddingTokenizer(model_variant="4b")

    # Single text
    tokens2 = tokenizer_4b8b.tokenize_safe("World")
    assert tokens2[-1] == 151643  # Should end with <|endoftext|>

    # Compare with base tokenizer (should NOT add EOS)
    base_tokenizer = Qwen3Tokenizer()
    base_tokens = base_tokenizer.tokenize_safe("Hello")
    assert base_tokens[-1] != 151643  # Base tokenizer should NOT add EOS


def test_embedding_tokenizer_strip_eos():
    """Test the strip_eos_token helper method."""
    tokenizer = Qwen3EmbeddingTokenizer(model_variant="0.6b")

    # Tokenize with auto-added EOS
    text = "Hello, world!"
    tokens = tokenizer.tokenize_safe(text)
    assert tokens[-1] == 151643  # Has EOS

    # Detokenize includes EOS
    decoded_with_eos = tokenizer.detokenize(tokens)
    assert decoded_with_eos == "Hello, world!<|endoftext|>"

    # Strip EOS and detokenize
    tokens_no_eos = tokenizer.strip_eos_token(tokens)
    decoded_no_eos = tokenizer.detokenize(tokens_no_eos)
    assert decoded_no_eos == "Hello, world!"

    # Test that strip_eos_token is idempotent
    tokens_no_eos2 = tokenizer.strip_eos_token(tokens_no_eos)
    assert tokens_no_eos2 == tokens_no_eos

    # Test with tokens that don't have EOS
    tokens_manual = [9707, 11, 1879, 0]  # "Hello, world!" without EOS
    tokens_unchanged = tokenizer.strip_eos_token(tokens_manual)
    assert tokens_unchanged == tokens_manual


def test_embedding_tokenizer_edge_cases():
    """Test edge cases for embedding tokenizers."""
    tokenizer_06b = Qwen3EmbeddingTokenizer(model_variant="0.6b")
    tokenizer_4b8b = Qwen3EmbeddingTokenizer(model_variant="4b")

    # Test empty string
    empty_tokens = tokenizer_06b.tokenize_safe("")
    assert empty_tokens == []
    assert tokenizer_06b.detokenize(empty_tokens) == ""

    # Test whitespace only
    space_tokens = tokenizer_06b.tokenize_safe("   ")
    assert space_tokens[-1] == 151643  # Should still add EOS

    # Test newlines
    newline_tokens = tokenizer_06b.tokenize_safe("\n\n")
    assert newline_tokens[-1] == 151643

    # Test very long text
    long_text = "Hello " * 1000
    long_tokens = tokenizer_4b8b.tokenize_safe(long_text)
    assert long_tokens[-1] == 151643
    decoded = tokenizer_4b8b.detokenize(long_tokens)
    assert decoded.endswith("<|endoftext|>")

    # Test special characters
    special_text = "Hello 👋 世界 🌍"
    special_tokens = tokenizer_06b.tokenize_unsafe(special_text)
    assert special_tokens[-1] == 151643
    decoded_special = tokenizer_06b.detokenize(special_tokens)
    assert decoded_special.startswith("Hello 👋 世界 🌍")


def test_embedding_tokenizer_consistency():
    """Test consistency between 0.6B and 4B/8B tokenizers for common text."""
    tokenizer_06b = Qwen3EmbeddingTokenizer(model_variant="0.6b")
    tokenizer_4b8b = Qwen3EmbeddingTokenizer(model_variant="4b")

    test_texts = [
        "Hello, world!",
        "def hello():\n    print('world')",
        "The quick brown fox jumps over the lazy dog.",
        "12345 + 67890 = 80235",
        "<EMAIL>",
        "https://www.example.com/path?query=value",
    ]

    for text in test_texts:
        # Both should produce same tokens (except for the 4 missing special tokens)
        tokens_06b = tokenizer_06b.tokenize_safe(text)
        tokens_4b8b = tokenizer_4b8b.tokenize_safe(text)

        # Should have same length
        assert len(tokens_06b) == len(tokens_4b8b), f"Length mismatch for: {text}"

        # Should end with EOS
        assert tokens_06b[-1] == 151643
        assert tokens_4b8b[-1] == 151643

        # Detokenization should be identical
        decoded_06b = tokenizer_06b.detokenize(tokens_06b)
        decoded_4b8b = tokenizer_4b8b.detokenize(tokens_4b8b)
        assert decoded_06b == decoded_4b8b, f"Decode mismatch for: {text}"


def test_embedding_tokenizer_special_tokens_handling():
    """Test how embedding tokenizers handle various special tokens."""
    tokenizer_06b = Qwen3EmbeddingTokenizer(model_variant="0.6b")
    tokenizer_4b8b = Qwen3EmbeddingTokenizer(model_variant="4b")

    # Test that 0.6B can tokenize think/tool_response as special tokens
    text_with_think = "Let me <think>consider this</think> carefully"
    tokens_06b = tokenizer_06b.tokenize_unsafe(text_with_think)
    # Should contain the think token IDs
    assert 151667 in tokens_06b  # <think>
    assert 151668 in tokens_06b  # </think>

    # Test that 4B/8B tokenizes them as regular text
    tokens_4b8b = tokenizer_4b8b.tokenize_unsafe(text_with_think)
    # Should NOT contain the think token IDs
    assert 151667 not in tokens_4b8b
    assert 151668 not in tokens_4b8b

    # Test common special tokens work in both
    text_with_im = "<|im_start|>user\nHello<|im_end|>"
    tokens_06b_im = tokenizer_06b.tokenize_unsafe(text_with_im)
    tokens_4b8b_im = tokenizer_4b8b.tokenize_unsafe(text_with_im)

    # Both should have im_start and im_end
    assert 151644 in tokens_06b_im  # <|im_start|>
    assert 151645 in tokens_06b_im  # <|im_end|>
    assert 151644 in tokens_4b8b_im
    assert 151645 in tokens_4b8b_im


def test_embedding_tokenizer_multiple_eos():
    """Test handling of multiple EOS tokens."""
    tokenizer = Qwen3EmbeddingTokenizer(model_variant="0.6b")

    # Text already ending with EOS
    text_with_eos = "Hello<|endoftext|>"
    tokens = tokenizer.tokenize_unsafe(text_with_eos)

    # Should not duplicate EOS
    assert tokens[-1] == 151643
    assert tokens[-2] != 151643

    # Text with EOS in the middle
    text_mid_eos = "Hello<|endoftext|>World"
    tokens_mid = tokenizer.tokenize_unsafe(text_mid_eos)

    # Should have EOS in middle and at end
    eos_positions = [i for i, t in enumerate(tokens_mid) if t == 151643]
    assert len(eos_positions) == 2
    assert eos_positions[-1] == len(tokens_mid) - 1  # Last position


def test_embedding_tokenizer_with_offsets():
    """Test detokenize_with_offsets for embedding tokenizers."""
    tokenizer = Qwen3EmbeddingTokenizer(model_variant="0.6b")

    text = "Hello, world!"
    tokens = tokenizer.tokenize_safe(text)

    # Get text with offsets
    decoded, offsets = tokenizer.detokenize_with_offsets(tokens)

    # Should include EOS in decoded text
    assert decoded == "Hello, world!<|endoftext|>"

    # Offsets should be monotonically increasing
    assert all(offsets[i] <= offsets[i + 1] for i in range(len(offsets) - 1))

    # First offset should be 0
    assert offsets[0] == 0

    # Strip EOS and check offsets
    tokens_no_eos = tokenizer.strip_eos_token(tokens)
    decoded_no_eos, offsets_no_eos = tokenizer.detokenize_with_offsets(tokens_no_eos)
    assert decoded_no_eos == "Hello, world!"
    assert len(offsets_no_eos) == len(tokens_no_eos)


def test_unified_qwen3_embedding_tokenizer():
    """Test the new unified Qwen3EmbeddingTokenizer class."""

    # Test 0.6B variant
    tokenizer_06b = Qwen3EmbeddingTokenizer(model_variant="0.6b")
    assert tokenizer_06b._model_variant == "0.6b"
    assert isinstance(tokenizer_06b._special_tokens, Qwen3SpecialTokens)
    assert tokenizer_06b.vocab_size == 151696  # 0.6B has all tokens

    # Test 4B variant
    tokenizer_4b = Qwen3EmbeddingTokenizer(model_variant="4b")
    assert tokenizer_4b._model_variant == "4b"
    assert isinstance(tokenizer_4b._special_tokens, Qwen3EmbeddingSpecialTokens)
    assert tokenizer_4b.vocab_size == 151692  # 4B/8B missing 4 tokens

    # Test 8B variant
    tokenizer_8b = Qwen3EmbeddingTokenizer(model_variant="8b")
    assert tokenizer_8b._model_variant == "8b"
    assert isinstance(tokenizer_8b._special_tokens, Qwen3EmbeddingSpecialTokens)
    assert tokenizer_8b.vocab_size == 151692  # 4B/8B missing 4 tokens

    # Test default variant (should be 4b)
    tokenizer_default = Qwen3EmbeddingTokenizer()
    assert tokenizer_default._model_variant == "4b"

    # Test invalid variant
    try:
        Qwen3EmbeddingTokenizer(model_variant="invalid")
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert "Unknown model variant" in str(e)

    # Test tokenization consistency
    text = "Hello, world!"
    tokens_06b = tokenizer_06b.tokenize_safe(text)
    tokens_4b = tokenizer_4b.tokenize_safe(text)

    # Both should add EOS
    assert tokens_06b[-1] == 151643
    assert tokens_4b[-1] == 151643

    # Test strip_eos_token
    tokens_no_eos = tokenizer_06b.strip_eos_token(tokens_06b)
    assert tokens_no_eos[-1] != 151643
    assert tokenizer_06b.detokenize(tokens_no_eos) == "Hello, world!"
