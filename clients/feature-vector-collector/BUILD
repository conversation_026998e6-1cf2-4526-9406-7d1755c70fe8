load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

SRC_FILES = glob(
    [
        "src/**/*.ts",
        "src/*.ts",
        "src/*.js",
    ],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
)

LIB_DEPS = [
    ":node_modules/@types/node",
    ":node_modules/node-machine-id",
    ":node_modules/systeminformation",
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = [
        "//clients:__subpackages__",
    ],
    deps = [
        "//clients:tsconfig",
    ],
)

ts_project(
    name = "ts",
    srcs = SRC_FILES + LIB_DEPS,
    # The declaration is needed so type information is accessible by
    # other ts_projects.
    declaration = True,
    out_dir = "out",
    source_map = True,
    tsconfig = ":tsconfig",
    visibility = [
        "//clients:__subpackages__",
    ],
    deps = [
        ":node_modules",
    ],
)

filegroup(
    name = "definitions",
    srcs = [
        "src/feature-vector-collector-obfuscated.d.ts",
    ],
    visibility = [
        "//clients:__subpackages__",
    ],
)

# This should not be used unless absolutely necessary.
js_library(
    name = "src",
    srcs = SRC_FILES + LIB_DEPS,
    data = [
        ":definitions",
    ],
    visibility = [
        # Jest & ESlint doesn't seem to use the .d.ts definitions from the :ts
        # target, so it needs to use the source files directly.
        "//clients/vscode:__pkg__",
    ],
    deps = [],
)
