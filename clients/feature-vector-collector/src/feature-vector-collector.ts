import * as os from "os";
import * as crypto from "crypto";
import * as fs from "fs";
import * as path from "path";
import * as process from "process";
import { exec } from "child_process";
import { promisify } from "util";
import { machineIdSync } from "node-machine-id";
import * as si from "systeminformation";

const execAsync = promisify(exec);

function sha256(input: Uint8Array): string {
  const hash = crypto.createHash("sha256");
  hash.update(input);
  return hash.digest("hex");
}

type vscodeCollector = {
  version: string;
  env: {
    machineId: string;
  };
};

export type FeatureVector = Record<number, string>;

enum FeatureVectorKey {
  vscode = 0,
  machineId = 1,
  os = 2,
  cpu = 3,
  memory = 4,
  numCpus = 5,
  hostname = 6,
  arch = 7,
  username = 8,
  macAddresses = 9,
  osRelease = 10,
  kernelVersion = 11,
  checksum = 12,
  telemetryDevDeviceId = 13,
  requestId = 14,
  randomHash = 15,
  osMachineId = 16,
  homeDirectoryIno = 17,
  projectRootIno = 18,
  gitUserEmail = 19,
  sshPublicKey = 20,
  userDataPathIno = 21,
  userDataMachineId = 22,
  storageUriPath = 23,
  gpuInfo = 24,
  timezone = 25,
  diskLayout = 26,
  systemInfo = 27,
  biosInfo = 28,
  baseboardInfo = 29,
  chassisInfo = 30,
  baseboardAssetTag = 31,
  chassisAssetTag = 32,
  cpuFlags = 33,
  memoryModuleSerials = 34,
  usbDeviceIds = 35,
  audioDeviceIds = 36,
  hypervisorType = 37,
  systemBootTime = 38,
  sshKnownHosts = 39,
}

export class Features {
  private _textEncoder = new TextEncoder();
  constructor(
    public readonly vscode: string,
    public readonly machineId: string,
    public readonly os: string,
    public readonly cpu: string,
    public readonly memory: string,
    public readonly numCpus: string,
    public readonly hostname: string,
    public readonly arch: string,
    public readonly username: string,
    public readonly macAddresses: string[],
    public readonly osRelease: string,
    public readonly kernelVersion: string,
    public readonly telemetryDevDeviceId: string,
    public readonly requestId: string,
    public readonly randomHash: string,
    public readonly osMachineId: string,
    public readonly homeDirectoryIno: string,
    public readonly projectRootIno: string,
    public readonly gitUserEmail: string,
    public readonly sshPublicKey: string,
    public readonly userDataPathIno: string,
    public readonly userDataMachineId: string,
    public readonly storageUriPath: string,
    public readonly gpuInfo: string,
    public readonly timezone: string,
    public readonly diskLayout: string,
    public readonly systemInfo: string,
    public readonly biosInfo: string,
    public readonly baseboardInfo: string,
    public readonly chassisInfo: string,
    public readonly baseboardAssetTag: string,
    public readonly chassisAssetTag: string,
    public readonly cpuFlags: string,
    public readonly memoryModuleSerials: string,
    public readonly usbDeviceIds: string,
    public readonly audioDeviceIds: string,
    public readonly hypervisorType: string,
    public readonly systemBootTime: number,
    public readonly sshKnownHosts: string,
  ) {}

  calculateChecksum(vector: FeatureVector) {
    const sortedKeys = Object.keys(vector).sort();
    const values = sortedKeys.map((key) => vector[Number(key)]);
    const combined = values.join("");
    const hash = sha256(this._textEncoder.encode(combined));

    return "v1#" + hash;
  }

  canonicalize(s: string): string {
    return sha256(this._textEncoder.encode(s.toLowerCase().trim()));
  }

  canonicalizeArray(array: string[]): string {
    return this.canonicalize(
      array.map((s) => s.toLowerCase().trim()).join(","),
    );
  }

  toVector() {
    const result: FeatureVector = {
      [FeatureVectorKey.vscode]: this.canonicalize(this.vscode),
      [FeatureVectorKey.machineId]: this.canonicalize(this.machineId),
      [FeatureVectorKey.os]: this.canonicalize(this.os),
      [FeatureVectorKey.cpu]: this.canonicalize(this.cpu),
      [FeatureVectorKey.memory]: this.canonicalize(this.memory),
      [FeatureVectorKey.numCpus]: this.canonicalize(this.numCpus),
      [FeatureVectorKey.hostname]: this.canonicalize(this.hostname),
      [FeatureVectorKey.arch]: this.canonicalize(this.arch),
      [FeatureVectorKey.username]: this.canonicalize(this.username),
      [FeatureVectorKey.macAddresses]: this.canonicalizeArray(
        this.macAddresses,
      ),
      [FeatureVectorKey.osRelease]: this.canonicalize(this.osRelease),
      [FeatureVectorKey.kernelVersion]: this.canonicalize(this.kernelVersion),
      [FeatureVectorKey.telemetryDevDeviceId]: this.canonicalize(
        this.telemetryDevDeviceId,
      ),
      [FeatureVectorKey.requestId]: this.canonicalize(this.requestId),
      [FeatureVectorKey.randomHash]: this.canonicalize(this.randomHash),
      [FeatureVectorKey.osMachineId]: this.canonicalize(this.osMachineId),
      [FeatureVectorKey.homeDirectoryIno]: this.canonicalize(
        this.homeDirectoryIno,
      ),
      [FeatureVectorKey.projectRootIno]: this.canonicalize(this.projectRootIno),
      [FeatureVectorKey.gitUserEmail]: this.canonicalize(this.gitUserEmail),
      [FeatureVectorKey.sshPublicKey]: this.canonicalize(this.sshPublicKey),
      [FeatureVectorKey.userDataPathIno]: this.canonicalize(
        this.userDataPathIno,
      ),
      [FeatureVectorKey.userDataMachineId]: this.canonicalize(
        this.userDataMachineId,
      ),
      [FeatureVectorKey.storageUriPath]: this.canonicalize(this.storageUriPath),
      [FeatureVectorKey.gpuInfo]: this.canonicalize(this.gpuInfo),
      [FeatureVectorKey.timezone]: this.canonicalize(this.timezone),
      [FeatureVectorKey.diskLayout]: this.canonicalize(this.diskLayout),
      [FeatureVectorKey.systemInfo]: this.canonicalize(this.systemInfo),
      [FeatureVectorKey.biosInfo]: this.canonicalize(this.biosInfo),
      [FeatureVectorKey.baseboardInfo]: this.canonicalize(this.baseboardInfo),
      [FeatureVectorKey.chassisInfo]: this.canonicalize(this.chassisInfo),
      [FeatureVectorKey.baseboardAssetTag]: this.canonicalize(
        this.baseboardAssetTag,
      ),
      [FeatureVectorKey.chassisAssetTag]: this.canonicalize(
        this.chassisAssetTag,
      ),
      [FeatureVectorKey.cpuFlags]: this.canonicalize(this.cpuFlags),
      [FeatureVectorKey.memoryModuleSerials]: this.canonicalize(
        this.memoryModuleSerials,
      ),
      [FeatureVectorKey.usbDeviceIds]: this.canonicalize(this.usbDeviceIds),
      [FeatureVectorKey.audioDeviceIds]: this.canonicalize(this.audioDeviceIds),
      [FeatureVectorKey.hypervisorType]: this.canonicalize(this.hypervisorType),
      [FeatureVectorKey.systemBootTime]: this.canonicalize(
        this.systemBootTime.toString(),
      ),
      [FeatureVectorKey.sshKnownHosts]: this.canonicalize(this.sshKnownHosts),
    };
    result[FeatureVectorKey.checksum] = this.calculateChecksum(result);
    return result;
  }
}

function getExternalMacAddresses(): string[] {
  const interfaces = os.networkInterfaces();
  const externalMacs: string[] = [];
  for (const iface in interfaces) {
    const ifaceInfos = interfaces[iface];
    if (!ifaceInfos) {
      continue;
    }
    if (ifaceInfos.length === 0) {
      continue;
    }
    if (ifaceInfos[0].internal) {
      continue;
    }
    for (const info of ifaceInfos) {
      externalMacs.push(info.mac);
    }
  }
  // Sort the external mac addresses to make the fingerprint stable
  externalMacs.sort();
  return externalMacs;
}

function generateRandomHash(): string {
  return crypto.randomBytes(16).toString("hex");
}

function getInodeNumber(dirPath: string): string {
  try {
    const stats = fs.lstatSync(dirPath);
    // Exclude special inode values:
    // - 0: Reserved/invalid inode number in most filesystems
    // - May indicate virtual filesystems, network filesystem issues, or errors
    if (stats.ino === 0) {
      return "";
    }
    return stats.ino.toString();
  } catch (e) {
    return "";
  }
}

async function getGitUserEmail(): Promise<string> {
  try {
    // Try local git config first
    try {
      const { stdout } = await execAsync("git config user.email", {
        timeout: 2000,
      });
      return stdout.trim();
    } catch {
      // If local user.email is not set, try global
      try {
        const { stdout } = await execAsync("git config --global user.email", {
          timeout: 2000,
        });
        return stdout.trim();
      } catch {
        return "";
      }
    }
  } catch (e) {
    return "";
  }
}

function getSshPublicKey(): string {
  const homeDir = os.homedir();
  const keyPaths = [
    path.join(homeDir, ".ssh", "id_rsa.pub"),
    path.join(homeDir, ".ssh", "id_ed25519.pub"),
    path.join(homeDir, ".ssh", "id_ecdsa.pub"),
  ];

  for (const keyPath of keyPaths) {
    try {
      const publicKey = fs.readFileSync(keyPath, "utf8");
      return publicKey.trim();
    } catch {
      // Continue to next key type
    }
  }
  return "";
}

async function getPreciseBootTime(): Promise<number> {
  try {
    if (process.platform === "linux") {
      // Read /proc/stat for btime
      const stat = fs.readFileSync("/proc/stat", "utf8");
      const btimeMatch = stat.match(/btime (\d+)/);
      if (btimeMatch) {
        return parseInt(btimeMatch[1]) * 1000; // Convert to milliseconds
      }
    } else if (process.platform === "win32") {
      // Use PowerShell to get precise boot time
      const { stdout } = await execAsync(
        'powershell "Get-CimInstance Win32_OperatingSystem | Select-Object -ExpandProperty LastBootUpTime"',
        { timeout: 5000 },
      );
      return new Date(stdout.trim()).getTime();
    } else if (process.platform === "darwin") {
      // Use sysctl on macOS
      const { stdout } = await execAsync("sysctl -n kern.boottime", {
        timeout: 5000,
      });
      // Parse: { sec = 1234567890, usec = 123456 }
      const match = stdout.match(/sec = (\d+), usec = (\d+)/);
      if (match) {
        const seconds = parseInt(match[1]);
        const microseconds = parseInt(match[2]);
        return seconds * 1000 + Math.floor(microseconds / 1000); // Convert to milliseconds
      }
    }

    // Fallback to systeminformation
    const timeData = si.time();
    return Date.now() - timeData.uptime * 1000;
  } catch (e) {
    return 0;
  }
}

function getSshKnownHosts(): string {
  try {
    const homeDir = os.homedir();
    const knownHostsPath = path.join(homeDir, ".ssh", "known_hosts");

    if (fs.existsSync(knownHostsPath)) {
      const knownHosts = fs.readFileSync(knownHostsPath, "utf8");
      // Get last few entries (most recent connections)
      const lines = knownHosts
        .trim()
        .split("\n")
        .filter((line) => line.trim() && !line.startsWith("#"));
      // Take last 5 entries to avoid huge data but capture recent activity
      const recentHosts = lines.slice(-5).join(",");
      return recentHosts;
    }
    return "";
  } catch (e) {
    return "";
  }
}

function getTelemetryDevDeviceId(userDataPath?: string): string {
  try {
    if (!userDataPath) {
      return "";
    }

    const globalStoragePath = path.join(
      userDataPath,
      "User",
      "globalStorage",
      "storage.json",
    );

    if (fs.existsSync(globalStoragePath)) {
      const globalStorage = JSON.parse(
        fs.readFileSync(globalStoragePath, "utf8"),
      );
      return globalStorage["telemetry.devDeviceId"] || "";
    }

    return "";
  } catch (e) {
    return "";
  }
}

function getUserDataMachineId(userDataPath?: string): string {
  try {
    if (!userDataPath) {
      return "";
    }

    const machineIdPath = path.join(userDataPath, "machineid");

    if (fs.existsSync(machineIdPath)) {
      return fs.readFileSync(machineIdPath, "utf8").trim();
    }

    return "";
  } catch (e) {
    return "";
  }
}

async function getSystemInformation(): Promise<{
  gpuInfo: string;
  timezone: string;
  diskLayout: string;
  systemInfo: string;
  biosInfo: string;
  baseboardInfo: string;
  chassisInfo: string;
  baseboardAssetTag: string;
  chassisAssetTag: string;
  cpuFlags: string;
  memoryModuleSerials: string;
  usbDeviceIds: string;
  audioDeviceIds: string;
  hypervisorType: string;
  systemBootTime: number;
  sshKnownHosts: string;
}> {
  try {
    // Get synchronous time data
    const timeData = si.time();

    // Get async data from systeminformation with proper error handling
    const [
      graphicsData,
      diskData,
      systemData,
      biosData,
      baseboardData,
      chassisData,
      cpuData,
      memLayoutData,
      usbData,
      audioData,
    ] = await Promise.allSettled([
      si.graphics(),
      si.diskLayout(),
      si.system(),
      si.bios(),
      si.baseboard(),
      si.chassis(),
      si.cpu(),
      si.memLayout(),
      si.usb(),
      si.audio(),
    ]);

    // Extract GPU info using proper types
    let gpuInfo = "";
    if (
      graphicsData.status === "fulfilled" &&
      graphicsData.value.controllers.length > 0
    ) {
      gpuInfo = JSON.stringify(
        graphicsData.value.controllers.map((gpu) => ({
          model: gpu.model || "",
          vendor: gpu.vendor || "",
          vram: gpu.vram || 0,
          bus: gpu.bus || "",
        })),
      );
    }

    // Get timezone from synchronous time data
    const timezone = timeData.timezone || "";

    // Get disk layout info using proper types
    let diskLayoutInfo = "";
    if (diskData.status === "fulfilled" && diskData.value.length > 0) {
      diskLayoutInfo = JSON.stringify(
        diskData.value.map((disk) => ({
          device: disk.device || "",
          type: disk.type || "",
          name: disk.name || "",
          size: disk.size || 0,
          interfaceType: disk.interfaceType || "",
        })),
      );
    }

    // Get system info (includes UUID which is what we want for hardware identification)
    let systemInfo = "";
    if (systemData.status === "fulfilled") {
      const data = systemData.value;
      systemInfo = JSON.stringify({
        manufacturer: data.manufacturer || "",
        model: data.model || "",
        version: data.version || "",
        serial: data.serial || "",
        uuid: data.uuid || "", // This is the SMBIOS System UUID we want
        sku: data.sku || "",
      });
    }

    // Get BIOS info using proper types
    let biosInfo = "";
    if (biosData.status === "fulfilled") {
      const data = biosData.value;
      biosInfo = JSON.stringify({
        vendor: data.vendor || "",
        version: data.version || "",
        releaseDate: data.releaseDate || "",
        revision: data.revision || "",
      });
    }

    // Get baseboard info using proper types
    let baseboardInfo = "";
    let baseboardAssetTag = "";
    if (baseboardData.status === "fulfilled") {
      const data = baseboardData.value;
      baseboardInfo = JSON.stringify({
        manufacturer: data.manufacturer || "",
        model: data.model || "",
        version: data.version || "",
        serial: data.serial || "",
      });
      baseboardAssetTag = data.assetTag || "";
    }

    // Get chassis info using proper types
    let chassisInfo = "";
    let chassisAssetTag = "";
    if (chassisData.status === "fulfilled") {
      const data = chassisData.value;
      chassisInfo = JSON.stringify({
        manufacturer: data.manufacturer || "",
        model: data.model || "",
        type: data.type || "",
        version: data.version || "",
        serial: data.serial || "",
        assetTag: data.assetTag || "",
      });
      chassisAssetTag = data.assetTag || "";
    }

    // Extract CPU flags
    let cpuFlags = "";
    if (cpuData.status === "fulfilled") {
      cpuFlags = cpuData.value.flags || "";
    }

    // Extract memory module serials
    let memoryModuleSerials = "";
    if (
      memLayoutData.status === "fulfilled" &&
      memLayoutData.value.length > 0
    ) {
      const serials = memLayoutData.value
        .map((mem) => mem.serialNum || "")
        .filter((serial) => serial !== "")
        .join(",");
      memoryModuleSerials = serials;
    }

    // Extract USB device IDs
    let usbDeviceIds = "";
    if (usbData.status === "fulfilled" && usbData.value.length > 0) {
      const deviceIds = usbData.value
        .map((usb) => `${usb.vendor || ""}:${usb.id || ""}`)
        .filter((id) => id !== ":")
        .join(",");
      usbDeviceIds = deviceIds;
    }

    // Extract audio device IDs
    let audioDeviceIds = "";
    if (audioData.status === "fulfilled" && audioData.value.length > 0) {
      const audioIds = audioData.value
        .map((audio) => `${audio.manufacturer || ""}:${audio.name || ""}`)
        .filter((id) => id !== ":")
        .join(",");
      audioDeviceIds = audioIds;
    }

    // Extract hypervisor type
    let hypervisorType = "";
    if (systemData.status === "fulfilled") {
      hypervisorType = systemData.value.virtualHost || "";
    }

    // Get precise system boot time and SSH known hosts concurrently
    const [systemBootTime, sshKnownHosts] = await Promise.all([
      getPreciseBootTime(),
      Promise.resolve(getSshKnownHosts()), // Wrap sync function in Promise.resolve for consistency
    ]);

    return {
      gpuInfo,
      timezone,
      diskLayout: diskLayoutInfo,
      systemInfo,
      biosInfo,
      baseboardInfo,
      chassisInfo,
      baseboardAssetTag,
      chassisAssetTag,
      cpuFlags,
      memoryModuleSerials,
      usbDeviceIds,
      audioDeviceIds,
      hypervisorType,
      systemBootTime,
      sshKnownHosts,
    };
  } catch (e) {
    // Return empty strings if systeminformation fails
    return {
      gpuInfo: "",
      timezone: "",
      diskLayout: "",
      systemInfo: "",
      biosInfo: "",
      baseboardInfo: "",
      chassisInfo: "",
      baseboardAssetTag: "",
      chassisAssetTag: "",
      cpuFlags: "",
      memoryModuleSerials: "",
      usbDeviceIds: "",
      audioDeviceIds: "",
      hypervisorType: "",
      systemBootTime: 0,
      sshKnownHosts: "",
    };
  }
}

export async function createFeatures(
  vscode: vscodeCollector,
  extensionContext: {
    globalStorageUri?: { fsPath: string };
    storageUri?: { fsPath: string };
  },
): Promise<Features> {
  const cpus = os.cpus();
  let username = "";
  try {
    username = os.userInfo().username;
  } catch (e) {
    // Ignore
  }
  const vscodeVersion = vscode.version;

  // Generate a request ID
  const requestId = crypto.randomUUID();

  // Generate a random hash
  const randomHash = generateRandomHash();

  // Get OS machine ID from node-machine-id
  let osMachineId = "";
  try {
    osMachineId = machineIdSync();
  } catch (e) {
    // Fallback to empty string if we can't get the machine ID
    osMachineId = "";
  }

  // Get home directory inode
  const homeDir = os.homedir();
  const homeDirectoryIno = getInodeNumber(homeDir);

  // Get project root inode - try current working directory as fallback
  const projectRootIno = getInodeNumber(process.cwd());

  // Derive user data path from global storage path (THREE levels up from globalStorage)
  const globalStoragePath = extensionContext.globalStorageUri?.fsPath;
  const userDataPath = globalStoragePath
    ? path.join(globalStoragePath, "..", "..", "..")
    : "";

  // Get telemetry devDeviceId from VSCode global storage
  const telemetryDevDeviceId = getTelemetryDevDeviceId(userDataPath);

  // Get VSCode user data path inode and machine ID
  const userDataPathIno = getInodeNumber(userDataPath);
  const userDataMachineId = getUserDataMachineId(userDataPath);

  // Get storage URI path
  const storageUriPath = extensionContext.storageUri?.fsPath || "";

  // Get SSH public key (sync, fast)
  const sshPublicKey = getSshPublicKey();

  // Run all async operations concurrently
  const [gitUserEmail, sysInfo] = await Promise.all([
    getGitUserEmail(),
    getSystemInformation(),
  ]);

  return new Features(
    vscodeVersion,
    vscode.env.machineId,
    os.type(),
    cpus[0].model,
    os.totalmem().toString(),
    cpus.length.toString(),
    os.hostname(),
    os.machine(),
    username,
    getExternalMacAddresses(),
    os.release(),
    os.version(),
    telemetryDevDeviceId,
    requestId,
    randomHash,
    osMachineId,
    homeDirectoryIno,
    projectRootIno,
    gitUserEmail,
    sshPublicKey,
    userDataPathIno,
    userDataMachineId,
    storageUriPath,
    sysInfo.gpuInfo,
    sysInfo.timezone,
    sysInfo.diskLayout,
    sysInfo.systemInfo,
    sysInfo.biosInfo,
    sysInfo.baseboardInfo,
    sysInfo.chassisInfo,
    sysInfo.baseboardAssetTag,
    sysInfo.chassisAssetTag,
    sysInfo.cpuFlags,
    sysInfo.memoryModuleSerials,
    sysInfo.usbDeviceIds,
    sysInfo.audioDeviceIds,
    sysInfo.hypervisorType,
    sysInfo.systemBootTime,
    sysInfo.sshKnownHosts,
  );
}
