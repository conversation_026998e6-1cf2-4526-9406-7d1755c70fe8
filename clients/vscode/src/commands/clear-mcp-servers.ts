import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentExtension } from "../extension";
import { DebugCommand } from "./utils/debug-command";

// Clear stored MCP servers
export class ClearMCPServersCommand extends DebugCommand {
    public static readonly commandID = "vscode-augment.clearMCPServers";

    constructor(
        private _extension: AugmentExtension,
        configListener: AugmentConfigListener
    ) {
        super(configListener, "Clear Stored MCP Servers");
    }

    async run() {
        await this._extension.toolConfigStore?.saveMCPServers([]);
    }
}
