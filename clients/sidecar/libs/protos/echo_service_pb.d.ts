// @generated by protoc-gen-es v2.3.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/libs/protos/echo_service.proto (package com.augmentcode.transport.grpc.examples, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file clients/sidecar/libs/protos/echo_service.proto.
 */
export declare const file_clients_sidecar_libs_protos_echo_service: GenFile;

/**
 * @generated from message com.augmentcode.transport.grpc.examples.EchoRequest
 */
export declare type EchoRequest = Message<"com.augmentcode.transport.grpc.examples.EchoRequest"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * @generated from field: int64 timestamp = 2;
   */
  timestamp: bigint;

  /**
   * @generated from field: optional string prefix = 3;
   */
  prefix?: string;
};

/**
 * Describes the message com.augmentcode.transport.grpc.examples.EchoRequest.
 * Use `create(EchoRequestSchema)` to create a new message.
 */
export declare const EchoRequestSchema: GenMessage<EchoRequest>;

/**
 * @generated from message com.augmentcode.transport.grpc.examples.EchoResponse
 */
export declare type EchoResponse = Message<"com.augmentcode.transport.grpc.examples.EchoResponse"> & {
  /**
   * @generated from field: string echoed_message = 1;
   */
  echoedMessage: string;

  /**
   * @generated from field: int64 request_timestamp = 2;
   */
  requestTimestamp: bigint;

  /**
   * @generated from field: int64 response_timestamp = 3;
   */
  responseTimestamp: bigint;

  /**
   * @generated from field: string server_info = 4;
   */
  serverInfo: string;
};

/**
 * Describes the message com.augmentcode.transport.grpc.examples.EchoResponse.
 * Use `create(EchoResponseSchema)` to create a new message.
 */
export declare const EchoResponseSchema: GenMessage<EchoResponse>;

/**
 * @generated from message com.augmentcode.transport.grpc.examples.MathRequest
 */
export declare type MathRequest = Message<"com.augmentcode.transport.grpc.examples.MathRequest"> & {
  /**
   * @generated from field: int32 a = 1;
   */
  a: number;

  /**
   * @generated from field: int32 b = 2;
   */
  b: number;
};

/**
 * Describes the message com.augmentcode.transport.grpc.examples.MathRequest.
 * Use `create(MathRequestSchema)` to create a new message.
 */
export declare const MathRequestSchema: GenMessage<MathRequest>;

/**
 * @generated from message com.augmentcode.transport.grpc.examples.FactorialRequest
 */
export declare type FactorialRequest = Message<"com.augmentcode.transport.grpc.examples.FactorialRequest"> & {
  /**
   * @generated from field: int32 n = 1;
   */
  n: number;
};

/**
 * Describes the message com.augmentcode.transport.grpc.examples.FactorialRequest.
 * Use `create(FactorialRequestSchema)` to create a new message.
 */
export declare const FactorialRequestSchema: GenMessage<FactorialRequest>;

/**
 * @generated from message com.augmentcode.transport.grpc.examples.MathResponse
 */
export declare type MathResponse = Message<"com.augmentcode.transport.grpc.examples.MathResponse"> & {
  /**
   * @generated from field: int64 result = 1;
   */
  result: bigint;

  /**
   * @generated from field: string operation = 2;
   */
  operation: string;
};

/**
 * Describes the message com.augmentcode.transport.grpc.examples.MathResponse.
 * Use `create(MathResponseSchema)` to create a new message.
 */
export declare const MathResponseSchema: GenMessage<MathResponse>;

/**
 * @generated from service com.augmentcode.transport.grpc.examples.EchoService
 */
export declare const EchoService: GenService<{
  /**
   * @generated from rpc com.augmentcode.transport.grpc.examples.EchoService.Echo
   */
  echo: {
    methodKind: "unary";
    input: typeof EchoRequestSchema;
    output: typeof EchoResponseSchema;
  },
  /**
   * @generated from rpc com.augmentcode.transport.grpc.examples.EchoService.Uppercase
   */
  uppercase: {
    methodKind: "unary";
    input: typeof EchoRequestSchema;
    output: typeof EchoResponseSchema;
  },
  /**
   * @generated from rpc com.augmentcode.transport.grpc.examples.EchoService.Reverse
   */
  reverse: {
    methodKind: "unary";
    input: typeof EchoRequestSchema;
    output: typeof EchoResponseSchema;
  },
}>;

/**
 * @generated from service com.augmentcode.transport.grpc.examples.MathService
 */
export declare const MathService: GenService<{
  /**
   * @generated from rpc com.augmentcode.transport.grpc.examples.MathService.Add
   */
  add: {
    methodKind: "unary";
    input: typeof MathRequestSchema;
    output: typeof MathResponseSchema;
  },
  /**
   * @generated from rpc com.augmentcode.transport.grpc.examples.MathService.Multiply
   */
  multiply: {
    methodKind: "unary";
    input: typeof MathRequestSchema;
    output: typeof MathResponseSchema;
  },
  /**
   * @generated from rpc com.augmentcode.transport.grpc.examples.MathService.Factorial
   */
  factorial: {
    methodKind: "unary";
    input: typeof FactorialRequestSchema;
    output: typeof MathResponseSchema;
  },
}>;

