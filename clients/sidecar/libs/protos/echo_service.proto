syntax = "proto3";

package com.augmentcode.transport.grpc.examples;

option java_outer_classname = "EchoServiceProto";
option java_package = "com.augmentcode.transport.grpc.examples";

// Echo service for demonstrating generated gRPC
service EchoService {
  // Simple echo method
  rpc Echo(EchoRequest) returns (EchoResponse);

  // Uppercase transformation
  rpc Uppercase(EchoRequest) returns (EchoResponse);

  // Reverse string
  rpc Reverse(EchoRequest) returns (EchoResponse);
}

// Request message
message EchoRequest {
  string message = 1;
  int64 timestamp = 2;
  optional string prefix = 3;
}

// Response message
message EchoResponse {
  string echoed_message = 1;
  int64 request_timestamp = 2;
  int64 response_timestamp = 3;
  string server_info = 4;
}

// Math service for more complex examples
service MathService {
  // Add two numbers
  rpc Add(MathRequest) returns (MathResponse);

  // Multiply two numbers
  rpc Multiply(MathRequest) returns (MathResponse);

  // Calculate factorial
  rpc Factorial(FactorialRequest) returns (MathResponse);
}

// Math request with two operands
message MathRequest {
  int32 a = 1;
  int32 b = 2;
}

// Factorial request with single operand
message FactorialRequest {
  int32 n = 1;
}

// Math response
message MathResponse {
  int64 result = 1;
  string operation = 2;
}
