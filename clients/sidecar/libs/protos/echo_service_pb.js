// @generated by protoc-gen-es v2.3.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/libs/protos/echo_service.proto (package com.augmentcode.transport.grpc.examples, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file clients/sidecar/libs/protos/echo_service.proto.
 */
export const file_clients_sidecar_libs_protos_echo_service = /*@__PURE__*/
  fileDesc("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvZWNob19zZXJ2aWNlLnByb3RvEidjb20uYXVnbWVudGNvZGUudHJhbnNwb3J0LmdycGMuZXhhbXBsZXMiUQoLRWNob1JlcXVlc3QSDwoHbWVzc2FnZRgBIAEoCRIRCgl0aW1lc3RhbXAYAiABKAMSEwoGcHJlZml4GAMgASgJSACIAQFCCQoHX3ByZWZpeCJyCgxFY2hvUmVzcG9uc2USFgoOZWNob2VkX21lc3NhZ2UYASABKAkSGQoRcmVxdWVzdF90aW1lc3RhbXAYAiABKAMSGgoScmVzcG9uc2VfdGltZXN0YW1wGAMgASgDEhMKC3NlcnZlcl9pbmZvGAQgASgJIiMKC01hdGhSZXF1ZXN0EgkKAWEYASABKAUSCQoBYhgCIAEoBSIdChBGYWN0b3JpYWxSZXF1ZXN0EgkKAW4YASABKAUiMQoMTWF0aFJlc3BvbnNlEg4KBnJlc3VsdBgBIAEoAxIRCglvcGVyYXRpb24YAiABKAky9AIKC0VjaG9TZXJ2aWNlEnMKBEVjaG8SNC5jb20uYXVnbWVudGNvZGUudHJhbnNwb3J0LmdycGMuZXhhbXBsZXMuRWNob1JlcXVlc3QaNS5jb20uYXVnbWVudGNvZGUudHJhbnNwb3J0LmdycGMuZXhhbXBsZXMuRWNob1Jlc3BvbnNlEngKCVVwcGVyY2FzZRI0LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5FY2hvUmVxdWVzdBo1LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5FY2hvUmVzcG9uc2USdgoHUmV2ZXJzZRI0LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5FY2hvUmVxdWVzdBo1LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5FY2hvUmVzcG9uc2Uy+QIKC01hdGhTZXJ2aWNlEnIKA0FkZBI0LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5NYXRoUmVxdWVzdBo1LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5NYXRoUmVzcG9uc2USdwoITXVsdGlwbHkSNC5jb20uYXVnbWVudGNvZGUudHJhbnNwb3J0LmdycGMuZXhhbXBsZXMuTWF0aFJlcXVlc3QaNS5jb20uYXVnbWVudGNvZGUudHJhbnNwb3J0LmdycGMuZXhhbXBsZXMuTWF0aFJlc3BvbnNlEn0KCUZhY3RvcmlhbBI5LmNvbS5hdWdtZW50Y29kZS50cmFuc3BvcnQuZ3JwYy5leGFtcGxlcy5GYWN0b3JpYWxSZXF1ZXN0GjUuY29tLmF1Z21lbnRjb2RlLnRyYW5zcG9ydC5ncnBjLmV4YW1wbGVzLk1hdGhSZXNwb25zZUI7Cidjb20uYXVnbWVudGNvZGUudHJhbnNwb3J0LmdycGMuZXhhbXBsZXNCEEVjaG9TZXJ2aWNlUHJvdG9iBnByb3RvMw");

/**
 * Describes the message com.augmentcode.transport.grpc.examples.EchoRequest.
 * Use `create(EchoRequestSchema)` to create a new message.
 */
export const EchoRequestSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_echo_service, 0);

/**
 * Describes the message com.augmentcode.transport.grpc.examples.EchoResponse.
 * Use `create(EchoResponseSchema)` to create a new message.
 */
export const EchoResponseSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_echo_service, 1);

/**
 * Describes the message com.augmentcode.transport.grpc.examples.MathRequest.
 * Use `create(MathRequestSchema)` to create a new message.
 */
export const MathRequestSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_echo_service, 2);

/**
 * Describes the message com.augmentcode.transport.grpc.examples.FactorialRequest.
 * Use `create(FactorialRequestSchema)` to create a new message.
 */
export const FactorialRequestSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_echo_service, 3);

/**
 * Describes the message com.augmentcode.transport.grpc.examples.MathResponse.
 * Use `create(MathResponseSchema)` to create a new message.
 */
export const MathResponseSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_echo_service, 4);

/**
 * @generated from service com.augmentcode.transport.grpc.examples.EchoService
 */
export const EchoService = /*@__PURE__*/
  serviceDesc(file_clients_sidecar_libs_protos_echo_service, 0);

/**
 * @generated from service com.augmentcode.transport.grpc.examples.MathService
 */
export const MathService = /*@__PURE__*/
  serviceDesc(file_clients_sidecar_libs_protos_echo_service, 1);

