#!/usr/bin/env node

/* eslint-disable no-console */

import { from<PERSON>son } from "@bufbuild/protobuf";
import {
  SendMessageTransport,
  ServiceRegistry,
  StdioMessageTarget,
} from "../ts";
import {
  EchoService,
  EchoResponseSchema,
  EchoRequest,
  EchoResponse,
} from "../../../protos/echo_service_pb";

/**
 * Echo Service Implementation
 */
class EchoServiceImpl {
  private serverInfo: string;

  constructor() {
    this.serverInfo = `TypeScript Server (PID: ${process.pid})`;
  }

  echo(request: EchoRequest): EchoResponse {
    try {
      console.error(`[SERVER] Echo request:`, request);

      // Validate request
      if (!request || typeof request.message !== "string") {
        throw new Error(
          `Invalid echo request: missing or invalid message field`,
        );
      }

      const prefix = request.prefix ? `${request.prefix} ` : "";
      const echoedMessage = `${prefix}${request.message}`;

      // Return proper protobuf message using JSON serialization
      const response = fromJson(EchoResponseSchema, {
        echoedMessage,
        requestTimestamp:
          request.timestamp?.toString() || Date.now().toString(),
        responseTimestamp: Date.now().toString(),
        serverInfo: this.serverInfo,
      });

      console.error(`[SERVER] Echo response:`, response);
      return response;
    } catch (error) {
      console.error(`[SERVER] Error in echo method:`, error);
      throw error;
    }
  }

  uppercase(request: EchoRequest): EchoResponse {
    try {
      console.error(`[SERVER] Uppercase request:`, request);

      // Validate request
      if (!request || typeof request.message !== "string") {
        throw new Error(
          `Invalid uppercase request: missing or invalid message field`,
        );
      }

      const prefix = request.prefix ? `${request.prefix} ` : "";
      const echoedMessage = `${prefix}${request.message.toUpperCase()}`;

      // Return proper protobuf message using JSON serialization
      const response = fromJson(EchoResponseSchema, {
        echoedMessage,
        requestTimestamp:
          request.timestamp?.toString() || Date.now().toString(),
        responseTimestamp: Date.now().toString(),
        serverInfo: this.serverInfo,
      });

      console.error(`[SERVER] Uppercase response:`, response);
      return response;
    } catch (error) {
      console.error(`[SERVER] Error in uppercase method:`, error);
      throw error;
    }
  }

  reverse(request: EchoRequest): EchoResponse {
    try {
      console.error(`[SERVER] Reverse request:`, request);

      // Validate request
      if (!request || typeof request.message !== "string") {
        throw new Error(
          `Invalid reverse request: missing or invalid message field`,
        );
      }

      const prefix = request.prefix ? `${request.prefix} ` : "";
      const reversedMessage = request.message.split("").reverse().join("");
      const echoedMessage = `${prefix}${reversedMessage}`;

      // Return proper protobuf message using JSON serialization
      const response = fromJson(EchoResponseSchema, {
        echoedMessage,
        requestTimestamp:
          request.timestamp?.toString() || Date.now().toString(),
        responseTimestamp: Date.now().toString(),
        serverInfo: this.serverInfo,
      });

      console.error(`[SERVER] Reverse response:`, response);
      return response;
    } catch (error) {
      console.error(`[SERVER] Error in reverse method:`, error);
      throw error;
    }
  }
}

/**
 * TypeScript Echo Server
 * Listens for gRPC requests from test executor and processes them
 */
class EchoServer {
  private target: StdioMessageTarget;
  private transport: SendMessageTransport;
  private registry: ServiceRegistry;
  private echoService: EchoServiceImpl;

  constructor() {
    this.target = new StdioMessageTarget({
      logPrefix: "[SERVER]",
      debug: false,
      jsonReplacer: (_key, value: unknown) =>
        typeof value === "bigint" ? value.toString() : value,
    });
    this.transport = new SendMessageTransport(this.target);
    this.echoService = new EchoServiceImpl();

    // Create service registry and register the echo service
    this.registry = ServiceRegistry.withGrpcServiceTransport(this.transport);
    this.registerEchoService();

    console.error("[SERVER] TypeScript Echo Server ready");
  }

  private registerEchoService(): void {
    this.registry.registerService(EchoService, this.echoService);
    console.error("[SERVER] Echo service registered");
  }

  start(): void {
    // Keep the process alive
    process.stdin.resume();

    // Handle graceful shutdown
    process.on("SIGINT", () => {
      console.error("[SERVER] Shutting down...");
      this.transport.dispose();
      this.target.dispose();
      process.exit(0);
    });

    process.on("SIGTERM", () => {
      console.error("[SERVER] Shutting down...");
      this.transport.dispose();
      this.target.dispose();
      process.exit(0);
    });

    console.error("[SERVER] Server started and listening for requests...");
  }
}

// Create and start the server
const server = new EchoServer();
server.start();
