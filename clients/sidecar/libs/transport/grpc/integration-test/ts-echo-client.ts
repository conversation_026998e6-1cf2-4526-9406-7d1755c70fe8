#!/usr/bin/env node

/* eslint-disable no-console */

import { createClient } from "@connectrpc/connect";
import { create } from "@bufbuild/protobuf";
import { SendMessageTransport, StdioMessageTarget } from "../ts";
import {
  EchoService,
  EchoRequestSchema,
  type EchoResponse,
} from "../../../protos/echo_service_pb";

/**
 * Interface for test commands from the test executor
 */
interface TestCommand {
  type: "test";
  method: "echo" | "uppercase" | "reverse";
  message: string;
  prefix?: string;
  expectError?: boolean;
}

/**
 * Interface for test results sent back to the test executor
 */
interface TestResult {
  type: "result";
  success: boolean;
  response?: {
    echoedMessage?: string;
    requestTimestamp?: string;
    responseTimestamp?: string;
    serverInfo?: string;
  };
  error?: string;
  duration?: number;
}

/**
 * TypeScript Echo Client
 * Receives test commands from test executor and makes gRPC calls
 */
class EchoClient {
  private target: StdioMessageTarget;
  private transport: SendMessageTransport;
  private client: ReturnType<typeof createClient<typeof EchoService>>;

  constructor() {
    this.target = new StdioMessageTarget({
      logPrefix: "[CLIENT]",
      debug: false,
      jsonReplacer: (_key, value: unknown) =>
        typeof value === "bigint" ? value.toString() : value,
    });
    this.transport = new SendMessageTransport(this.target);
    this.client = createClient(EchoService, this.transport);

    // Listen for test commands from the test executor
    this.target.onReceiveMessage((message: unknown) => {
      if (this.isTestCommand(message)) {
        void this.handleTestCommand(message);
      }
    });

    console.error("[CLIENT] TypeScript Echo Client ready");
  }

  private isTestCommand(message: unknown): message is TestCommand {
    return (
      typeof message === "object" &&
      message !== null &&
      "type" in message &&
      (message as { type: unknown }).type === "test"
    );
  }

  private async handleTestCommand(command: TestCommand): Promise<void> {
    const startTime = Date.now();

    try {
      console.error(
        `[CLIENT] Executing test: ${command.method} with message: "${command.message}"`,
      );

      // Create the request as a proper protobuf message
      const requestData = create(EchoRequestSchema, {
        message: command.message,
        timestamp: BigInt(Date.now()),
        prefix: command.prefix,
      });

      let response: EchoResponse;

      // Call the appropriate method with the plain JSON data
      switch (command.method) {
        case "echo":
          response = await this.client.echo(requestData);
          break;
        case "uppercase":
          response = await this.client.uppercase(requestData);
          break;
        case "reverse":
          response = await this.client.reverse(requestData);
          break;
        default:
          throw new Error(`Unknown method: ${command.method as string}`);
      }

      const duration = Date.now() - startTime;

      console.error(`[CLIENT] Received response:`, response);

      // Validate the response based on the method
      const isValid = this.validateResponse(command, response);

      const result: TestResult = {
        type: "result",
        success: isValid,
        response: {
          echoedMessage: response.echoedMessage,
          requestTimestamp: response.requestTimestamp?.toString(),
          responseTimestamp: response.responseTimestamp?.toString(),
          serverInfo: response.serverInfo,
        },
        duration,
      };

      if (!isValid) {
        result.error = `Response validation failed for method ${command.method}`;
      }

      // Send result back to test executor
      this.target.sendMessage(result);
    } catch (error) {
      const duration = Date.now() - startTime;

      console.error(`[CLIENT] Error during test execution:`, error);

      // Provide more detailed error information
      let errorMessage = "Unknown error";
      let errorType = "UnknownError";

      if (error instanceof Error) {
        errorMessage = error.message;
        errorType = error.constructor.name;

        // Add stack trace for debugging if it's not a timeout
        if (!errorMessage.includes("timed out")) {
          console.error(`[CLIENT] Error stack trace:`, error.stack);
        }
      } else {
        errorMessage = String(error);
      }

      const result: TestResult = {
        type: "result",
        success: command.expectError || false,
        error: `${errorType}: ${errorMessage}`,
        duration,
      };

      // Send error result back to test executor
      this.target.sendMessage(result);
    }
  }

  private validateResponse(
    command: TestCommand,
    response: EchoResponse,
  ): boolean {
    try {
      // Check if response exists and has the right structure
      if (!response) {
        console.error("[CLIENT] Response is null or undefined");
        return false;
      }

      if (typeof response !== "object") {
        console.error(
          `[CLIENT] Response is not an object, got: ${typeof response}`,
        );
        return false;
      }

      // Check for required echoedMessage field
      if (
        !("echoedMessage" in response) ||
        response.echoedMessage === null ||
        response.echoedMessage === undefined
      ) {
        console.error(
          "[CLIENT] Response missing or invalid echoedMessage field",
        );
        console.error(
          "[CLIENT] Response structure:",
          Object.keys(response as object),
        );
        return false;
      }

      // Validate the echoed message content
      const expected = this.getExpectedResponse(command);
      const actual = response.echoedMessage;

      if (actual !== expected) {
        console.error(
          `[CLIENT] Response content mismatch for method "${command.method}"`,
        );
        console.error(`[CLIENT] Expected: "${expected}"`);
        console.error(`[CLIENT] Actual: "${actual}"`);
        console.error(`[CLIENT] Input message: "${command.message}"`);
        return false;
      }

      // Validate timestamps are present and reasonable
      if (!response.requestTimestamp || !response.responseTimestamp) {
        console.error("[CLIENT] Response missing timestamps");
        console.error(
          `[CLIENT] requestTimestamp: ${response.requestTimestamp}`,
        );
        console.error(
          `[CLIENT] responseTimestamp: ${response.responseTimestamp}`,
        );
        return false;
      }

      // Validate timestamps are reasonable (not in the future, response after request)
      const requestTime =
        typeof response.requestTimestamp === "bigint"
          ? Number(response.requestTimestamp)
          : parseInt(String(response.requestTimestamp));
      const responseTime =
        typeof response.responseTimestamp === "bigint"
          ? Number(response.responseTimestamp)
          : parseInt(String(response.responseTimestamp));

      if (responseTime < requestTime) {
        console.error(
          `[CLIENT] Invalid timestamps: response time (${responseTime}) is before request time (${requestTime})`,
        );
        return false;
      }

      // Validate server info is present and reasonable
      if (
        !response.serverInfo ||
        typeof response.serverInfo !== "string" ||
        response.serverInfo.trim() === ""
      ) {
        console.error("[CLIENT] Response missing or invalid serverInfo");
        console.error(`[CLIENT] serverInfo: ${response.serverInfo}`);
        return false;
      }

      console.error(
        `[CLIENT] Response validation passed for method "${command.method}"`,
      );
      return true;
    } catch (error) {
      console.error("[CLIENT] Error during response validation:", error);
      return false;
    }
  }

  private getExpectedResponse(command: TestCommand): string {
    const prefix = command.prefix ? `${command.prefix} ` : "";

    switch (command.method) {
      case "echo":
        return `${prefix}${command.message}`;
      case "uppercase":
        return `${prefix}${command.message.toUpperCase()}`;
      case "reverse":
        return `${prefix}${command.message.split("").reverse().join("")}`;
      default:
        throw new Error(`Unknown method: ${command.method as string}`);
    }
  }

  start(): void {
    // Keep the process alive
    process.stdin.resume();

    // Handle graceful shutdown
    process.on("SIGINT", () => {
      console.error("[CLIENT] Shutting down...");
      this.transport.dispose();
      this.target.dispose();
      process.exit(0);
    });

    process.on("SIGTERM", () => {
      console.error("[CLIENT] Shutting down...");
      this.transport.dispose();
      this.target.dispose();
      process.exit(0);
    });
  }
}

// Create and start the client
const client = new EchoClient();
client.start();
