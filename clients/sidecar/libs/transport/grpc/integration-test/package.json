{"name": "@augmentcode/grpc-integration-test-v2", "version": "1.0.0", "description": "Improved integration tests for TypeScript-Kotlin gRPC transport", "private": true, "scripts": {"build": "pnpm run clean && pnpm run compile", "clean": "rm -rf dist", "compile": "pnpm run build:executor && pnpm run build:client && pnpm run build:server && pnpm run make-executable", "build:executor": "esbuild test-executor.ts --bundle --platform=node --outfile=dist/test-executor.js --format=cjs --target=node18", "build:client": "esbuild ts-echo-client.ts --bundle --platform=node --outfile=dist/ts-echo-client.js --format=cjs --target=node18", "build:server": "esbuild ts-echo-server.ts --bundle --platform=node --outfile=dist/ts-echo-server.js --format=cjs --target=node18", "make-executable": "chmod +x dist/*.js", "test": "pnpm run build && node dist/test-executor.js", "test:ts-to-ts": "pnpm run build && node dist/test-executor.js --scenario ts-to-ts", "test:debug": "pnpm run build && node --inspect dist/test-executor.js", "dev": "pnpm run build -- --watch"}, "dependencies": {"@bufbuild/protobuf": "^2.3.0", "@connectrpc/connect": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "esbuild": "^0.19.11", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}