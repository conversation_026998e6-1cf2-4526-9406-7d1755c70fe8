{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": false, "sourceMap": false, "removeComments": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022", "DOM"], "types": ["node", "@bufbuild/protobuf"], "baseUrl": "./", "paths": {"../ts/*": ["../ts/*"], "../../../protos/*": ["../../../protos/*"]}}, "include": ["*.ts", "../ts/*.ts", "../../../protos/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}