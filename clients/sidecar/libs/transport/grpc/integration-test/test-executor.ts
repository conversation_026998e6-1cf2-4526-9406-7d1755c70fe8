#!/usr/bin/env node

/* eslint-disable no-console */

import { spawn, ChildProcess } from "child_process";
import { EventEmitter } from "events";
import { GrpcSendMessageTransportPayload as GrpcMessage } from "../ts";

/**
 * Interface for test commands sent to clients
 */
interface TestCommand {
  type: "test";
  method: "echo" | "uppercase" | "reverse";
  message: string;
  prefix?: string;
  expectError?: boolean;
}

/**
 * Interface for test results from clients
 */
interface TestResult {
  type: "result";
  success: boolean;
  response?: {
    echoedMessage?: string;
    requestTimestamp?: string;
    responseTimestamp?: string;
    serverInfo?: string;
  };
  error?: string;
  duration?: number;
}

/**
 * Represents a spawned process with stdio communication
 */
class ProcessWrapper extends EventEmitter {
  private process: ChildProcess;
  private buffer: string = "";

  constructor(
    public readonly name: string,
    command: string,
    args: string[] = [],
    cwd?: string,
  ) {
    super();

    // Parse command and args for Kotlin executables
    const {
      finalCommand,
      finalArgs,
      env,
      cwd: parsedCwd,
    } = this.parseCommand(command, args);
    const finalCwd = parsedCwd || cwd;

    console.log(
      `[${this.name}] Spawning: ${finalCommand} ${finalArgs.join(" ")} ${finalCwd ? `(cwd: ${finalCwd})` : ""}`,
    );
    this.process = spawn(finalCommand, finalArgs, {
      cwd: finalCwd,
      stdio: ["pipe", "pipe", "pipe"],
      env: { ...process.env, ...env },
    });

    this.setupEventHandlers();
  }

  private parseCommand(
    command: string,
    args: string[],
  ): {
    finalCommand: string;
    finalArgs: string[];
    env: Record<string, string>;
    cwd?: string;
  } {
    const env: Record<string, string> = {};
    let cwd: string | undefined;

    // // Handle Kotlin gradlew commands
    // if (command.startsWith("kotlin:gradlew:")) {
    //   const commandPart = command.replace("kotlin:gradlew:", "");
    //   const parts = commandPart.split(/\s+/);
    //   const gradlewCommand = parts[0]; // e.g., "run"
    //   const gradlewArgs = parts.slice(1); // e.g., ["--args='--mode=client'"]

    //   // Set working directory to kotlin subdirectory
    //   cwd = "./kotlin";

    //   return {
    //     finalCommand: "./gradlew",
    //     finalArgs: [gradlewCommand, ...gradlewArgs, ...args],
    //     env,
    //     cwd,
    //   };
    // }

    return {
      finalCommand: command,
      finalArgs: args,
      env,
      cwd,
    };
  }

  private setupEventHandlers(): void {
    this.process.stdout?.on("data", (data: Buffer) => {
      this.buffer += data.toString();
      this.processBuffer();
    });

    this.process.stderr?.on("data", (data: Buffer) => {
      const message = data.toString().trim();
      if (message) {
        console.log(`[${this.name}:stderr] ${message}`);

        // Check for readiness indicators
        if (
          message.includes("ready") ||
          message.includes("listening") ||
          message.includes("waiting for messages") ||
          message.includes("Server started")
        ) {
          this.emit("ready");
        }
      }
    });

    this.process.on("exit", (code, signal) => {
      console.log(
        `[${this.name}] Process exited with code ${code}, signal ${signal}`,
      );
      this.emit("exit", code, signal);
    });

    this.process.on("error", (error) => {
      console.error(`[${this.name}] Process error:`, error);
      this.emit("error", error);
    });
  }

  private processBuffer(): void {
    const lines = this.buffer.split("\n");
    this.buffer = lines.pop() || ""; // Keep incomplete line in buffer

    for (const line of lines) {
      if (line.trim()) {
        try {
          const message: unknown = JSON.parse(line.trim());
          console.log(`[${this.name}:stdout] ${JSON.stringify(message)}`);
          this.emit("message", message);
        } catch (error) {
          console.log(`[${this.name}:stdout] ${line.trim()}`);
        }
      }
    }
  }

  sendMessage(message: unknown): void {
    const json = JSON.stringify(message);
    console.log(`[${this.name}:stdin] ${json}`);
    this.process.stdin?.write(json + "\n");
  }

  kill(): void {
    if (this.process && !this.process.killed) {
      console.log(`[${this.name}] Killing process`);
      this.process.kill("SIGTERM");
    }
  }

  get pid(): number | undefined {
    return this.process.pid;
  }
}

/**
 * Main test executor that orchestrates integration tests
 */
class TestExecutor {
  private processes: ProcessWrapper[] = [];
  private pendingRequests = new Map<
    string,
    {
      resolve: (value: any) => void;
      reject: (reason: any) => void;
      timeout: NodeJS.Timeout;
    }
  >();

  async runTest(
    clientPath: string,
    serverPath: string,
    testCommand: TestCommand,
    timeoutMs: number = 10000,
  ): Promise<TestResult> {
    console.log(`\n=== Running Test: ${testCommand.method} ===`);
    console.log(`Client: ${clientPath}`);
    console.log(`Server: ${serverPath}`);
    console.log(`Command:`, testCommand);

    // Create client and server processes
    const client = clientPath.endsWith(".js")
      ? new ProcessWrapper("client", "node", [clientPath])
      : new ProcessWrapper("client", clientPath);

    const server = serverPath.endsWith(".js")
      ? new ProcessWrapper("server", "node", [serverPath])
      : new ProcessWrapper("server", serverPath);

    this.processes.push(client, server);

    try {
      // Set up message routing
      this.setupMessageRouting(client, server);

      // Wait for processes to be ready (longer timeout for Kotlin processes)
      const readyTimeout =
        clientPath.includes("kotlin") || serverPath.includes("kotlin")
          ? 15000
          : 5000;
      await this.waitForReady([client, server], readyTimeout);

      // Send test command to client (longer timeout for Kotlin processes)
      const testTimeout =
        clientPath.includes("kotlin") || serverPath.includes("kotlin")
          ? 20000
          : timeoutMs;
      const testResult = await this.executeTest(
        client,
        testCommand,
        testTimeout,
      );

      console.log(`Test result:`, testResult);
      return testResult;
    } finally {
      // Clean up processes
      this.cleanup();
    }
  }

  private setupMessageRouting(
    client: ProcessWrapper,
    server: ProcessWrapper,
  ): void {
    // Route gRPC requests from client to server
    client.on("message", (message: unknown) => {
      if (this.isGrpcRequest(message)) {
        console.log(
          `[ROUTER] Request ${message.id}: ${client.name} -> ${server.name}`,
        );
        server.sendMessage(message);
      } else if (this.isTestResult(message)) {
        // Test result from client - don't route, handle in executeTest
      }
    });

    // Route gRPC responses from server to client
    server.on("message", (message: unknown) => {
      if (this.isGrpcResponse(message)) {
        console.log(
          `[ROUTER] Response ${message.id}: ${server.name} -> ${client.name}`,
        );
        client.sendMessage(message);
      }
    });
  }

  private isGrpcRequest(message: unknown): message is GrpcMessage {
    return (
      typeof message === "object" &&
      message !== null &&
      "type" in message &&
      (message as { type: unknown }).type ===
        "com.augmentcode.client.rpc.request"
    );
  }

  private isGrpcResponse(message: unknown): message is GrpcMessage {
    return (
      typeof message === "object" &&
      message !== null &&
      "type" in message &&
      (message as { type: unknown }).type ===
        "com.augmentcode.client.rpc.response"
    );
  }

  private isTestResult(message: unknown): message is TestResult {
    return (
      typeof message === "object" &&
      message !== null &&
      "type" in message &&
      (message as { type: unknown }).type === "result"
    );
  }

  private async waitForReady(
    processes: ProcessWrapper[],
    timeoutMs: number,
  ): Promise<void> {
    console.log(`[EXECUTOR] Waiting for processes to be ready...`);

    const startTime = Date.now();
    const readyStates = new Map<string, boolean>();

    // Initialize ready states
    for (const process of processes) {
      readyStates.set(process.name, false);
    }

    // Set up listeners for readiness signals
    const readyPromises = processes.map((process) => {
      return new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(
            new Error(
              `Process ${process.name} did not become ready within ${timeoutMs}ms`,
            ),
          );
        }, timeoutMs);

        // Create a custom event for readiness detection
        const readyHandler = () => {
          console.log(`[EXECUTOR] Process ${process.name} is ready`);
          readyStates.set(process.name, true);
          clearTimeout(timeout);
          process.off("ready", readyHandler);
          process.off("exit", exitHandler);
          resolve();
        };

        // Also check for process exit
        const exitHandler = (code: number | null) => {
          if (code !== null && code !== 0) {
            clearTimeout(timeout);
            process.off("ready", readyHandler);
            process.off("exit", exitHandler);
            reject(
              new Error(
                `Process ${process.name} exited with code ${code} before becoming ready`,
              ),
            );
          }
        };

        process.on("ready", readyHandler);
        process.on("exit", exitHandler);

        // Check if process already failed to start
        if (process.pid === undefined) {
          clearTimeout(timeout);
          reject(new Error(`Process ${process.name} failed to start`));
        }
      });
    });

    try {
      // Wait for all processes to be ready
      await Promise.all(readyPromises);

      // Additional small delay to ensure message handlers are fully set up
      await new Promise((resolve) => setTimeout(resolve, 500));

      const elapsed = Date.now() - startTime;
      console.log(`[EXECUTOR] All processes ready after ${elapsed}ms`);
    } catch (error) {
      // Clean up any processes that did start
      for (const process of processes) {
        process.kill();
      }
      throw error;
    }
  }

  private async executeTest(
    client: ProcessWrapper,
    testCommand: TestCommand,
    timeoutMs: number,
  ): Promise<TestResult> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      const timeout = setTimeout(() => {
        const elapsed = Date.now() - startTime;
        console.log(
          `[EXECUTOR] Test timed out after ${elapsed}ms (limit: ${timeoutMs}ms)`,
        );
        reject(new Error(`Test timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      // Listen for test result from client
      const resultHandler = (message: unknown) => {
        if (this.isTestResult(message)) {
          const elapsed = Date.now() - startTime;
          console.log(`[EXECUTOR] Test completed in ${elapsed}ms`);
          clearTimeout(timeout);
          client.off("message", resultHandler);
          client.off("exit", exitHandler);
          resolve(message);
        }
      };

      // Listen for client exit
      const exitHandler = (code: number | null) => {
        if (code !== null && code !== 0) {
          const elapsed = Date.now() - startTime;
          console.log(
            `[EXECUTOR] Client exited with code ${code} after ${elapsed}ms`,
          );
          clearTimeout(timeout);
          client.off("message", resultHandler);
          client.off("exit", exitHandler);
          reject(
            new Error(
              `Client process exited with code ${code} before test completion`,
            ),
          );
        }
      };

      client.on("message", resultHandler);
      client.on("exit", exitHandler);

      // Send test command to client
      console.log(
        `[EXECUTOR] Sending test command to client: ${JSON.stringify(testCommand)}`,
      );
      client.sendMessage(testCommand);
    });
  }

  private cleanup(): void {
    console.log(`[EXECUTOR] Cleaning up processes...`);

    for (const process of this.processes) {
      try {
        if (process.pid) {
          console.log(
            `[EXECUTOR] Killing process ${process.name} (PID: ${process.pid})`,
          );
          process.kill();
        } else {
          console.log(`[EXECUTOR] Process ${process.name} already terminated`);
        }
      } catch (error) {
        console.error(
          `[EXECUTOR] Error killing process ${process.name}:`,
          error,
        );
      }
    }

    this.processes = [];

    // Clear any pending timeouts
    for (const { timeout } of this.pendingRequests.values()) {
      clearTimeout(timeout);
    }
    this.pendingRequests.clear();

    console.log(`[EXECUTOR] Cleanup completed`);
  }
}

/**
 * Main function to run integration tests
 */
async function main(): Promise<void> {
  const executor = new TestExecutor();

  const testCases = [
    // TypeScript to TypeScript tests
    {
      name: "TS Client -> TS Server: Echo",
      client: "./dist/ts-echo-client.js",
      server: "./dist/ts-echo-server.js",
      command: {
        type: "test" as const,
        method: "echo" as const,
        message: "Hello World",
      },
    },
    {
      name: "TS Client -> TS Server: Uppercase",
      client: "./dist/ts-echo-client.js",
      server: "./dist/ts-echo-server.js",
      command: {
        type: "test" as const,
        method: "uppercase" as const,
        message: "hello world",
      },
    },
    {
      name: "TS Client -> TS Server: Reverse",
      client: "./dist/ts-echo-client.js",
      server: "./dist/ts-echo-server.js",
      command: {
        type: "test" as const,
        method: "reverse" as const,
        message: "Hello World",
      },
    },
    // // Kotlin to TypeScript tests
    // {
    //   name: "Kotlin Client -> TS Server: Echo",
    //   client: "kotlin:gradlew:run --args='--mode=client'",
    //   server: "./dist/ts-echo-server.js",
    //   command: {
    //     type: "test" as const,
    //     method: "echo" as const,
    //     message: "Hello from Kotlin",
    //   },
    // },
    // {
    //   name: "Kotlin Client -> TS Server: Uppercase",
    //   client: "kotlin:gradlew:run --args='--mode=client'",
    //   server: "./dist/ts-echo-server.js",
    //   command: {
    //     type: "test" as const,
    //     method: "uppercase" as const,
    //     message: "kotlin client test",
    //   },
    // },
    // // TypeScript to Kotlin tests
    // {
    //   name: "TS Client -> Kotlin Server: Echo",
    //   client: "./dist/ts-echo-client.js",
    //   server: "kotlin:gradlew:run --args='--mode=server'",
    //   command: {
    //     type: "test" as const,
    //     method: "echo" as const,
    //     message: "Hello to Kotlin",
    //   },
    // },
    // {
    //   name: "TS Client -> Kotlin Server: Uppercase",
    //   client: "./dist/ts-echo-client.js",
    //   server: "kotlin:gradlew:run --args='--mode=server'",
    //   command: {
    //     type: "test" as const,
    //     method: "uppercase" as const,
    //     message: "typescript client test",
    //   },
    // },
    // // Kotlin to Kotlin tests
    // {
    //   name: "Kotlin Client -> Kotlin Server: Echo",
    //   client: "kotlin:gradlew:run --args='--mode=client'",
    //   server: "kotlin:gradlew:run --args='--mode=server'",
    //   command: {
    //     type: "test" as const,
    //     method: "echo" as const,
    //     message: "Kotlin to Kotlin",
    //   },
    // },
    // {
    //   name: "Kotlin Client -> Kotlin Server: Reverse",
    //   client: "kotlin:gradlew:run --args='--mode=client'",
    //   server: "kotlin:gradlew:run --args='--mode=server'",
    //   command: {
    //     type: "test" as const,
    //     method: "reverse" as const,
    //     message: "dlroW olleH",
    //   },
    // },
  ];

  let passed = 0;
  let failed = 0;

  for (const testCase of testCases) {
    try {
      console.log(`\n${"=".repeat(60)}`);
      console.log(`Running: ${testCase.name}`);
      console.log(`${"=".repeat(60)}`);

      const result = await executor.runTest(
        testCase.client,
        testCase.server,
        testCase.command,
      );

      if (result.success) {
        console.log(`✅ PASSED: ${testCase.name}`);
        passed++;
      } else {
        console.log(`❌ FAILED: ${testCase.name} - ${result.error}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ERROR: ${testCase.name} - ${String(error)}`);
      failed++;
    }
  }

  console.log(`\n${"=".repeat(60)}`);
  console.log(`Test Results: ${passed} passed, ${failed} failed`);
  console.log(`${"=".repeat(60)}`);

  process.exit(failed > 0 ? 1 : 0);
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error("Unhandled error:", error);
    process.exit(1);
  });
}

export { TestExecutor, TestCommand, TestResult };
