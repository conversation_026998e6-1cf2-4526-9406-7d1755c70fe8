import { create } from "@bufbuild/protobuf";
import { ServiceRegistry } from "../service-registry";
import { GrpcSendMessageTransportPayload } from "../send-message-transport";
import {
  TestResponseSchema,
  TestService,
} from "@augment-internal/sidecar-libs/protos/test_service_pb";

describe("ServiceRegistry", () => {
  let registry: ServiceRegistry;

  beforeEach(() => {
    registry = new ServiceRegistry();
  });

  describe("registerService", () => {
    it("should register a service", () => {
      // Arrange
      const impl = {
        testMethod: jest.fn(),
        errorMethod: jest.fn(),
      };

      // Act
      registry.registerService(TestService, impl);

      // Assert
      const service = registry.getService(TestService.typeName);
      expect(service).toBeDefined();
      expect(service?.methods.testMethod).toBeDefined();
      expect(service?.methods.errorMethod).toBeDefined();
    });
  });

  describe("canHandle", () => {
    it("should return true for registered services", () => {
      // Arrange
      const impl = {
        testMethod: jest.fn(),
        errorMethod: jest.fn(),
      };
      registry.registerService(TestService, impl);

      const message: GrpcSendMessageTransportPayload = {
        type: "com.augmentcode.client.rpc.request",
        id: "test-id",
        methodLocalName: "testMethod",
        serviceTypeName: TestService.typeName,
        data: {},
      };

      // Act
      const result = registry.canHandle(message);

      // Assert
      expect(result).toBe(true);
    });

    it("should return false for unregistered services", () => {
      // Arrange
      const message: GrpcSendMessageTransportPayload = {
        type: "com.augmentcode.client.rpc.request",
        id: "test-id",
        methodLocalName: "testMethod",
        serviceTypeName: "UnregisteredService",
        data: {},
      };

      // Act
      const result = registry.canHandle(message);

      // Assert
      expect(result).toBe(false);
    });

    it("should return false for response messages", () => {
      // Arrange
      const impl = {
        testMethod: jest.fn(),
        errorMethod: jest.fn(),
      };
      registry.registerService(TestService, impl);

      const message: GrpcSendMessageTransportPayload = {
        type: "com.augmentcode.client.rpc.response",
        id: "test-id",
        methodLocalName: "testMethod",
        serviceTypeName: TestService.typeName,
        data: {},
      };

      // Act
      const result = registry.canHandle(message);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe("handleRequest", () => {
    it("should handle a request and send a response", async () => {
      // Arrange
      const impl = {
        testMethod: jest
          .fn()
          .mockResolvedValue(
            create(TestResponseSchema, { result: "test-result" }),
          ),
        errorMethod: jest.fn(),
      };
      registry.registerService(TestService, impl);

      const message: GrpcSendMessageTransportPayload = {
        type: "com.augmentcode.client.rpc.request",
        id: "test-id",
        methodLocalName: "testMethod",
        serviceTypeName: TestService.typeName,
        data: { foo: "test-value" },
      };

      const sendResponse = jest.fn();

      // Act
      await registry.handleRequest(message, sendResponse);

      // Assert
      expect(impl.testMethod).toHaveBeenCalled();
      expect(sendResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          type: "com.augmentcode.client.rpc.response",
          id: "test-id",
          methodLocalName: "testMethod",
          serviceTypeName: TestService.typeName,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: expect.any(Object),
        }),
      );
    });

    it("should handle errors and send an error response", async () => {
      // Arrange
      const error = new Error("Test error");
      const impl = {
        testMethod: jest.fn().mockRejectedValue(error),
        errorMethod: jest.fn(),
      };
      registry.registerService(TestService, impl);

      const message: GrpcSendMessageTransportPayload = {
        type: "com.augmentcode.client.rpc.request",
        id: "test-id",
        methodLocalName: "testMethod",
        serviceTypeName: TestService.typeName,
        data: { foo: "test-value" },
      };

      const sendResponse = jest.fn();

      // Act
      await registry.handleRequest(message, sendResponse);

      // Assert
      expect(impl.testMethod).toHaveBeenCalled();
      expect(sendResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          type: "com.augmentcode.client.rpc.response",
          id: "test-id",
          methodLocalName: "testMethod",
          serviceTypeName: TestService.typeName,
          error: "Test error",
        }),
      );
    });

    it("should send an error response for unregistered services", async () => {
      // Arrange
      const message: GrpcSendMessageTransportPayload = {
        type: "com.augmentcode.client.rpc.request",
        id: "test-id",
        methodLocalName: "testMethod",
        serviceTypeName: "UnregisteredService",
        data: { foo: "test-value" },
      };

      const sendResponse = jest.fn();

      // Act
      await registry.handleRequest(message, sendResponse);

      // Assert
      expect(sendResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          type: "com.augmentcode.client.rpc.response",
          id: "test-id",
          methodLocalName: "testMethod",
          serviceTypeName: "UnregisteredService",
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          error: expect.stringContaining("Service not registered"),
        }),
      );
    });
  });
});
