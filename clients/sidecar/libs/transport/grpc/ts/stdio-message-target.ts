/* eslint-disable no-console */

import { MessageTarget } from "./message-target";

/**
 * Configuration options for StdioMessageTarget
 */
export interface StdioMessageTargetOptions {
  /**
   * Prefix to add to error messages for debugging
   * @default ""
   */
  logPrefix?: string;

  /**
   * Whether to enable debug logging
   * @default false
   */
  debug?: boolean;

  /**
   * Custom JSON replacer function for serialization
   * @default undefined
   */
  jsonReplacer?: (key: string, value: any) => any;

  /**
   * Custom JSON reviver function for deserialization
   * @default undefined
   */
  jsonReviver?: (key: string, value: any) => any;
}

/**
 * A MessageTarget implementation that communicates via stdin/stdout.
 * This is useful for process-to-process communication where one process
 * sends JSON messages via stdout and receives them via stdin.
 *
 * Features:
 * - Line-based JSON message protocol
 * - BigInt serialization support
 * - Error handling for malformed JSON
 * - Debug logging support
 * - Configurable message prefixes
 *
 * Usage:
 * ```typescript
 * const target = new StdioMessageTarget({
 *   logPrefix: '[CLIENT]',
 *   debug: true
 * });
 *
 * // Send a message
 * target.sendMessage({ type: 'test', data: 'hello' });
 *
 * // Listen for messages
 * const cleanup = target.onReceiveMessage((message) => {
 *   console.log('Received:', message);
 * });
 *
 * // Clean up when done
 * cleanup();
 * ```
 */
export class StdioMessageTarget implements MessageTarget {
  private messageCallbacks: Set<(message: unknown) => void> = new Set();
  private readonly options: Required<
    Omit<StdioMessageTargetOptions, "jsonReviver">
  > &
    Pick<StdioMessageTargetOptions, "jsonReviver">;
  private isInitialized = false;

  constructor(options: StdioMessageTargetOptions = {}) {
    this.options = {
      logPrefix: options.logPrefix || "",
      debug: options.debug || false,
      jsonReplacer: options.jsonReplacer || this.defaultJsonReplacer,
      jsonReviver: options.jsonReviver,
    };

    this.initialize();
  }

  /**
   * Initialize stdin listening
   */
  private initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Set up stdin to receive UTF-8 text
    process.stdin.setEncoding("utf8");

    // Listen for data from stdin
    process.stdin.on("data", this.handleStdinData.bind(this));

    this.isInitialized = true;

    if (this.options.debug) {
      this.log("StdioMessageTarget initialized");
    }
  }

  /**
   * Handle incoming data from stdin
   */
  private handleStdinData(data: string): void {
    const lines = data.trim().split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) {
        continue;
      }

      try {
        const message: unknown = JSON.parse(
          trimmedLine,
          this.options.jsonReviver,
        );

        if (this.options.debug) {
          this.log("Received message:", message);
        }

        // Notify all registered callbacks
        for (const callback of this.messageCallbacks) {
          try {
            callback(message);
          } catch (error) {
            this.logError("Error in message callback:", error);
          }
        }
      } catch (error) {
        this.logError("Failed to parse JSON from stdin:", error);
        if (this.options.debug) {
          this.logError("Raw line was:", trimmedLine);
        }
      }
    }
  }

  /**
   * Send a message to stdout as a JSON line
   */
  sendMessage(message: unknown): void {
    try {
      const jsonString = JSON.stringify(message, this.options.jsonReplacer);

      if (this.options.debug) {
        this.log("Sending message:", message);
      }

      // Write to stdout with newline
      console.log(jsonString);
    } catch (error) {
      this.logError("Failed to serialize message:", error);
      throw error;
    }
  }

  /**
   * Register a callback for incoming messages
   */
  onReceiveMessage(callback: (message: unknown) => void): () => void {
    this.messageCallbacks.add(callback);

    if (this.options.debug) {
      this.log(
        "Message callback registered, total callbacks:",
        this.messageCallbacks.size,
      );
    }

    // Return cleanup function
    return () => {
      this.messageCallbacks.delete(callback);
      if (this.options.debug) {
        this.log(
          "Message callback unregistered, remaining callbacks:",
          this.messageCallbacks.size,
        );
      }
    };
  }

  /**
   * Default JSON replacer that handles BigInt serialization
   */
  private defaultJsonReplacer = (_key: string, value: any): any => {
    if (typeof value === "bigint") {
      return value.toString();
    }
    return value;
  };

  /**
   * Log a message with optional prefix
   */
  private log(...args: unknown[]): void {
    if (this.options.logPrefix) {
      console.error(this.options.logPrefix, ...args);
    } else {
      console.error(...args);
    }
  }

  /**
   * Log an error message with optional prefix
   */
  private logError(...args: unknown[]): void {
    if (this.options.logPrefix) {
      console.error(this.options.logPrefix, ...args);
    } else {
      console.error(...args);
    }
  }

  /**
   * Dispose of resources and clean up
   */
  dispose(): void {
    this.messageCallbacks.clear();

    if (this.options.debug) {
      this.log("StdioMessageTarget disposed");
    }
  }
}
