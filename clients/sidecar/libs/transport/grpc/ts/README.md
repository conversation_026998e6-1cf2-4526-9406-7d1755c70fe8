# gRPC Communication Layer

This directory contains the implementation of a custom gRPC communication layer that uses `sendMessage` for communication between different contexts (e.g., extension and webview).

## Components

### MessageTarget

The `MessageTarget` interface defines a target for sending and receiving messages. It has two methods:

- `postMessage(message: unknown): void` - Sends a message to the target
- `onMessage(handler: (message: unknown) => void): () => void` - Registers a handler for incoming messages and returns a cleanup function

### GrpcSendMessageTransportPayload

The `GrpcSendMessageTransportPayload` interface defines the structure of messages sent over the transport:

- `type` - Either "com.augmentcode.client.rpc.request" or "com.augmentcode.client.rpc.response"
- `id` - A unique identifier for the message
- `methodLocalName` - The name of the method being called
- `serviceTypeName` - The name of the service being called
- `data` - The request or response data
- `error` - Optional error message

### GrpcServiceTransport

The `GrpcServiceTransport` interface defines a transport that can register service registries:

- `addServiceRegistry(serviceRegistry: ServiceRegistry): void` - Registers a service registry
- `removeServiceRegistry(serviceRegistry: ServiceRegistry): void` - Unregisters a service registry

### ServiceRegistry

The `ServiceRegistry` class provides a registry for service implementations with these key methods:

- `registerService<T extends DescService>(serviceDesc: T, impl: ServiceImpl<T>): void` - Registers a service implementation
- `getService(serviceTypeName: string): ServiceImplSpec | undefined` - Gets a service implementation by name
- `canHandle(message: GrpcSendMessageTransportPayload): boolean` - Checks if this registry can handle a message
- `handleRequest(message: GrpcSendMessageTransportPayload, sendResponse: (response: GrpcSendMessageTransportPayload) => void): Promise<void>` - Handles a request
- `static withGrpcServiceTransport(transport: GrpcServiceTransport): ServiceRegistry` - Creates a registry and adds it to a transport

### SendMessageTransport

The `SendMessageTransport` class implements both the Connect RPC `Transport` interface and the `GrpcServiceTransport` interface. It uses `sendMessage` for communication and delegates request handling to registered service registries.

## Usage Examples

### Basic Usage

```typescript
// Create a transport
const transport = new SendMessageTransport(messageTarget);

// Create a service registry and add it to the transport
const registry = ServiceRegistry.withGrpcServiceTransport(transport);

// Register a service with the registry
registry.registerService(MyService, {
  myMethod: async (request) => {
    // Implement the method
    return new MyResponse({ result: `Processed ${request.input}` });
  }
});

// Create a client using the transport
const client = createClient(MyService, transport);

// Make a request
const response = await client.myMethod({ input: "test" });
```

### Multiple Service Registries

```typescript
// Create a transport
const transport = new SendMessageTransport(messageTarget);

// Create service registries for different domains and add them to the transport
const authRegistry = ServiceRegistry.withGrpcServiceTransport(transport);
const dataRegistry = ServiceRegistry.withGrpcServiceTransport(transport);

// Register services with their respective registries
authRegistry.registerService(AuthService, authServiceImpl);
dataRegistry.registerService(DataService, dataServiceImpl);

// Create clients using the transport
const authClient = createClient(AuthService, transport);
const dataClient = createClient(DataService, transport);

// Make requests
const authResponse = await authClient.login({ username, password });
const dataResponse = await dataClient.getData({ id });
```

### Custom Middleware Pattern

```typescript
// Create a transport
const transport = new SendMessageTransport(messageTarget);

// Create a service registry
const registry = new ServiceRegistry();

// Register a service with the registry
registry.registerService(MyService, myServiceImpl);

// Create a logging middleware function
const withLogging = (registry: ServiceRegistry): ServiceRegistry => {
  const originalHandleRequest = registry.handleRequest.bind(registry);

  // Override the handleRequest method to add logging
  registry.handleRequest = async (message, sendResponse) => {
    console.log(`Handling request: ${message.serviceTypeName}.${message.methodLocalName}`);
    const start = Date.now();

    try {
      await originalHandleRequest(message, (response) => {
        console.log(`Request completed in ${Date.now() - start}ms`);
        sendResponse(response);
      });
    } catch (error) {
      console.error(`Request failed: ${error}`);
      throw error;
    }
  };

  return registry;
};

// Apply the logging middleware and add to transport
const loggingRegistry = withLogging(registry);
transport.addServiceRegistry(loggingRegistry);
```

## Architecture

The architecture follows a clean separation of concerns:

1. The `SendMessageTransport` handles communication details and delegates request handling to registered service registries.
2. The `ServiceRegistry` manages service implementations and handles method resolution and execution.
3. The `GrpcServiceTransport` interface defines how transports and service registries interact.

This design enables:
- Independent testing of components
- Multiple service registries per transport
- Adding cross-cutting concerns via middleware patterns
- Cleaner separation between communication and business logic

## Design Decisions

1. **Service Registry as a Class**: The `ServiceRegistry` is implemented as a class rather than an interface to provide a standard implementation that works for most use cases.

2. **Static Factory Method**: The `ServiceRegistry.withGrpcServiceTransport` static method provides a convenient way to create a registry and add it to a transport in one step.

3. **Transport Dependency**: The service registry depends on the transport (via the `GrpcServiceTransport` interface) rather than the other way around. This allows for a cleaner separation of concerns and makes it easier to test components in isolation.

4. **Message-Based Communication**: All communication is based on the `GrpcSendMessageTransportPayload` interface, which provides a standard format for requests and responses.

5. **Middleware Pattern**: The architecture supports middleware patterns through composition, allowing for cross-cutting concerns like logging, metrics, and error handling.
