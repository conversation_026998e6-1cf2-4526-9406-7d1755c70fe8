import {
  Transport,
  UnaryResponse,
  StreamResponse,
  ContextVal<PERSON>,
} from "@connectrpc/connect";
import {
  DescMessage,
  MessageInitShape,
  DescMethodUnary,
  DescMethodStreaming,
  from<PERSON>son,
  to<PERSON><PERSON>,
  create,
  type JsonValue,
} from "@bufbuild/protobuf";
import { MessageTarget } from "./message-target";
import { ServiceRegistry } from "./service-registry";

/**
 * Interface for gRPC messages sent over sendMessage
 */
export interface GrpcSendMessageTransportPayload {
  type:
    | "com.augmentcode.client.rpc.request"
    | "com.augmentcode.client.rpc.response";
  id: string;
  methodLocalName: string;
  serviceTypeName: string;
  data: unknown; // JSON object representing protobuf message
  timeout?: number;
  error?: string;
}

export interface GrpcServiceTransport {
  addServiceRegistry(serviceRegistry: ServiceRegistry): void;
  removeServiceRegistry(serviceRegistry: ServiceRegistry): void;
}

/**
 * A custom implementation of the Connect RPC Transport interface that uses
 * sendMessage for communication. This transport can be used on both the
 * extension and webview sides to enable bidirectional gRPC communication.
 */
export class SendMessageTransport implements Transport, GrpcServiceTransport {
  public static PROTOCOL_NAME = "com.augmentcode.client.rpc";

  private readonly pendingRequests = new Map<
    string,
    {
      resolve: (value: GrpcSendMessageTransportPayload) => void;
      reject: (reason: unknown) => void;
      timeout?: NodeJS.Timeout;
    }
  >();
  private readonly cleanup: () => void;
  private readonly serviceRegistries = new Set<ServiceRegistry>();

  /**
   * Creates a new SendMessageTransport
   * @param target The message target to use for communication
   */
  constructor(private readonly target: MessageTarget) {
    this.cleanup = this.target.onReceiveMessage(this.handleMessage.bind(this));
  }

  /**
   * Registers a service registry with this transport
   * @param serviceRegistry The service registry to register
   */
  addServiceRegistry(serviceRegistry: ServiceRegistry): void {
    this.serviceRegistries.add(serviceRegistry);
  }

  /**
   * Unregisters a service registry from this transport
   * @param serviceRegistry The service registry to unregister
   */
  removeServiceRegistry(serviceRegistry: ServiceRegistry): void {
    this.serviceRegistries.delete(serviceRegistry);
  }

  /**
   * Handles incoming messages from the message target
   */
  private handleMessage(message: unknown): void {
    if (
      !message ||
      typeof message !== "object" ||
      !this.isGrpcMessageLike(message)
    ) {
      return;
    }

    const grpcMessage = message as GrpcSendMessageTransportPayload;

    if (grpcMessage.type === "com.augmentcode.client.rpc.request") {
      void this.handleRequest(grpcMessage);
    } else if (grpcMessage.type === "com.augmentcode.client.rpc.response") {
      void this.handleResponse(grpcMessage);
    }
  }

  /**
   * Type guard to check if a message is a GrpcSendMessageTransportPayload
   */
  private isGrpcMessageLike(message: object): boolean {
    return (
      ("type" in message &&
        (message as { type: unknown }).type ===
          "com.augmentcode.client.rpc.request") ||
      (message as { type: unknown }).type ===
        "com.augmentcode.client.rpc.response"
    );
  }

  /**
   * Handles incoming request messages
   */
  private async handleRequest(
    message: GrpcSendMessageTransportPayload,
  ): Promise<void> {
    // Try each request handler until one succeeds
    for (const registry of this.serviceRegistries) {
      if (registry.canHandle(message)) {
        try {
          await registry.handleRequest(
            message,
            (response: GrpcSendMessageTransportPayload) => {
              this.target.sendMessage(response);
            },
          );
          return;
        } catch (error) {
          // If this is the last handler, send the error response
          if (
            Array.from(this.serviceRegistries).indexOf(registry) ===
            this.serviceRegistries.size - 1
          ) {
            this.target.sendMessage({
              type: "com.augmentcode.client.rpc.response",
              id: message.id,
              methodLocalName: message.methodLocalName,
              serviceTypeName: message.serviceTypeName,
              data: "",
              error: error instanceof Error ? error.message : String(error),
            });
          }
          // Otherwise, try the next handler
        }
      }
    }

    // If no handlers can handle the message, send an error response
    this.target.sendMessage({
      type: "com.augmentcode.client.rpc.response",
      id: message.id,
      methodLocalName: message.methodLocalName,
      serviceTypeName: message.serviceTypeName,
      data: "",
      error: `No handlers registered for service: ${message.serviceTypeName}`,
    });
  }

  /**
   * Handles incoming response messages
   */
  private handleResponse(message: GrpcSendMessageTransportPayload): void {
    const pendingRequest = this.pendingRequests.get(message.id);
    if (!pendingRequest) {
      return;
    }

    this.pendingRequests.delete(message.id);

    // Clear timeout
    clearTimeout(pendingRequest.timeout);

    if (message.error) {
      pendingRequest.reject(
        new Error(
          `gRPC server error for ${message.serviceTypeName}.${message.methodLocalName} (ID: ${message.id}): ${message.error}`,
        ),
      );
      return;
    }

    try {
      // Validate that the response has the expected structure
      if (!message.data && message.data !== null && message.data !== "") {
        throw new Error(
          `gRPC response missing data field for ${message.serviceTypeName}.${message.methodLocalName} (ID: ${message.id})`,
        );
      }

      pendingRequest.resolve(message);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      pendingRequest.reject(
        new Error(
          `Failed to process gRPC response for ${message.serviceTypeName}.${message.methodLocalName} (ID: ${message.id}): ${errorMessage}`,
        ),
      );
    }
  }

  /**
   * Sends a request message and returns a promise that resolves with the response
   */
  private sendRequest(
    message: GrpcSendMessageTransportPayload,
    timeoutMs?: number,
  ): Promise<GrpcSendMessageTransportPayload> {
    return new Promise((resolve, reject) => {
      let timeout: NodeJS.Timeout | undefined;

      // Set up timeout if specified
      if (timeoutMs) {
        timeout = setTimeout(() => {
          this.pendingRequests.delete(message.id);
          reject(
            new Error(
              `gRPC request timed out after ${timeoutMs}ms: ${message.serviceTypeName}.${message.methodLocalName} (ID: ${message.id}). This may indicate that the server is not responding or the message routing is broken.`,
            ),
          );
        }, timeoutMs);
      }

      this.pendingRequests.set(message.id, { resolve, reject, timeout });
      this.target.sendMessage(message);
    });
  }

  /**
   * Implements the Transport.unary method for making unary RPC calls
   */
  async unary<I extends DescMessage, O extends DescMessage>(
    method: DescMethodUnary<I, O>,
    signal: AbortSignal | undefined,
    timeoutMs: number | undefined,
    header: Record<string, string> | undefined,
    input: MessageInitShape<I>,
    _contextValues?: ContextValues,
  ): Promise<UnaryResponse<I, O>> {
    const id = crypto.randomUUID();
    const methodLocalName = method.localName;
    const serviceTypeName = method.parent.typeName;

    if (!serviceTypeName) {
      throw new Error("Service name is required for unary calls");
    }

    // Create the protobuf message and serialize to JSON
    const requestData = input
      ? toJson(method.input, create(method.input, input))
      : {};
    // Check if the request has been aborted
    if (signal?.aborted) {
      throw new Error(
        `gRPC request aborted before sending: ${serviceTypeName}.${methodLocalName} (ID: ${id})`,
      );
    }

    // Set up abort signal handling
    let abortListener: (() => void) | undefined;
    if (signal) {
      abortListener = () => {
        const pendingRequest = this.pendingRequests.get(id);
        if (pendingRequest) {
          this.pendingRequests.delete(id);
          clearTimeout(pendingRequest.timeout);
          pendingRequest.reject(
            new Error(
              `gRPC request aborted during execution: ${serviceTypeName}.${methodLocalName} (ID: ${id})`,
            ),
          );
        }
      };
      signal.addEventListener("abort", abortListener);
    }

    const responseData = await this.sendRequest(
      {
        type: "com.augmentcode.client.rpc.request",
        id,
        methodLocalName,
        serviceTypeName,
        data: requestData,
        timeout: timeoutMs,
      },
      timeoutMs,
    );

    // Clean up abort listener
    if (signal && abortListener) {
      signal.removeEventListener("abort", abortListener);
    }

    // Create a response object that satisfies the UnaryResponse interface
    const response: UnaryResponse<I, O> = {
      stream: false,
      method: method,
      service: method.parent,
      header: new Headers(header),
      message: fromJson(method.output, responseData.data as JsonValue),
      trailer: new Headers(),
    };

    return response;
  }

  /**
   * Implements the Transport.stream method for making streaming RPC calls
   * Note: This implementation does not support streaming yet
   */
  stream<I extends DescMessage, O extends DescMessage>(
    _method: DescMethodStreaming<I, O>,
    _signal?: AbortSignal,
    _timeoutMs?: number,
    _header?: Record<string, string>,
    _input?: AsyncIterable<MessageInitShape<I>>,
    _contextValues?: ContextValues,
  ): Promise<StreamResponse<I, O>> {
    throw new Error("Streaming is not supported by this transport");
  }

  /**
   * Disposes of the transport, cleaning up any resources
   */
  dispose(): void {
    this.cleanup();

    // Clear any pending timeouts
    for (const { timeout } of this.pendingRequests.values()) {
      clearTimeout(timeout);
    }

    this.pendingRequests.clear();
    this.serviceRegistries.clear();
  }
}
