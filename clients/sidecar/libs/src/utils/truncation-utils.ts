import { truncate<PERSON>iddleHelper } from "./strings";
import {
  TruncatedContentMetadata,
  TruncatedContentType,
  UntruncatedContentManager,
} from "./untruncated-content-manager";

/**
 * Options for truncating content
 */
export interface TruncateOptions {
  /** Maximum length in bytes */
  maxBytes: number;
  /** Type of content being truncated */
  contentType: TruncatedContentType;
  /** Tool use ID if applicable */
  toolUseId?: string;
  /** Request ID if applicable */
  requestId?: string;
  /** Conversation ID if applicable */
  conversationId?: string;
}

/**
 * Result of truncating content
 */
export interface TruncateResult {
  /** The truncated content */
  truncatedContent: string;
  /** Metadata about the truncation */
  metadata?: TruncatedContentMetadata;
}

/**
 * Truncates content in the middle and keep some metadata
 *
 * @param content The content to truncate
 * @param options Truncation options
 * @param contentManager The untruncated content manager
 * @param enableUntruncatedContentStorage Whether to store untruncated content (feature flag)
 * @returns The truncated content with metadata and the metadata object
 */
export async function truncateWithMetadata(
  content: string,
  options: TruncateOptions,
  contentManager: UntruncatedContentManager,
  enableUntruncatedContentStorage: boolean = false,
): Promise<TruncateResult> {
  const lines = content.split("\n");
  const totalLines = lines.length;
  const totalSize = content.length;

  if (totalSize <= options.maxBytes) {
    return {
      truncatedContent: content,
    };
  }

  const { truncatedText, shownRangeWhenTruncated } = truncateMiddleHelper(
    content,
    options.maxBytes,
  );

  // Conditionally store the full content based on feature flag
  if (enableUntruncatedContentStorage) {
    const metadata = await contentManager.storeUntruncatedContent(
      content,
      options.contentType,
      shownRangeWhenTruncated || [1, totalLines],
      options.toolUseId,
      options.requestId,
      options.conversationId,
    );

    // Add standardized metadata footer
    const metadataFooter = createTruncationFooter(metadata);
    const truncatedContent = `${truncatedText}\n\n${metadataFooter}`;

    return {
      truncatedContent,
      metadata,
    };
  } else {
    // Return truncated content without metadata when storage is disabled
    return {
      truncatedContent: truncatedText,
    };
  }
}

/**
 * Creates a standardized footer for truncated content
 *
 * @param metadata The truncation metadata
 * @returns A formatted footer string
 */
export function createTruncationFooter(
  metadata: TruncatedContentMetadata,
): string {
  const { shownRange, totalLines, referenceId } = metadata;

  // Check if we have a complex range (showing both beginning and end)
  let rangeText: string;
  if (shownRange.length === 4) {
    // Format as "lines 1-50, 150-200 of 200 lines"
    rangeText = `lines ${shownRange[0]}-${shownRange[1]}, ${shownRange[2]}-${shownRange[3]} of ${totalLines} lines`;
  } else {
    // Format as "lines 1-50 of 200 lines"
    rangeText = `lines ${shownRange[0]}-${shownRange[1]} of ${totalLines} lines`;
  }

  return (
    `[This result was truncated. Showing ${rangeText}. ` +
    `Use view-range-untruncated or search-untruncated tools to access the full content. ` +
    `Reference ID: ${referenceId}]`
  );
}
