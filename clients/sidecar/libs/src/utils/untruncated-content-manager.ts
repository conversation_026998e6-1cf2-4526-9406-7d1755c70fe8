import { IPluginFileStore } from "../client-interfaces/plugin-file-store";
import { v4 as uuidv4 } from "uuid";
import { getLogger } from "../logging";

/**
 * Types of content that can be truncated
 */
export enum TruncatedContentType {
  ToolOutput = "tool-output",
  UserMessage = "user-message",
  ConversationHistory = "conversation-history",
}

/**
 * Metadata about truncated content
 */
export interface TruncatedContentMetadata {
  /** Type of the truncated content */
  contentType: TruncatedContentType;
  /** Total size of the untruncated content in bytes */
  totalSize: number;
  /** Total number of lines in the untruncated content */
  totalLines: number;
  /**
   * Range of lines shown in the truncated content (1-based, inclusive)
   * Can be either [start, end] for a single range or [start1, end1, start2, end2] for two ranges
   */
  shownRange: [number, number] | [number, number, number, number];
  /** Reference ID for the truncated content */
  referenceId: string;
  /** Associated tool use ID if applicable */
  toolUseId?: string;
  /** Associated request ID if applicable */
  requestId?: string;
  /** Associated conversation ID if applicable */
  conversationId?: string;
  /** Timestamp when the content was stored */
  timestamp: number;
}

/**
 * Result of a search in untruncated content
 */
export interface SearchResult {
  /** The matching lines with context */
  content: string;
  /** The line numbers of the matching content (1-based, inclusive) */
  matchedRange: [number, number];
  /** Total number of matches found */
  totalMatches: number;
}

/**
 * Manager for storing and retrieving untruncated content
 */
export class UntruncatedContentManager {
  private static readonly ASSET_PREFIX = "untruncated";
  private readonly _logger = getLogger("UntruncatedContentManager");

  constructor(private readonly _fileStore: IPluginFileStore) {}

  /**
   * Store untruncated content and return metadata
   *
   * @param content The full untruncated content
   * @param contentType The type of content being stored
   * @param shownRange The range of lines shown in the truncated version (1-based, inclusive)
   * @param toolUseId Optional tool use ID
   * @param requestId Optional request ID
   * @param conversationId Optional conversation ID
   * @returns Metadata about the stored content
   */
  public async storeUntruncatedContent(
    content: string,
    contentType: TruncatedContentType,
    shownRange: [number, number] | [number, number, number, number],
    toolUseId?: string,
    requestId?: string,
    conversationId?: string,
  ): Promise<TruncatedContentMetadata> {
    const referenceId = uuidv4();
    const lines = content.split("\n");
    const totalLines = lines.length;
    const totalSize = content.length;

    const metadata: TruncatedContentMetadata = {
      contentType,
      totalSize,
      totalLines,
      shownRange,
      referenceId,
      toolUseId,
      requestId,
      conversationId,
      timestamp: Date.now(),
    };

    // Store the content
    const assetPath = this.getAssetPath(referenceId);
    await this._fileStore.saveAsset(
      assetPath,
      new TextEncoder().encode(content),
    );

    // Store the metadata
    const metadataPath = this.getMetadataPath(referenceId);
    await this._fileStore.saveAsset(
      metadataPath,
      new TextEncoder().encode(JSON.stringify(metadata)),
    );

    return metadata;
  }

  /**
   * Get a range of content from untruncated content
   *
   * @param referenceId The reference ID of the content
   * @param startLine The starting line (1-based, inclusive)
   * @param endLine The ending line (1-based, inclusive)
   * @returns The requested range of content or undefined if not found
   */
  public async getContentRange(
    referenceId: string,
    startLine: number,
    endLine: number,
  ): Promise<string | undefined> {
    const content = await this.getFullContent(referenceId);
    if (!content) {
      return undefined;
    }

    const lines = content.split("\n");
    const totalLines = lines.length;

    // Validate and adjust range
    startLine = Math.max(1, Math.min(startLine, totalLines));
    endLine = Math.max(startLine, Math.min(endLine, totalLines));

    // Return the requested range (adjusting for 0-based array)
    return lines.slice(startLine - 1, endLine).join("\n");
  }

  /**
   * Search for a term in untruncated content
   *
   * @param referenceId The reference ID of the content
   * @param searchTerm The term to search for
   * @param contextLines Number of context lines to include before and after matches
   * @returns Search results or undefined if content not found
   */
  public async searchContent(
    referenceId: string,
    searchTerm: string,
    contextLines: number = 2,
  ): Promise<SearchResult | undefined> {
    const content = await this.getFullContent(referenceId);
    if (!content) {
      return undefined;
    }

    const lines = content.split("\n");
    const matches: number[] = [];

    // Find all matching lines
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].toLowerCase().includes(searchTerm.toLowerCase())) {
        matches.push(i);
      }
    }

    if (matches.length === 0) {
      return {
        content: `No matches found for "${searchTerm}"`,
        matchedRange: [0, 0],
        totalMatches: 0,
      };
    }

    // Group nearby matches to avoid duplicating context
    const groups: [number, number][] = [];
    let currentGroup: [number, number] | null = null;

    for (const match of matches) {
      if (currentGroup === null) {
        currentGroup = [match, match];
      } else if (match <= currentGroup[1] + contextLines * 2) {
        // Extend the current group
        currentGroup[1] = match;
      } else {
        // Start a new group
        groups.push(currentGroup);
        currentGroup = [match, match];
      }
    }

    if (currentGroup !== null) {
      groups.push(currentGroup);
    }

    // Build the result with context
    const resultLines: string[] = [];
    let firstMatchLine = Number.MAX_SAFE_INTEGER;
    let lastMatchLine = 0;

    for (const [groupStart, groupEnd] of groups) {
      const contextStart = Math.max(0, groupStart - contextLines);
      const contextEnd = Math.min(lines.length - 1, groupEnd + contextLines);

      firstMatchLine = Math.min(firstMatchLine, contextStart + 1);
      lastMatchLine = Math.max(lastMatchLine, contextEnd + 1);

      // Add a separator if this isn't the first group
      if (resultLines.length > 0) {
        resultLines.push("...");
      }

      // Add lines with context
      for (let i = contextStart; i <= contextEnd; i++) {
        const prefix = matches.includes(i) ? ">> " : "   ";
        resultLines.push(`${prefix}${i + 1}: ${lines[i]}`);
      }
    }

    return {
      content: resultLines.join("\n"),
      matchedRange: [firstMatchLine, lastMatchLine],
      totalMatches: matches.length,
    };
  }

  /**
   * Get metadata for untruncated content
   *
   * @param referenceId The reference ID of the content
   * @returns The metadata or undefined if not found
   */
  public async getMetadata(
    referenceId: string,
  ): Promise<TruncatedContentMetadata | undefined> {
    const metadataPath = this.getMetadataPath(referenceId);
    const metadataBytes = await this._fileStore.loadAsset(metadataPath);

    if (!metadataBytes) {
      return undefined;
    }

    try {
      const metadataJson = new TextDecoder().decode(metadataBytes);
      return JSON.parse(metadataJson) as TruncatedContentMetadata;
    } catch (error) {
      this._logger.debug("Failed to parse metadata:", error);
      return undefined;
    }
  }

  /**
   * Get the full untruncated content
   *
   * @param referenceId The reference ID of the content
   * @returns The full content or undefined if not found
   */
  private async getFullContent(
    referenceId: string,
  ): Promise<string | undefined> {
    const assetPath = this.getAssetPath(referenceId);
    const contentBytes = await this._fileStore.loadAsset(assetPath);

    if (!contentBytes) {
      return undefined;
    }

    return new TextDecoder().decode(contentBytes);
  }

  /**
   * Get the asset path for content
   */
  private getAssetPath(referenceId: string): string {
    return `${UntruncatedContentManager.ASSET_PREFIX}/content/${referenceId}.txt`;
  }

  /**
   * Get the asset path for metadata
   */
  private getMetadataPath(referenceId: string): string {
    return `${UntruncatedContentManager.ASSET_PREFIX}/metadata/${referenceId}.json`;
  }
}
