import { ChildProcess, execSync, spawn } from "child_process";
import { getLogger } from "../../logging";
import { truncateMiddle } from "../../utils/strings";
import { ShellConfig } from "../tool-types";

/**
 * A helper class to manage child processes (non-VSCode terminal) and their output
 */
interface ShellProcess {
  process: ChildProcess;
  command: string;
  outputChunks: string[];
  killed: boolean;
  exitCode: number | null;
  cwd: string;
}

/**
 * A helper class to manage child processes (non-VSCode terminal) and their output
 */
export class ShellProcessTools {
  private readonly _maxOutputLength = 63 * 1024; // 64KiB - 1KiB buffer for additional text from the tools themselves

  private readonly _processes = new Map<number, ShellProcess>();
  private readonly _waitResolvers = new Map<
    number,
    (result: { output: string; returnCode: number | null }) => void
  >();
  private _nextId = 1;
  private readonly _logger = getLogger("ShellProcessTools");

  constructor(private readonly _shellInfo: ShellConfig) {}

  public launch(
    command: string,
    cwd: string | undefined,
    abortSignal: AbortSignal,
  ): Promise<number> {
    const id = this._nextId++;
    const env = { ...process.env, ...this._shellInfo.env };

    // Create the child process
    const childProcess = spawn(command, [], {
      cwd: cwd ?? process.cwd(),
      env,
      stdio: ["pipe", "pipe", "pipe"],
      shell: this._shellInfo.path ?? this._shellInfo.name,
      signal: abortSignal,
      // On Unix-like systems (Linux, macOS), detach the process so it gets its own process group
      detached: process.platform === "linux" || process.platform === "darwin",
    });

    // Collect stdout and stderr
    // TODO: Handle large output by only storing N/2 at start and end.
    if (childProcess.stdout) {
      childProcess.stdout.on("data", (data: Buffer) => {
        this._processes.get(id)!.outputChunks.push(data.toString());
      });
    }
    if (childProcess.stderr) {
      childProcess.stderr.on("data", (data: Buffer) => {
        this._processes.get(id)!.outputChunks.push(data.toString());
      });
    }

    // Handle process exit
    childProcess.on("exit", (code) => {
      this._logger.verbose(`Process ${id} exited with code ${code}`);
      const process = this._processes.get(id);
      if (process) {
        // This may have already been set (e.g., by the kill tool), and we don't want to
        // overwrite that value (since this has less information).
        if (process.exitCode === null) {
          process.exitCode = code;
        }
        const output = truncateMiddle(
          process.outputChunks.join(""),
          this._maxOutputLength,
        );

        // Resolve any waiting promises
        const resolver = this._waitResolvers.get(id);
        if (resolver) {
          resolver({
            output: output,
            returnCode: process.exitCode,
          });
          this._waitResolvers.delete(id);
        }
      }
    });

    // Handle process errors
    childProcess.on("error", (error) => {
      this._logger.verbose(`Process ${id} error: ${error.message}`);
      const process = this._processes.get(id);
      if (process) {
        process.killed = true;
        // This may have already been set (e.g., by the kill tool), and we don't want to
        // overwrite that value (since this has less information).
        if (process.exitCode === null) {
          process.exitCode = -1;
        }
        process.outputChunks.push(`\nProcess error: ${error.message}`);
        const output = truncateMiddle(
          process.outputChunks.join(""),
          this._maxOutputLength,
        );

        // Resolve any waiting promises
        const resolver = this._waitResolvers.get(id);
        if (resolver) {
          resolver({
            output: output,
            returnCode: process.exitCode,
          });
          this._waitResolvers.delete(id);
        }
      }
    });

    const processInfo: ShellProcess = {
      process: childProcess,
      command,
      outputChunks: [],
      killed: false,
      exitCode: null,
      cwd: cwd ?? process.cwd(),
    };
    this._processes.set(id, processInfo);

    // Handle abort signal
    abortSignal.addEventListener("abort", () => {
      void this.kill(id);
    });

    return Promise.resolve(id);
  }

  public kill(
    id: number,
  ): Promise<
    { output: string; killed: boolean; returnCode: number | null } | undefined
  > {
    const proc = this._processes.get(id);
    if (!proc) {
      return Promise.resolve(undefined);
    }
    if (proc.killed) {
      return Promise.resolve({
        output: proc.outputChunks.join(""),
        killed: false,
        returnCode: proc.exitCode,
      });
    }

    this._logger.verbose(`Killing process ${id}`);
    try {
      if (process.platform === "linux" || process.platform === "darwin") {
        // On Unix-like systems, kill the entire process group using negative PID
        execSync(`kill -9 -${proc.process.pid}`);
      } else {
        // For Windows, just kill the process directly
        if (proc) {
          proc.process.kill();
        }
      }
    } catch (e) {
      // Process might already be gone
    }

    proc.killed = true;
    proc.exitCode = -1;

    return Promise.resolve({
      output: proc.outputChunks.join(""),
      killed: true,
      returnCode: proc.exitCode,
    });
  }

  public readOutput(
    id: number,
  ): Promise<{ output: string; returnCode: number | null } | undefined> {
    const process = this._processes.get(id);
    if (!process) {
      return Promise.resolve(undefined);
    }

    return Promise.resolve({
      output: process.outputChunks.join(""),
      returnCode: process.exitCode,
    });
  }

  public writeInput(id: number, input: string): boolean {
    const process = this._processes.get(id);
    if (!process || process.killed || !process.process.stdin) {
      return false;
    }
    process.process.stdin.write(input);
    return true;
  }

  public listProcesses(): {
    id: number;
    command: string;
    state: "running" | "completed" | "killed";
    returnCode: number | null;
  }[] {
    const result = [];
    for (const [id, process] of this._processes.entries()) {
      const state: "running" | "completed" | "killed" =
        process.killed || process.exitCode !== null
          ? process.exitCode === -1
            ? "killed"
            : "completed"
          : "running";
      result.push({
        id,
        command: process.command,
        state,
        returnCode: process.exitCode,
      });
    }
    return result;
  }

  public waitForProcess(
    id: number,
    timeoutSeconds: number,
    abortSignal: AbortSignal,
  ): Promise<{ output: string; returnCode: number | null }> {
    return new Promise((resolve) => {
      const process = this._processes.get(id);
      if (!process) {
        resolve({ output: "", returnCode: null });
        return;
      }

      // If process already completed, return immediately
      if (process.exitCode !== null) {
        resolve({
          output: process.outputChunks.join(""),
          returnCode: process.exitCode,
        });
        return;
      }

      const timer = setTimeout(() => {
        // Process still running after timeout
        this._logger.verbose(
          `Process ${id} still running after ${timeoutSeconds} seconds. Timing out.`,
        );
        this._waitResolvers.delete(id);
        resolve({
          output: process.outputChunks.join(""),
          returnCode: null,
        });
      }, timeoutSeconds * 1000);

      // Store resolver to be called when process completes
      this._waitResolvers.set(id, (result) => {
        clearTimeout(timer);
        this._waitResolvers.delete(id);
        resolve(result);
      });

      const abortHandler = () => {
        clearTimeout(timer);
        this._waitResolvers.delete(id);
        resolve({ output: process.outputChunks.join(""), returnCode: null });
      };
      abortSignal.addEventListener("abort", abortHandler);
    });
  }

  public closeAllProcesses() {
    // Clean up all processes
    for (const process of this._processes.values()) {
      if (!process.killed) {
        process.process.kill("SIGTERM");
      }
    }
    this._processes.clear();
    this._waitResolvers.clear();
  }

  public cleanup() {
    this.closeAllProcesses();
  }

  public get shellName() {
    return this._shellInfo.name;
  }
}
