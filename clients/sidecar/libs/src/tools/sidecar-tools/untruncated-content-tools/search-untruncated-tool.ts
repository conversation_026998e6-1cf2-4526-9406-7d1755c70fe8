import { ToolBase, ToolSafety, ToolUseResponse } from "../../tool-types";
import { Exchange } from "../../../chat/chat-types";
import { UntruncatedContentManager } from "../../../utils/untruncated-content-manager";
import { SidecarToolType } from "../sidecar-tool-types";

/**
 * Tool for searching within untruncated content
 */
export class SearchUntruncatedTool extends ToolBase<SidecarToolType> {
  public description = "Search for a term within untruncated content";

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      reference_id: {
        type: "string",
        description:
          "The reference ID of the truncated content (found in the truncation footer)",
      },
      search_term: {
        type: "string",
        description: "The term to search for within the content",
      },
      context_lines: {
        type: "integer",
        description:
          "Number of context lines to include before and after matches (default: 2)",
      },
    },
    required: ["reference_id", "search_term"],
  });

  constructor(private readonly _contentManager: UntruncatedContentManager) {
    super(SidecarToolType.searchUntruncated, ToolSafety.Safe);
  }

  public checkToolCallSafe(): boolean {
    return true;
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    try {
      const referenceId = toolInput.reference_id as string;
      const searchTerm = toolInput.search_term as string;
      const contextLines = (toolInput.context_lines as number) || 2;

      // Get metadata to validate the reference ID
      const metadata = await this._contentManager.getMetadata(referenceId);
      if (!metadata) {
        return {
          text: `Error: Content with reference ID '${referenceId}' not found`,
          isError: true,
        };
      }

      // Search the content
      const searchResult = await this._contentManager.searchContent(
        referenceId,
        searchTerm,
        contextLines,
      );
      if (!searchResult) {
        return {
          text: `Error: Failed to search content for reference ID '${referenceId}'`,
          isError: true,
        };
      }

      // Format the response
      const { content, totalMatches, matchedRange } = searchResult;

      const searchInfo =
        totalMatches > 0
          ? `Found ${totalMatches} matches for "${searchTerm}" in lines ${matchedRange[0]}-${matchedRange[1]} of ${metadata.totalLines} total lines`
          : `No matches found for "${searchTerm}" in ${metadata.totalLines} total lines`;

      return {
        text: `${searchInfo}\n\n${content}`,
        isError: false,
      };
    } catch (error) {
      return {
        text: `Error: ${error instanceof Error ? error.message : String(error)}`,
        isError: true,
      };
    }
  }
}
