/**
 * @file task-tools-utils.test.ts
 * Tests for task tools utility functions, especially UUID conversion functionality.
 */

import { TaskState } from "../../../agent/task/task-types";
import {
  validateTaskUpdateInput,
  validateTaskInput,
  validateTaskState,
  buildUpdateObject,
} from "../task-tools-utils";

// Mock the fromShortUuid function
jest.mock("../../../agent/task/task-utils", () => ({
  fromShortUuid: jest.fn((shortId: string) => {
    // Mock implementation: if it looks like a short UUID, convert it to a full UUID
    if (shortId.length < 20 && shortId.includes("-")) {
      return `full-uuid-${shortId}`;
    }
    return shortId;
  }),
}));

describe("Task Tools Utils", () => {
  describe("validateTaskUpdateInput", () => {
    it("should convert short UUID to full UUID for task_id", () => {
      const input = {
        task_id: "short-uuid-123",
        state: "IN_PROGRESS",
      };

      const result = validateTaskUpdateInput(input);

      expect(result.taskId).toBe("full-uuid-short-uuid-123");
      expect(result.state).toBe(TaskState.IN_PROGRESS);
    });

    it("should handle full UUID without conversion", () => {
      const fullUuid = "01234567-89ab-cdef-0123-456789abcdef";
      const input = {
        task_id: fullUuid,
        name: "Test Task",
      };

      const result = validateTaskUpdateInput(input);

      expect(result.taskId).toBe(fullUuid);
      expect(result.name).toBe("Test Task");
    });

    it("should throw error for missing task_id", () => {
      const input = {
        state: "IN_PROGRESS",
      };

      expect(() => validateTaskUpdateInput(input)).toThrow(
        "Task ID is required and must be a non-empty string",
      );
    });

    it("should throw error for empty task_id", () => {
      const input = {
        task_id: "",
        state: "IN_PROGRESS",
      };

      expect(() => validateTaskUpdateInput(input)).toThrow(
        "Task ID is required and must be a non-empty string",
      );
    });

    it("should validate all fields correctly", () => {
      const input = {
        task_id: "short-uuid-456",
        state: "COMPLETE",
        name: "Updated Task",
        description: "Updated description",
      };

      const result = validateTaskUpdateInput(input);

      expect(result.taskId).toBe("full-uuid-short-uuid-456");
      expect(result.state).toBe(TaskState.COMPLETE);
      expect(result.name).toBe("Updated Task");
      expect(result.description).toBe("Updated description");
    });
  });

  describe("validateTaskInput", () => {
    it("should convert short UUIDs to full UUIDs for parent and after task IDs", () => {
      const input = {
        name: "New Task",
        description: "Task description",
        parent_task_id: "short-parent-123",
        after_task_id: "short-after-456",
      };

      const result = validateTaskInput(input);

      expect(result.name).toBe("New Task");
      expect(result.description).toBe("Task description");
      expect(result.parentTaskId).toBe("full-uuid-short-parent-123");
      expect(result.afterTaskId).toBe("full-uuid-short-after-456");
    });

    it("should handle undefined parent and after task IDs", () => {
      const input = {
        name: "New Task",
        description: "Task description",
      };

      const result = validateTaskInput(input);

      expect(result.name).toBe("New Task");
      expect(result.description).toBe("Task description");
      expect(result.parentTaskId).toBeUndefined();
      expect(result.afterTaskId).toBeUndefined();
    });

    it("should handle full UUIDs without conversion", () => {
      const parentUuid = "01234567-89ab-cdef-0123-456789abcdef";
      const afterUuid = "fedcba98-7654-3210-fedc-ba9876543210";
      const input = {
        name: "New Task",
        description: "Task description",
        parent_task_id: parentUuid,
        after_task_id: afterUuid,
      };

      const result = validateTaskInput(input);

      expect(result.parentTaskId).toBe(parentUuid);
      expect(result.afterTaskId).toBe(afterUuid);
    });

    it("should throw error for missing name", () => {
      const input = {
        description: "Task description",
      };

      expect(() => validateTaskInput(input)).toThrow(
        "Name is required and must be a non-empty string",
      );
    });

    it("should throw error for missing description", () => {
      const input = {
        name: "New Task",
      };

      expect(() => validateTaskInput(input)).toThrow(
        "Description is required and must be a non-empty string",
      );
    });
  });

  describe("validateTaskState", () => {
    it("should return NOT_STARTED as default", () => {
      const result = validateTaskState({});
      expect(result).toBe(TaskState.NOT_STARTED);
    });

    it("should validate valid states", () => {
      expect(validateTaskState({ state: "IN_PROGRESS" })).toBe(
        TaskState.IN_PROGRESS,
      );
      expect(validateTaskState({ state: "COMPLETE" })).toBe(TaskState.COMPLETE);
      expect(validateTaskState({ state: "CANCELLED" })).toBe(
        TaskState.CANCELLED,
      );
    });

    it("should throw error for invalid state", () => {
      expect(() => validateTaskState({ state: "INVALID_STATE" })).toThrow(
        "Invalid state: INVALID_STATE",
      );
    });
  });

  describe("buildUpdateObject", () => {
    it("should build update object with all fields", () => {
      const validatedInput = {
        taskId: "test-uuid",
        state: TaskState.IN_PROGRESS,
        name: "Updated Name",
        description: "Updated Description",
      };

      const result = buildUpdateObject(validatedInput);

      expect(result).toEqual({
        state: TaskState.IN_PROGRESS,
        name: "Updated Name",
        description: "Updated Description",
      });
    });

    it("should build update object with only defined fields", () => {
      const validatedInput = {
        taskId: "test-uuid",
        state: TaskState.COMPLETE,
        name: undefined,
        description: "Updated Description",
      };

      const result = buildUpdateObject(validatedInput);

      expect(result).toEqual({
        state: TaskState.COMPLETE,
        description: "Updated Description",
      });
      expect(result.name).toBeUndefined();
    });

    it("should throw error when no updates provided", () => {
      const validatedInput = {
        taskId: "test-uuid",
        state: undefined,
        name: undefined,
        description: undefined,
      };

      expect(() => buildUpdateObject(validatedInput)).toThrow(
        "At least one property (state, name, description) must be provided to update",
      );
    });
  });
});
