<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import InlineEditTextAugment from "$common-webviews/src/common/components/InlineEditTextAugment.svelte";
  import type { TextSize } from "$common-webviews/src/design-system/components/TextAugment.svelte";

  // Story controls
  export let value: string = "Click to edit this text";
  export let placeholder: string = "Enter text here...";
  export let disabled: boolean = false;
  export let size: TextSize = 2;
  export let isEditable: boolean = true;
  export let multiline: boolean = true;
  export let showCommitButton: boolean = true;
  export let commitOnBlur: boolean = true;
  export let commitOnEnter: boolean = true;
  export let allowCtrlEnterForNewline: boolean = true;
  export let doubleClickToEdit: boolean = true;
  export let commitButtonVariant: "classic" | "ghost" | "surface" = "classic";
  export let commitButtonColor: "accent" | "neutral" | "success" | "warning" | "error" = "accent";

  // Demo states
  const sizeVariants: TextSize[] = [1, 2, 3, 4];
  const commitButtonVariants: ("classic" | "ghost" | "surface")[] = ["classic", "ghost", "surface"];
  const commitButtonColors: ("accent" | "neutral" | "success" | "warning" | "error")[] = [
    "accent",
    "neutral",
    "success",
    "warning",
    "error",
  ];

  // Event handlers for demo
  function handleChange(event: CustomEvent<{ oldValue: string; newValue: string }>) {
    console.log("Text changed:", event.detail);
  }

  function handleStartEdit(event: CustomEvent<{ value: string }>) {
    console.log("Started editing:", event.detail);
  }

  function handleCancelEdit(event: CustomEvent<{ value: string }>) {
    console.log("Cancelled editing:", event.detail);
  }
</script>

<ColumnLayout>
  <h2>InlineEditTextAugment Component</h2>
  <p>
    A flexible inline text editing component that supports both single-line and multiline editing
    with customizable behavior and styling options.
  </p>

  <h3>Interactive Example</h3>
  <div
    style="max-width: 400px; padding: 16px; border: 1px solid var(--ds-color-neutral-6); border-radius: 8px;"
  >
    <InlineEditTextAugment
      {value}
      {placeholder}
      {disabled}
      {size}
      {isEditable}
      {multiline}
      {showCommitButton}
      {commitOnBlur}
      {commitOnEnter}
      {allowCtrlEnterForNewline}
      {doubleClickToEdit}
      {commitButtonVariant}
      {commitButtonColor}
      on:change={handleChange}
      on:startEdit={handleStartEdit}
      on:cancelEdit={handleCancelEdit}
    />
  </div>

  <h3>Size Variants</h3>
  {#each sizeVariants as sizeVariant}
    <div style="margin-bottom: 12px;">
      <strong>Size {sizeVariant}:</strong>
      <InlineEditTextAugment value="Sample text for size {sizeVariant}" size={sizeVariant} />
    </div>
  {/each}

  <h3>Behavior Variants</h3>

  <div style="margin-bottom: 12px;">
    <strong>Single-line mode:</strong>
    <InlineEditTextAugment value="Single-line text editing" multiline={false} />
  </div>

  <div style="margin-bottom: 12px;">
    <strong>No commit button:</strong>
    <InlineEditTextAugment value="Edit without commit button" showCommitButton={false} />
  </div>

  <div style="margin-bottom: 12px;">
    <strong>No commit on blur:</strong>
    <InlineEditTextAugment value="Must use Enter or button to commit" commitOnBlur={false} />
  </div>

  <div style="margin-bottom: 12px;">
    <strong>Click to edit (no double-click):</strong>
    <InlineEditTextAugment value="Single click to edit" doubleClickToEdit={false} />
  </div>

  <div style="margin-bottom: 12px;">
    <strong>Disabled state:</strong>
    <InlineEditTextAugment value="This text cannot be edited" disabled={true} />
  </div>

  <div style="margin-bottom: 12px;">
    <strong>Not editable:</strong>
    <InlineEditTextAugment value="This text is read-only" isEditable={false} />
  </div>

  <h3>Commit Button Variants</h3>
  {#each commitButtonVariants as variant}
    <div style="margin-bottom: 12px;">
      <strong>{variant} variant:</strong>
      <InlineEditTextAugment
        value="Text with {variant} commit button"
        commitButtonVariant={variant}
      />
    </div>
  {/each}

  <h3>Commit Button Colors</h3>
  {#each commitButtonColors as color}
    <div style="margin-bottom: 12px;">
      <strong>{color} color:</strong>
      <InlineEditTextAugment value="Text with {color} commit button" commitButtonColor={color} />
    </div>
  {/each}

  <h3>Empty State</h3>
  <div style="margin-bottom: 12px;">
    <InlineEditTextAugment value="" placeholder="This is placeholder text..." />
  </div>

  <h3>Long Text Example</h3>
  <div style="max-width: 300px; margin-bottom: 12px;">
    <InlineEditTextAugment
      value="This is a very long text that should demonstrate text overflow behavior and how the component handles lengthy content that exceeds the container width."
    />
  </div>

  <h3>Usage Notes</h3>
  <ul>
    <li>
      <strong>Double-click</strong> the text to start editing (when doubleClickToEdit is true)
    </li>
    <li><strong>Enter</strong> to commit changes (Ctrl+Enter for new line in multiline mode)</li>
    <li><strong>Escape</strong> to cancel editing</li>
    <li><strong>Click outside</strong> to commit (when commitOnBlur is true)</li>
    <li><strong>Check button</strong> to explicitly commit changes</li>
  </ul>
</ColumnLayout>
