<script>
  import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

  /* eslint-disable @typescript-eslint/naming-convention */
</script>

<Fieldset title="Task Management Tools">
  <div class="l-task-tools">
    <h2>View Task List</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "task-view-1",
        tool_name: SidecarToolType.viewTaskList,
        input_json: JSON.stringify({}),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "task-req-1",
        toolUseId: "task-view-1",
        result: {
          text: `# Current Task List

[ ] UUID:root-1 NAME:Project Development DESCRIPTION:Main project task
-[x] UUID:task-1 NAME:Setup development environment DESCRIPTION:Configure dev tools and dependencies
-[/] UUID:task-2 NAME:Implement core features DESCRIPTION:Build the main functionality
--[ ] UUID:subtask-1 NAME:Design API endpoints DESCRIPTION:Define REST API structure
--[/] UUID:subtask-2 NAME:Create database schema DESCRIPTION:Design data models
--[ ] UUID:subtask-3 NAME:Implement authentication DESCRIPTION:Add user login system
-[ ] UUID:task-3 NAME:Write documentation DESCRIPTION:Create user and developer docs
-[-] UUID:task-4 NAME:Legacy feature migration DESCRIPTION:This was cancelled

To update this task list, use the update_tasklist tool.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="task-req-1"
    />

    <h2>Update Tasks - Single Task</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "update-single-1",
        tool_name: SidecarToolType.updateTasks,
        input_json: JSON.stringify({
          task_id: "task-123",
          state: "COMPLETE",
          name: "Updated task name",
          description: "Updated task description",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "update-req-1",
        toolUseId: "update-single-1",
        result: {
          text: `Task list updated successfully. Created: 0, Updated: 1, Deleted: 0.

# Task Changes

## Updated Tasks

[x] UUID:task-123 NAME:Updated task name DESCRIPTION:Updated task description

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="update-req-1"
    />

    <h2>Update Tasks - Batch Update</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "update-batch-1",
        tool_name: SidecarToolType.updateTasks,
        input_json: JSON.stringify({
          tasks: [
            { task_id: "task-1", state: "COMPLETE" },
            { task_id: "task-2", state: "IN_PROGRESS" },
            { task_id: "task-3", state: "CANCELLED" },
          ],
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "update-req-2",
        toolUseId: "update-batch-1",
        result: {
          text: `Task list updated successfully. Created: 0, Updated: 3, Deleted: 0.

# Task Changes

## Updated Tasks

[x] UUID:task-1 NAME:Complete the first task DESCRIPTION:This task has been completed
[/] UUID:task-2 NAME:Work on second task DESCRIPTION:This task is now in progress
[-] UUID:task-3 NAME:Cancelled task DESCRIPTION:This task was cancelled

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="update-req-2"
    />

    <h2>Add Tasks - Single Task Creation</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "add-single-1",
        tool_name: SidecarToolType.addTasks,
        input_json: JSON.stringify({
          name: "New important task",
          description: "This is a newly created task that needs attention",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "add-req-1",
        toolUseId: "add-single-1",
        result: {
          text: `Task list updated successfully. Created: 1, Updated: 0, Deleted: 0.

# Task Changes

## Created Tasks

[ ] UUID:new-task-1 NAME:New important task DESCRIPTION:This is a newly created task that needs attention

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="add-req-1"
    />

    <h2>Add Tasks - Batch Creation</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "add-batch-1",
        tool_name: SidecarToolType.addTasks,
        input_json: JSON.stringify({
          tasks: [
            { name: "First new task", description: "Description for first task" },
            {
              name: "Second new task",
              description: "Description for second task",
              state: "IN_PROGRESS",
            },
            { name: "Third new task", description: "Description for third task" },
          ],
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "add-req-2",
        toolUseId: "add-batch-1",
        result: {
          text: `Task list updated successfully. Created: 3, Updated: 0, Deleted: 0.

# Task Changes

## Created Tasks

[ ] UUID:new-task-1 NAME:First new task DESCRIPTION:Description for first task
[/] UUID:new-task-2 NAME:Second new task DESCRIPTION:Description for second task
[ ] UUID:new-task-3 NAME:Third new task DESCRIPTION:Description for third task

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="add-req-2"
    />

    <h2>Reorganize Task List - Hierarchy Changes</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "reorganize-hierarchy-1",
        tool_name: SidecarToolType.reorganizeTaskList,
        input_json: JSON.stringify({
          markdown: `[ ] UUID:root-1 NAME:Main Project DESCRIPTION:Root project task
-[ ] UUID:feature-1 NAME:Feature Development DESCRIPTION:Main feature work
--[ ] UUID:subtask-1 NAME:Design mockups DESCRIPTION:Create UI designs
--[/] UUID:subtask-2 NAME:Implement backend DESCRIPTION:Backend API development
--[ ] UUID:subtask-3 NAME:Frontend integration DESCRIPTION:Connect frontend to backend
-[x] UUID:feature-2 NAME:Testing DESCRIPTION:Quality assurance testing`,
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "reorganize-req-2",
        toolUseId: "reorganize-hierarchy-1",
        result: {
          text: `Task list updated successfully. Created: 4, Updated: 2, Deleted: 0.

# Task Changes

## Created Tasks

[ ] UUID:feature-1 NAME:Feature Development DESCRIPTION:Main feature work
[ ] UUID:subtask-1 NAME:Design mockups DESCRIPTION:Create UI designs
[ ] UUID:subtask-3 NAME:Frontend integration DESCRIPTION:Connect frontend to backend
[x] UUID:feature-2 NAME:Testing DESCRIPTION:Quality assurance testing

## Updated Tasks

[ ] UUID:root-1 NAME:Main Project DESCRIPTION:Root project task
[/] UUID:subtask-2 NAME:Implement backend DESCRIPTION:Backend API development

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="reorganize-req-2"
    />

    <h2>Loading States</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "update-loading-1",
        tool_name: SidecarToolType.updateTasks,
        input_json: JSON.stringify({
          task_id: "task-456",
          state: "COMPLETE",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.running,
        requestId: "update-req-loading",
        toolUseId: "update-loading-1",
      }}
      isLastTurn={true}
      requestId="update-req-loading"
    />

    <ToolUse
      toolUse={{
        tool_use_id: "add-loading-1",
        tool_name: SidecarToolType.addTasks,
        input_json: JSON.stringify({
          name: "Loading task",
          description: "This task is being created",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.running,
        requestId: "add-req-loading",
        toolUseId: "add-loading-1",
      }}
      isLastTurn={true}
      requestId="add-req-loading"
    />

    <h2>Error States</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "update-error-1",
        tool_name: SidecarToolType.updateTasks,
        input_json: JSON.stringify({
          task_id: "invalid-task-id",
          state: "COMPLETE",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "update-req-error",
        toolUseId: "update-error-1",
        result: {
          text: "Error: Task with ID 'invalid-task-id' not found in the task list.",
          isError: true,
        },
      }}
      isLastTurn={true}
      requestId="update-req-error"
    />

    <ToolUse
      toolUse={{
        tool_use_id: "add-error-1",
        tool_name: SidecarToolType.addTasks,
        input_json: JSON.stringify({
          name: "",
          description: "Task with empty name",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "add-req-error",
        toolUseId: "add-error-1",
        result: {
          text: "Error: Task name cannot be empty. Please provide a valid task name.",
          isError: true,
        },
      }}
      isLastTurn={true}
      requestId="add-req-error"
    />

    <ToolUse
      toolUse={{
        tool_use_id: "reorganize-error-1",
        tool_name: SidecarToolType.reorganizeTaskList,
        input_json: JSON.stringify({
          markdown: "Invalid markdown format without proper task structure",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "reorganize-req-error",
        toolUseId: "reorganize-error-1",
        result: {
          text: "Error: Failed to parse markdown. Invalid task format. Expected format: [x] UUID:... NAME:... DESCRIPTION:...",
          isError: true,
        },
      }}
      isLastTurn={true}
      requestId="reorganize-req-error"
    />

    <h2>Edge Cases</h2>
    <ToolUse
      toolUse={{
        tool_use_id: "update-no-changes-1",
        tool_name: SidecarToolType.updateTasks,
        input_json: JSON.stringify({
          task_id: "task-789",
          state: "NOT_STARTED",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "update-req-no-changes",
        toolUseId: "update-no-changes-1",
        result: {
          text: `Task list updated successfully. Created: 0, Updated: 0, Deleted: 0.

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="update-req-no-changes"
    />

    <ToolUse
      toolUse={{
        tool_use_id: "add-subtask-1",
        tool_name: SidecarToolType.addTasks,
        input_json: JSON.stringify({
          name: "Subtask under parent",
          description: "This is a subtask created under an existing parent task",
          parent_task_id: "parent-task-123",
        }),
      }}
      toolUseState={{
        phase: ToolUsePhase.completed,
        requestId: "add-req-3",
        toolUseId: "add-subtask-1",
        result: {
          text: `Task list updated successfully. Created: 1, Updated: 0, Deleted: 0.

# Task Changes

## Created Tasks

[ ] UUID:new-subtask-1 NAME:Subtask under parent DESCRIPTION:This is a subtask created under an existing parent task

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
          isError: false,
        },
      }}
      isLastTurn={true}
      requestId="add-req-3"
    />
  </div>
</Fieldset>

<style>
  .l-task-tools {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
    padding: var(--ds-spacing-2);
  }

  h2 {
    margin: var(--ds-spacing-4) 0 var(--ds-spacing-2) 0;
    font-size: var(--ds-font-size-3);
    font-weight: var(--ds-font-weight-bold);
    color: var(--ds-color-text-primary);
    border-bottom: 1px solid var(--ds-color-border-subtle);
    padding-bottom: var(--ds-spacing-1);
  }

  h2:first-child {
    margin-top: 0;
  }
</style>
