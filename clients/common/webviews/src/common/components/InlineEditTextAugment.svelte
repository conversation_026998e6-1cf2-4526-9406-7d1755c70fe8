<script lang="ts">
  import RegularCheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check.svg?component";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment, {
    type TextSize,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { onMount } from "svelte";

  // Core props
  export let value: string = "";
  export let placeholder: string = "";
  export let disabled: boolean = false;

  // Styling props
  export let size: TextSize = 2;
  let className: string = "";
  export { className as class };

  // Behavior props
  export let isEditing: boolean = false;
  export let isEditable: boolean = true;
  export let multiline: boolean = true;
  export let showCommitButton: boolean = true;
  export let commitOnBlur: boolean = true;
  export let commitOnEnter: boolean = true;
  export let allowCtrlEnterForNewline: boolean = true;
  export let doubleClickToEdit: boolean = true;
  export let onCancelEdit = (_orig: string) => {};
  export let onStartEdit = () => {};
  export let onChange = (_newValue: string, _origValue: string) => {};

  // Advanced props
  export let outsideNode: HTMLElement | undefined = undefined;
  export let commitButtonVariant: "classic" | "ghost" | "surface" = "classic";
  export let commitButtonColor: "accent" | "neutral" | "success" | "warning" | "error" = "accent";

  // Internal state
  let textInput: HTMLTextAreaElement | HTMLInputElement | undefined;
  let node: HTMLElement | undefined;
  let editedValue = value;

  // Reactive updates
  $: if (!isEditing) {
    editedValue = value;
  }

  // Function to cancel editing
  function cancelEditing() {
    editedValue = value;
    isEditing = false;
    onCancelEdit(value);
  }

  // Handle key events in the input field
  function handleKeyDown(e: KeyboardEvent) {
    if (commitOnEnter && e.key === "Enter") {
      if (multiline && allowCtrlEnterForNewline) {
        // Allow multiline by ctrl/cmd key
        if (!(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)) {
          e.preventDefault();
          handleCommit();
        }
      } else {
        e.preventDefault();
        handleCommit();
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEditing();
    }
  }

  // Start editing mode
  function startEditing() {
    if (!isEditable || disabled) {
      return;
    }
    if (!isEditing) {
      onStartEdit();
      isEditing = true;
    }
    // Focus the input field after the DOM updates
    setTimeout(() => {
      textInput?.focus();
    }, 0);
  }

  // Commit the changes
  function handleCommit() {
    const trimmedValue = editedValue.trim();
    const oldValue = value;

    if (trimmedValue !== oldValue) {
      value = trimmedValue;
      onChange(trimmedValue, oldValue);
    }

    setTimeout(() => {
      textInput?.blur();
    }, 0);

    isEditing = false;
  }

  // Handle blur event
  function handleBlur() {
    if (commitOnBlur && isEditing) {
      handleCommit();
    }
  }

  // Handle text click for editing
  function handleTextInteraction(e: MouseEvent) {
    if (doubleClickToEdit && e.detail === 2) {
      startEditing();
    }
  }
  $: {
    //bind to external isEditing prop so that changes make focus work.
    if (isEditing) {
      startEditing();
    }
  }
  // Set up click outside listener
  onMount(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      if (isEditing && (outsideNode || node)?.contains(e.target as Node) === false) {
        if (commitOnBlur) {
          handleCommit();
        } else {
          cancelEditing();
        }
      }
    };

    document.addEventListener("click", handleDocumentClick);

    return () => {
      document.removeEventListener("click", handleDocumentClick);
    };
  });
</script>

<div
  class="c-inline-edit-text-augment {className}"
  class:c-inline-edit-text-augment--is-editing={isEditing}
  class:c-inline-edit-text-augment--disabled={disabled}
  bind:this={node}
>
  {#if isEditable && !disabled && isEditing}
    <div class="c-inline-edit-text-augment__input">
      <!-- inlining field-sizing:content due to svelte issue: https://github.com/sveltejs/language-tools/issues/2275 -->
      <TextAugment {size}>
        {#if multiline}
          <textarea
            style="field-sizing: content;"
            bind:this={textInput}
            class="c-inline-edit-text-augment__input-field"
            {placeholder}
            {disabled}
            bind:value={editedValue}
            on:keydown={handleKeyDown}
            on:blur={handleBlur}
            rows="1"
          ></textarea>
        {:else}
          <input
            style="field-sizing: content;"
            bind:this={textInput}
            class="c-inline-edit-text-augment__input-field"
            type="text"
            {placeholder}
            {disabled}
            bind:value={editedValue}
            on:keydown={handleKeyDown}
            on:blur={handleBlur}
          />
        {/if}
      </TextAugment>

      {#if showCommitButton}
        <IconButtonAugment
          size={1}
          variant={commitButtonVariant}
          color={commitButtonColor}
          {disabled}
          on:click={handleCommit}
        >
          <RegularCheckIcon />
        </IconButtonAugment>
      {/if}
    </div>
  {/if}

  <div
    class="c-inline-edit-text-augment__text"
    class:c-inline-edit-text-augment__text--editable={isEditable && !disabled}
    on:click={handleTextInteraction}
    role="button"
    tabindex={isEditable && !disabled ? 0 : undefined}
    on:keydown={(e) => {
      if (e.key === "Enter") {
        startEditing();
      }
    }}
    aria-label={value || placeholder}
  >
    <TextAugment {size}>
      {value || placeholder}
    </TextAugment>
  </div>
</div>

<style>
  .c-inline-edit-text-augment {
    position: relative;
    flex: 1;
    min-width: 0;
    max-width: 100%;
    --inline-edit-active-border-color: var(--ds-color-neutral-a5);
  }

  .c-inline-edit-text-augment--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  /**
   * Icon button alignment - centered but visually aligned with text
   */
  .c-inline-edit-text-augment__input > :global(.c-icon-btn) {
    padding-top: calc(var(--ds-spacing-1) / 2);
    display: inline-block;
    --icon-btn-size: var(--ds-spacing-4_5);
  }

  .c-inline-edit-text-augment__input {
    display: flex;
    align-items: flex-start;
    gap: var(--ds-spacing-1);
    flex: 1;
    min-width: 100%;
  }

  .c-inline-edit-text-augment__input-field {
    appearance: none;
    width: 100%;
    background-color: transparent;
    padding: var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-2);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--inline-edit-active-border-color);
    outline: none;
    color: var(--ds-color-neutral-12);
    resize: none;
    line-height: 1;
    font-family: inherit;
    font-size: inherit;
    overflow: hidden;
  }

  .c-inline-edit-text-augment__input-field:focus {
    border-color: var(--inline-edit-active-border-color);
  }

  .c-inline-edit-text-augment__input-field:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .c-inline-edit-text-augment__text {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    padding: 2px solid transparent;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .c-inline-edit-text-augment__text--editable {
    cursor: text;
  }

  .c-inline-edit-text-augment__text--editable:hover {
    border-radius: var(--ds-radius-1);
  }

  .c-inline-edit-text-augment--is-editing .c-inline-edit-text-augment__text {
    display: none;
  }
  /* Accessibility improvements */
  .c-inline-edit-text-augment__text:focus-visible {
    outline: 2px solid var(--inline-edit-active-border-color);
    outline-offset: 2px;
  }
</style>
