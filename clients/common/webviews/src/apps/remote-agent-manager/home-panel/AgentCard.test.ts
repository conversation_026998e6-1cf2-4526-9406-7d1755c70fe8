/* eslint-disable @typescript-eslint/naming-convention */
import { render, fireEvent, waitFor } from "@testing-library/svelte";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { writable } from "svelte/store";
import AgentCard from "./AgentCard.svelte";
import {
  RemoteAgentStatus,
  RemoteAgentWorkspaceStatus,
} from "$vscode/src/remote-agent-manager/types";
import type { RemoteAgent } from "$vscode/src/remote-agent-manager/types";
import { RemoteAgentsClient } from "../models/remote-agents-client";

// Mock the context dependencies
const mockRemoteAgentsClient = {
  deleteRemoteAgent: vi.fn(),
  deletePinnedAgentFromStore: vi.fn(),
  getPinnedAgentsFromStore: vi.fn(),
} as any;

// Create a proper Svelte store mock
const createMockStore = (initialState: any) => {
  const store = writable(initialState);
  return {
    ...store,
    state: initialState,
    update: vi.fn((fn: any) => {
      const newState = fn(initialState);
      if (newState) {
        Object.assign(initialState, newState);
        store.set(initialState);
      }
    }),
  };
};

const mockAgent: RemoteAgent = {
  remote_agent_id: "test-agent-123",
  session_summary: "Test Agent",
  status: RemoteAgentStatus.agentIdle,
  workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
  has_updates: false,
  turn_summaries: [],
  started_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
  expires_at: "2024-01-02T00:00:00Z",
  is_setup_script_agent: false,
};

describe("AgentCard Optimistic Deletion", () => {
  let mockSharedWebviewStore: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockSharedWebviewStore = createMockStore({
      agentOverviews: [mockAgent],
      pinnedAgents: {},
    });
  });

  it("should immediately remove agent from UI when delete is clicked", async () => {
    // Setup successful deletion
    mockRemoteAgentsClient.deleteRemoteAgent.mockResolvedValue(true);

    const { getByTitle } = render(AgentCard, {
      props: {
        agent: mockAgent,
        selected: false,
        onSelect: vi.fn(),
      },
      context: new Map([
        [RemoteAgentsClient.key, mockRemoteAgentsClient],
        ["remoteAgentStore", mockSharedWebviewStore],
      ]),
    });

    const deleteButton = getByTitle("Delete agent");

    // Click delete button
    await fireEvent.click(deleteButton);

    // Verify optimistic update was called to remove agent
    expect(mockSharedWebviewStore.update).toHaveBeenCalledWith(expect.any(Function));

    // Verify the update function removes the agent
    const updateCall = mockSharedWebviewStore.update.mock.calls[0][0];
    const result = updateCall({
      agentOverviews: [mockAgent],
      pinnedAgents: {},
    });

    expect(result.agentOverviews).toEqual([]);
  });

  it("should revert UI changes and show error when deletion fails", async () => {
    // Setup failed deletion
    mockRemoteAgentsClient.deleteRemoteAgent.mockRejectedValue(new Error("Network error"));

    const { getByTitle, queryByText } = render(AgentCard, {
      props: {
        agent: mockAgent,
        selected: false,
        onSelect: vi.fn(),
      },
      context: new Map([
        [RemoteAgentsClient.key, mockRemoteAgentsClient],
        ["remoteAgentStore", mockSharedWebviewStore],
      ]),
    });

    const deleteButton = getByTitle("Delete agent");

    // Click delete button
    await fireEvent.click(deleteButton);

    // Wait for the error to appear
    await waitFor(() => {
      expect(queryByText("Network error")).toBeTruthy();
    });
  });

  it("should disable delete button while deletion is in progress", async () => {
    // Setup slow deletion
    let resolveDelete: (value: boolean) => void;
    const deletePromise = new Promise<boolean>((resolve) => {
      resolveDelete = resolve;
    });
    mockRemoteAgentsClient.deleteRemoteAgent.mockReturnValue(deletePromise);

    const { getByTitle } = render(AgentCard, {
      props: {
        agent: mockAgent,
        selected: false,
        onSelect: vi.fn(),
      },
      context: new Map([
        [RemoteAgentsClient.key, mockRemoteAgentsClient],
        ["remoteAgentStore", mockSharedWebviewStore],
      ]),
    });

    const deleteButton = getByTitle("Delete agent") as HTMLButtonElement;

    // Click delete button
    await fireEvent.click(deleteButton);

    // Button should be disabled during deletion
    expect(deleteButton.disabled).toBe(true);
    expect(deleteButton.title).toBe("Deleting agent...");

    // Resolve the deletion
    resolveDelete!(true);

    // Wait for completion
    await waitFor(() => {
      expect(deleteButton.disabled).toBe(false);
    });
  });
});
