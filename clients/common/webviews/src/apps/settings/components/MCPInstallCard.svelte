<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import SettingsCard from "./SettingsCard.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import SearchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";


  export let onMCPServerAdd: ((server: Omit<MCPServer, "id">) => void) | undefined = undefined;
  export let servers: MCPServer[] = [];

  // Import MCPServer type
  import type { MCPServer } from "$vscode/src/webview-providers/webview-messages";

  // MCP options data
  const mcpOptions = [
    {
      value: "context7",
      label: "Context 7",
      description: "Package documentation",
      command: "npx -y @upstash/context7-mcp@latest"
    },
    {
      value: "playwright",
      label: "Playwright",
      description: "Browser automation",
      command: "npx -y @playwright/mcp@latest"
    },
    // {
    //   value: "puppeteer",
    //   label: "Puppeteer",
    //   description: "End to end testing",
    //   command: "npx -y @modelcontextprotocol/server-puppeteer"
    // },
    {
      value: "sequential-thinking",
      label: "Sequential thinking",
      description: "Think through complex problems step-by-step.",
      command: "npx -y @modelcontextprotocol/server-sequential-thinking" 
    },
  ];

  let collapsed = true;
  let searchQuery = "";
  let filteredMcpOptions = mcpOptions;
  let debounceTimer: ReturnType<typeof setTimeout> | null = null;

  // API key input state
  let showingApiKeyInput: string | null = null; // Track which MCP is showing API key input
  let apiKeyValue = "";
  let apiKeyInputRef: HTMLInputElement | undefined;

  // Debounced search function
  function debounceSearch(query: string) {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
      filterMcpOptions(query);
    }, 300); // 300ms debounce delay
  }

  // Filter MCP options based on search query
  function filterMcpOptions(query: string) {
    if (!query.trim()) {
      filteredMcpOptions = mcpOptions;
      return;
    }

    const lowercaseQuery = query.toLowerCase();
    filteredMcpOptions = mcpOptions.filter(option =>
      option.label.toLowerCase().includes(lowercaseQuery) ||
      (option.description && option.description.toLowerCase().includes(lowercaseQuery))
    );
  }

  // Handle search input changes
  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
    debounceSearch(searchQuery);
  }

  // Function to handle individual MCP installation
  async function handleIndividualInstall(option: typeof mcpOptions[0]) {
    try {
      // Check if this MCP is already installed
      const isAlreadyInstalled = servers.some(server => server.name === option.label);

      if (isAlreadyInstalled) {
        return; // Already installed, do nothing
      }

      // For MCPs that don't require API keys, install directly
      const mcpServer: Omit<MCPServer, "id"> = {
        name: option.label,
        command: option.command,
        arguments: "", // Keep empty for backward compatibility
        useShellInterpolation: true, // New servers use shell interpolation
        env: undefined,
      };

      // Install the individual server
      if (onMCPServerAdd) {
        onMCPServerAdd(mcpServer);
      }
    } catch (error) {
      console.error(`Failed to install ${option.label}:`, error);
    }
  }

  // Function to handle API key submission
  async function handleApiKeySubmit(option: typeof mcpOptions[0]) {
    try {
      if (!apiKeyValue.trim()) {
        // Focus back to input if API key is empty
        apiKeyInputRef?.focus();
        return;
      }

      const mcpServer: Omit<MCPServer, "id"> = {
        name: option.label,
        command: getCommandWithApiKey(option, apiKeyValue.trim()),
        arguments: "", // Keep empty for backward compatibility
        useShellInterpolation: true, // New servers use shell interpolation
        env: getApiKeyEnvironment(option, apiKeyValue.trim()),
      };

      // Install the individual server
      if (onMCPServerAdd) {
        onMCPServerAdd(mcpServer);
      }

      // Reset the API key input state
      showingApiKeyInput = null;
      apiKeyValue = "";
    } catch (error) {
      console.error(`Failed to install ${option.label}:`, error);
    }
  }

  // Function to cancel API key input
  function cancelApiKeyInput() {
    showingApiKeyInput = null;
    apiKeyValue = "";
  }

  // Function to get the command with API key included
  function getCommandWithApiKey(option: typeof mcpOptions[0], apiKey: string): string {
    switch (option.value) {
      case "tavily":
        // Tavily requires the API key as a command line argument
        return `npx -y tavily-mcp@latest --TAVILY_API_KEY=${apiKey}`;
      case "exa-search":
        // Exa Search uses environment variable, so keep original command
        return option.command;
      default:
        // For other MCPs, use the original command
        return option.command;
    }
  }

  // Function to get the appropriate environment variables for API key
  function getApiKeyEnvironment(option: typeof mcpOptions[0], apiKey: string): Record<string, string> {
    switch (option.value) {
      case "tavily":
        // Tavily uses command line argument, so no env vars needed
        return {};
      case "exa-search":
        return { EXA_API_KEY: apiKey };
      default:
        // For future MCPs that require API keys, we can add them here
        return { API_KEY: apiKey };
    }
  }

</script>

<div class="mcp-install-wrapper">
  <CollapsibleAugment bind:collapsed>
    <div slot="header" class="mcp-install-header">
      <SettingsCard>
        <div slot="header-left" class="mcp-install-left">
          <CollapseButtonAugment />
          <TextAugment color="neutral" size={1} weight="light" class="card-title">
            Easy MCP Installation
          </TextAugment>
        </div>

      </SettingsCard>
    </div>

    <div class="mcp-install-content">
      <!-- Search bar shown if there are more than 9 MCPs -->
       {#if mcpOptions.length > 9}
        <div class="mcp-search-container">
          <TextFieldAugment
            bind:value={searchQuery}
            placeholder="Search MCPs..."
            size={1}
            variant="surface"
            on:input={handleSearchInput}
          >
            <div slot="iconLeft" class="search-icon">
              <SearchIcon />
            </div>
          </TextFieldAugment>
        </div>
      {/if}
      

      <div class="mcp-list-container">
        {#each mcpOptions as option}
          <div class="mcp-service-item">
            <SettingsCard>
              <div slot="header-left" class="mcp-service-info">
                <div class="mcp-service-title">
                  <TextAugment size={1} weight="medium">{option.label}</TextAugment>
                </div>
                {#if option.description}
                <TextAugment size={1} color="secondary">{option.description}</TextAugment>
                {/if}

                <!-- API Key Input - shown when this MCP is selected for API key input -->
                {#if showingApiKeyInput === option.value}
                  <div class="api-key-input-container">
                    <TextFieldAugment
                      bind:value={apiKeyValue}
                      bind:textInput={apiKeyInputRef}
                      placeholder="Enter your API key..."
                      size={1}
                      variant="surface"
                      type="password"
                      on:keydown={(e) => {
                        if (e.key === 'Enter') {
                          handleApiKeySubmit(option);
                        } else if (e.key === 'Escape') {
                          cancelApiKeyInput();
                        }
                      }}
                    />
                    <div class="api-key-actions">
                      <ButtonAugment
                        variant="ghost-block"
                        color="accent"
                        size={1}
                        on:click={() => handleApiKeySubmit(option)}
                      >
                        Install
                      </ButtonAugment>
                      <ButtonAugment
                        variant="ghost-block"
                        color="neutral"
                        size={1}
                        on:click={cancelApiKeyInput}
                      >
                        Cancel
                      </ButtonAugment>
                    </div>
                  </div>
                {/if}
              </div>
              <div slot="header-right" class="mcp-service-actions">
                {#if servers.some(server => server.name === option.label)}
                  <div class="installed-indicator">
                    <BadgeAugment.Root color="success" size={1} variant="soft"
                      >Installed</BadgeAugment.Root
                    >
                  </div>
                {:else}
                  <ButtonAugment
                    variant="ghost-block"
                    color="accent"
                    size={1}
                    on:click={() => handleIndividualInstall(option)}
                  >
                    <span>+</span>
                  </ButtonAugment>
                {/if}
              </div>
            </SettingsCard>
          </div>
        {/each}
      </div>
    </div>
  </CollapsibleAugment>
</div>

<style>
  .mcp-install-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  .mcp-install-header {
    width: 100%;
  }

  .mcp-install-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }



  .mcp-install-content {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-2);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Search container styles commented out - search bar is temporarily disabled */
  /* .mcp-search-container {
    margin-bottom: var(--ds-spacing-2);
  } */

  .mcp-list-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    height: 150px; /* Fixed height for 3 MCP items - prevents layout shift */
    overflow-y: auto;

    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2)) var(--ds-scrollbar-track, transparent);
  }

  /* Webkit scrollbar styling */
  .mcp-list-container::-webkit-scrollbar {
    width: 8px;
  }

  .mcp-list-container::-webkit-scrollbar-track {
    background: var(--ds-scrollbar-track, transparent);
  }

  .mcp-list-container::-webkit-scrollbar-thumb {
    background-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2));
    border-radius: 4px;
  }

  .mcp-list-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--ds-scrollbar-thumb-hover, rgba(255, 255, 255, 0.3));
  }

  /* Search icon and no-results styles commented out - search functionality is temporarily disabled */
  /* .search-icon {
    display: flex;
    align-items: center;
    color: var(--gray-a10);
  }

  .search-icon :global(svg) {
    width: 14px;
    height: 14px;
  }

  .no-results {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-4);
    text-align: center;
  } */

  .mcp-service-info {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .mcp-service-title {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }



  .mcp-service-actions {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .installed-indicator {
    display: flex;
    align-items: center;
    cursor: default;
  }

  .api-key-input-container {
    margin-top: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .api-key-actions {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
  }
</style>
