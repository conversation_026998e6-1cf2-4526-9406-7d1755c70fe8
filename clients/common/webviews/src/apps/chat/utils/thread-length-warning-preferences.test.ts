import { describe, it, expect, beforeEach, vi } from "vitest";
import {
  shouldNeverShowThreadLengthWarning,
  setNeverShowThreadLengthWarning,
  clearThreadLengthWarningPreference,
} from "./thread-length-warning-preferences";

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

describe("thread-length-warning-preferences", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("shouldNeverShowThreadLengthWarning", () => {
    it("returns false when no preference is stored", () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const result = shouldNeverShowThreadLengthWarning();
      
      expect(result).toBe(false);
      expect(localStorageMock.getItem).toHaveBeenCalledWith("thread-length-warning-never-show");
    });

    it("returns true when neverShow preference is set to true", () => {
      const preference = {
        neverShow: true,
        timestamp: "2023-01-01T00:00:00.000Z",
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(preference));
      
      const result = shouldNeverShowThreadLengthWarning();
      
      expect(result).toBe(true);
    });

    it("returns false when neverShow preference is set to false", () => {
      const preference = {
        neverShow: false,
        timestamp: "2023-01-01T00:00:00.000Z",
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(preference));
      
      const result = shouldNeverShowThreadLengthWarning();
      
      expect(result).toBe(false);
    });

    it("returns false when localStorage throws an error", () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error("localStorage error");
      });
      
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      
      const result = shouldNeverShowThreadLengthWarning();
      
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to read thread length warning preference from localStorage:",
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });

    it("returns false when stored data is invalid JSON", () => {
      localStorageMock.getItem.mockReturnValue("invalid json");
      
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      
      const result = shouldNeverShowThreadLengthWarning();
      
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to read thread length warning preference from localStorage:",
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe("setNeverShowThreadLengthWarning", () => {
    it("stores the preference with current timestamp", () => {
      const mockDate = new Date("2023-01-01T12:00:00.000Z");
      vi.setSystemTime(mockDate);
      
      setNeverShowThreadLengthWarning();
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        "thread-length-warning-never-show",
        JSON.stringify({
          neverShow: true,
          timestamp: "2023-01-01T12:00:00.000Z",
        })
      );
      
      vi.useRealTimers();
    });

    it("handles localStorage errors gracefully", () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error("localStorage error");
      });
      
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      
      expect(() => setNeverShowThreadLengthWarning()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to save thread length warning preference to localStorage:",
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe("clearThreadLengthWarningPreference", () => {
    it("removes the preference from localStorage", () => {
      clearThreadLengthWarningPreference();
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith("thread-length-warning-never-show");
    });

    it("handles localStorage errors gracefully", () => {
      localStorageMock.removeItem.mockImplementation(() => {
        throw new Error("localStorage error");
      });
      
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      
      expect(() => clearThreadLengthWarningPreference()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to clear thread length warning preference from localStorage:",
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });
});
