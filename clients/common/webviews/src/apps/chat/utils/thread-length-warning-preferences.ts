/**
 * Utility module for managing thread length warning preferences
 * Handles localStorage operations for the "never show again" functionality
 */

interface ThreadLengthWarningPreference {
  neverShow: boolean;
  timestamp: string;
}

const STORAGE_KEY = "thread-length-warning-never-show";

/**
 * Checks if the user has chosen to never show thread length warnings again
 * @returns true if the user has permanently dismissed the warnings
 */
export function shouldNeverShowThreadLengthWarning(): boolean {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return false;
    }

    const preference: ThreadLengthWarningPreference = JSON.parse(stored);
    return preference.neverShow === true;
  } catch (error) {
    console.error("Failed to read thread length warning preference from localStorage:", error);
    return false;
  }
}

/**
 * Sets the user preference to never show thread length warnings again
 */
export function setNeverShowThreadLengthWarning(): void {
  try {
    const preference: ThreadLengthWarningPreference = {
      neverShow: true,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(preference));
  } catch (error) {
    console.error("Failed to save thread length warning preference to localStorage:", error);
  }
}

/**
 * Clears the "never show again" preference (useful for testing or user preference reset)
 */
export function clearThreadLengthWarningPreference(): void {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error("Failed to clear thread length warning preference from localStorage:", error);
  }
}
