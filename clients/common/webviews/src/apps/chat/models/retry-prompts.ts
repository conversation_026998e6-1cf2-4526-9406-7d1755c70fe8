/**
 * Retry prompts for MAX_TOKENS scenarios.
 * These prompts are appended to the original user message to encourage more concise responses.
 */
const LINES_BUDGET = 300;
const RETRY_PROMPTS = {
  /* eslint-disable @typescript-eslint/naming-convention */
  "save-file": `save-file tool call generation failed because tool input exceeded the token limit.
Please try again by limiting the file content to at most ${LINES_BUDGET} lines. If more content needs to be added call save-file tool to edit the file after it has been created.
`,
  "str-replace-edit": `edit tool call generation failed because tool input exceeded the token limit.
Please try again by breaking down the edits into smaller chunks of at most ${LINES_BUDGET} lines each. Then put as many of these chunks in a single tool call as possible up to the total limit of ${LINES_BUDGET} lines.
`,
  /* eslint-enable @typescript-eslint/naming-convention */
} as const;

type RetryTypes = keyof typeof RETRY_PROMPTS;
export const MAX_TOKEN_RETRY_ATTEMPTS = 2;

/**
 * Gets the retry prompt for a given attempt number.
 * @returns The retry prompt to append, or undefined if no more retries
 */
export function retryPrompt(tool?: string, attempt = 0): string {
  const prompt = RETRY_PROMPTS[tool as RetryTypes];
  if (!prompt) {
    return "";
  }
  switch (attempt) {
    case 0:
      return prompt;
    case 1:
      return prompt + ", be better this time.";
    default:
      return `${tool} tool call generation failed because tool input exceeded the token limit. Take some care in making smaller chunks`;
  }
}
