import { describe, test, expect } from "vitest";

// Test the utility functions from MCPInstallCard
// Since the component is complex, we'll test the key logic functions

describe("MCPInstallCard API Key Functions", () => {
  // Mock MCP options similar to the component
  const mcpOptions = [
    {
      value: "context7",
      label: "Context 7",
      description: "Package documentation",
      command: "npx -y @upstash/context7-mcp@latest"
    },
    {
      value: "tavily",
      label: "Tavily",
      description: "Web search (requires API key)",
      command: "npx -y tavily-mcp@latest",
      requiresApiKey: true
    },
    {
      value: "exa-search",
      label: "Exa Search",
      description: "Fast, intelligent web search and crawling. (requires API key)",
      command: "npx -y exa-mcp-server",
      requiresApiKey: true
    }
  ];

  // Extract the utility functions from the component for testing
  function getCommandWithApiKey(option: typeof mcpOptions[0], apiKey: string): string {
    switch (option.value) {
      case "tavily":
        // Tavily requires the API key as a command line argument
        return `npx -y tavily-mcp@latest --TAVILY_API_KEY=${apiKey}`;
      case "exa-search":
        // Exa Search uses environment variable, so keep original command
        return option.command;
      default:
        // For other MCPs, use the original command
        return option.command;
    }
  }

  function getApiKeyEnvironment(option: typeof mcpOptions[0], apiKey: string): Record<string, string> {
    switch (option.value) {
      case "tavily":
        // Tavily uses command line argument, so no env vars needed
        return {};
      case "exa-search":
        return { EXA_API_KEY: apiKey };
      default:
        // For future MCPs that require API keys, we can add them here
        return { API_KEY: apiKey };
    }
  }

  describe("getCommandWithApiKey", () => {
    test("should include API key in command for Tavily", () => {
      const tavilyOption = mcpOptions.find(opt => opt.value === "tavily")!;
      const apiKey = "test-tavily-key-123";
      
      const result = getCommandWithApiKey(tavilyOption, apiKey);
      
      expect(result).toBe("npx -y tavily-mcp@latest --TAVILY_API_KEY=test-tavily-key-123");
    });

    test("should keep original command for Exa Search", () => {
      const exaOption = mcpOptions.find(opt => opt.value === "exa-search")!;
      const apiKey = "test-exa-key-456";
      
      const result = getCommandWithApiKey(exaOption, apiKey);
      
      expect(result).toBe("npx -y exa-mcp-server");
    });

    test("should keep original command for MCPs without API key requirement", () => {
      const context7Option = mcpOptions.find(opt => opt.value === "context7")!;
      const apiKey = "test-key";
      
      const result = getCommandWithApiKey(context7Option, apiKey);
      
      expect(result).toBe("npx -y @upstash/context7-mcp@latest");
    });
  });

  describe("getApiKeyEnvironment", () => {
    test("should return empty object for Tavily (uses command line)", () => {
      const tavilyOption = mcpOptions.find(opt => opt.value === "tavily")!;
      const apiKey = "test-tavily-key-123";
      
      const result = getApiKeyEnvironment(tavilyOption, apiKey);
      
      expect(result).toEqual({});
    });

    test("should return EXA_API_KEY for Exa Search", () => {
      const exaOption = mcpOptions.find(opt => opt.value === "exa-search")!;
      const apiKey = "test-exa-key-456";
      
      const result = getApiKeyEnvironment(exaOption, apiKey);
      
      expect(result).toEqual({ EXA_API_KEY: "test-exa-key-456" });
    });

    test("should return generic API_KEY for unknown MCPs", () => {
      const unknownOption = {
        value: "unknown-mcp",
        label: "Unknown MCP",
        command: "npx unknown-mcp",
        description: "Unknown MCP description",
        requiresApiKey: true
      };
      const apiKey = "test-unknown-key";
      
      const result = getApiKeyEnvironment(unknownOption, apiKey);
      
      expect(result).toEqual({ API_KEY: "test-unknown-key" });
    });
  });

  describe("MCP Option Configuration", () => {
    test("should have correct MCPs marked as requiring API keys", () => {
      const tavilyOption = mcpOptions.find(opt => opt.value === "tavily");
      const exaOption = mcpOptions.find(opt => opt.value === "exa-search");
      const context7Option = mcpOptions.find(opt => opt.value === "context7");

      expect(tavilyOption?.requiresApiKey).toBe(true);
      expect(exaOption?.requiresApiKey).toBe(true);
      expect(context7Option?.requiresApiKey).toBeUndefined();
    });

    test("should have correct base commands", () => {
      const tavilyOption = mcpOptions.find(opt => opt.value === "tavily");
      const exaOption = mcpOptions.find(opt => opt.value === "exa-search");

      expect(tavilyOption?.command).toBe("npx -y tavily-mcp@latest");
      expect(exaOption?.command).toBe("npx -y exa-mcp-server");
    });
  });
});
