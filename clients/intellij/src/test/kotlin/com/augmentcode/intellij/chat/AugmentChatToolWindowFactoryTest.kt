package com.augmentcode.intellij.chat

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.testFramework.TestActionEvent
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentChatToolWindowFactoryTest : AugmentBasePlatformTestCase() {
  private lateinit var mockPluginStateService: PluginStateService
  private lateinit var mockToolWindow: ToolWindow
  private lateinit var mockToolWindowManager: ToolWindowManager

  public override fun setUp() {
    super.setUp()

    mockToolWindowManager = mockk(relaxed = true)
    every { mockToolWindowManager.invokeLater(any()) } answers {
      val runnable = firstArg<Runnable>()
      runnable.run()
    }

    project.registerOrReplaceServiceInstance(
      ToolWindowManager::class.java,
      mockToolWindowManager,
      testRootDisposable,
    )
  }

  @Test
  @Ignore("Flaky test passes individually but fails when run with other tests. will fix in main branch.")
  fun testToolWindowUsesFlattenedGearMenu() {
    // Mock PluginStateService
    mockPluginStateService = mockk(relaxed = true)
    every { mockPluginStateService.state } returns PluginState.ENABLED
    every { mockPluginStateService.context } returns PluginContext(true, DefaultFeatureFlags, null)

    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    // Mock ToolWindow
    mockToolWindow = mockk(relaxed = true)
    val gearActionsSlot = slot<ActionGroup>()

    every { mockToolWindow.setAdditionalGearActions(capture(gearActionsSlot)) } returns Unit

    val factory = AugmentChatToolWindowFactory()

    // This should not throw an exception and should call setAdditionalGearActions
    factory.createToolWindowContent(project, mockToolWindow)

    // Verify that setAdditionalGearActions was called
    verify { mockToolWindow.setAdditionalGearActions(any<ActionGroup>()) }

    // Verify that the captured action group contains expected actions
    val capturedActionGroup = gearActionsSlot.captured
    val event = TestActionEvent.createTestEvent()
    val actions = capturedActionGroup.getChildren(event)
    val actionIds =
      actions.map { action ->
        ActionManager.getInstance().getId(action) ?: action.javaClass.simpleName
      }

    // Verify flattened structure: actions should be directly in the gear menu
    assertTrue(
      "Flattened menu should contain ToggleCompletionsAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ToggleCompletionsAction"),
    )
    assertTrue(
      "Flattened menu should contain OpenSettingsWebviewAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.OpenSettingsWebviewAction"),
    )
    assertTrue(
      "Flattened menu should contain ReindexAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ReindexAction"),
    )
    assertTrue(
      "Flattened menu should contain ShowHelpAction",
      actionIds.contains("com.augmentcode.intellij.actions.ShowHelpAction"),
    )
    assertTrue(
      "Flattened menu should contain ManageAccountAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ManageAccountAction"),
    )
    assertTrue(
      "Flattened menu should contain ExtensionStatusAction",
      actionIds.contains("com.augmentcode.intellij.actions.ExtensionStatusAction"),
    )
    assertTrue(
      "Flattened menu should contain SignOutAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.SignOutAction"),
    )

    // Verify separators are present for visual organization
    val separatorCount = actions.count { it is com.intellij.openapi.actionSystem.Separator }
    assertTrue(
      "Flattened menu should contain separators for visual organization (expected at least 4)",
      separatorCount >= 4,
    )

    // Verify help action is positioned last (excluding separators)
    val nonSeparatorActions = actions.filter { it !is com.intellij.openapi.actionSystem.Separator }
    val lastAction = nonSeparatorActions.lastOrNull()
    val lastActionId = lastAction?.let { ActionManager.getInstance().getId(it) ?: it.javaClass.simpleName }
    assertEquals(
      "Help action should be the last item in the menu",
      "com.augmentcode.intellij.actions.ShowHelpAction",
      lastActionId,
    )

    // Verify help action has the correct icon
    val helpAction =
      actions.find { action ->
        ActionManager.getInstance().getId(action) == "com.augmentcode.intellij.actions.ShowHelpAction"
      }
    assertNotNull("Help action should be present", helpAction)
    assertEquals(
      "Help action should use AllIcons.Actions.Help icon",
      com.intellij.icons.AllIcons.Actions.Help,
      helpAction?.templatePresentation?.icon,
    )
  }

  @Test
  @Ignore("Flaky test passes individually but fails when run with other tests. will fix in main branch.")
  fun testToolWindowFlattenedGearMenuSignedOutActions() {
    // Mock PluginStateService for signed out state
    mockPluginStateService = mockk(relaxed = true)
    every { mockPluginStateService.state } returns PluginState.SIGN_IN_REQUIRED
    every { mockPluginStateService.context } returns PluginContext(false, DefaultFeatureFlags, null)

    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    // Mock ToolWindow
    mockToolWindow = mockk(relaxed = true)
    val gearActionsSlot = slot<ActionGroup>()

    every { mockToolWindow.setAdditionalGearActions(capture(gearActionsSlot)) } returns Unit

    val factory = AugmentChatToolWindowFactory()
    factory.createToolWindowContent(project, mockToolWindow)

    // Verify that setAdditionalGearActions was called
    verify { mockToolWindow.setAdditionalGearActions(any<ActionGroup>()) }

    // Verify that the captured action group contains expected actions for signed out state
    val capturedActionGroup = gearActionsSlot.captured
    val event = TestActionEvent.createTestEvent()
    val actions = capturedActionGroup.getChildren(event)
    val actionIds =
      actions.map { action ->
        ActionManager.getInstance().getId(action) ?: action.javaClass.simpleName
      }

    // Verify flattened structure for signed out state
    assertTrue(
      "Flattened menu should contain SignInAction when signed out",
      actionIds.contains("com.augmentcode.intellij.actions.SignInAction"),
    )
    assertTrue(
      "Flattened menu should contain ShowHelpAction",
      actionIds.contains("com.augmentcode.intellij.actions.ShowHelpAction"),
    )
    assertTrue(
      "Flattened menu should contain ExtensionStatusAction",
      actionIds.contains("com.augmentcode.intellij.actions.ExtensionStatusAction"),
    )

    // Verify signed-in only actions are not present when signed out
    assertFalse(
      "Flattened menu should not contain ToggleCompletionsAction when signed out",
      actionIds.contains("com.augmentcode.intellij.actions.ToggleCompletionsAction"),
    )
    assertFalse(
      "Flattened menu should not contain ReindexAction when signed out",
      actionIds.contains("com.augmentcode.intellij.actions.ReindexAction"),
    )
    assertFalse(
      "Flattened menu should not contain SignOutAction when signed out",
      actionIds.contains("com.augmentcode.intellij.actions.SignOutAction"),
    )

    // Verify separators are present for visual organization
    val separatorCount = actions.count { it is com.intellij.openapi.actionSystem.Separator }
    assertTrue(
      "Flattened menu should contain separators for visual organization (expected at least 3)",
      separatorCount >= 3,
    )

    // Verify help action is positioned last (excluding separators)
    val nonSeparatorActions = actions.filter { it !is com.intellij.openapi.actionSystem.Separator }
    val lastAction = nonSeparatorActions.lastOrNull()
    val lastActionId = lastAction?.let { ActionManager.getInstance().getId(it) ?: it.javaClass.simpleName }
    assertEquals(
      "Help action should be the last item in the menu when signed out",
      "com.augmentcode.intellij.actions.ShowHelpAction",
      lastActionId,
    )

    // Verify help action has the correct icon
    val helpAction =
      actions.find { action ->
        ActionManager.getInstance().getId(action) == "com.augmentcode.intellij.actions.ShowHelpAction"
      }
    assertNotNull("Help action should be present when signed out", helpAction)
    assertEquals(
      "Help action should use AllIcons.Actions.Help icon when signed out",
      com.intellij.icons.AllIcons.Actions.Help,
      helpAction?.templatePresentation?.icon,
    )
  }
}
