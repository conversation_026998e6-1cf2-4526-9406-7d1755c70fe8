package com.augmentcode.intellij.workspacemanagement.indexing

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.vfs.toNioPathOrNull
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.indexing.FileContent
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path
import kotlin.io.path.absolutePathString

@RunWith(JUnit4::class)
class WorkspaceIndexerTest : AugmentBasePlatformTestCase() {
  @Test
  fun testIndexer() {
    val mockWorkspaceCoordinatorService = mockk<WorkspaceCoordinatorService>(relaxed = true)
    project.registerOrReplaceServiceInstance(
      WorkspaceCoordinatorService::class.java,
      mockWorkspaceCoordinatorService,
      testRootDisposable,
    )

    val path = Path.of("test")

    val indexer = WorkspaceIndexer()
    val inputData = mockk<FileContent>(relaxed = true)
    every { inputData.project } returns project
    every { inputData.file } returns mockk(relaxed = true) { every { toNioPathOrNull() } returns path }

    var got = indexer.map(inputData)

    verify(exactly = 1) { mockWorkspaceCoordinatorService.enqueueFileForProcessing(path) }
    assertEquals(path.absolutePathString(), got.keys.first())

    got = indexer.map(inputData)
    verify(exactly = 2) { mockWorkspaceCoordinatorService.enqueueFileForProcessing(path) }
    assertEquals(path.absolutePathString(), got.keys.first())
  }
}
