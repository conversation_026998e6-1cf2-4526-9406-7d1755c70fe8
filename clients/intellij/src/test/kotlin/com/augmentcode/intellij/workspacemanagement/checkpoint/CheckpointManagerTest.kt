package com.augmentcode.intellij.workspacemanagement.checkpoint

import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.produce
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class CheckpointManagerTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  private fun setupMockEngine(expectedCalls: Int = 1): MockEngine {
    var currentCheckpoint = 0
    val checkpointCalls = mutableListOf<String>()

    val mockEngine =
      MockEngine { request ->
        assertEquals("POST", request.method.value)
        assertEquals("/checkpoint-blobs", request.url.encodedPath)
        checkpointCalls.add(request.body.toByteArray().decodeToString())
        assertTrue(checkpointCalls.size <= expectedCalls)

        currentCheckpoint += 1
        respond(
          content = """{"new_checkpoint_id": "checkpoint-$currentCheckpoint"}""",
          status = HttpStatusCode.OK,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    return mockEngine
  }

  @Test
  fun testCheckpointRegeneration() =
    runBlocking {
      val setupMockEngine = setupMockEngine(2)
      val manager = CheckpointManager(maxWorkingSetSize = 4)

      // Initial checkpoint creation with two files
      manager.run(
        produce {
          send(BlobAddedEvent(Path.of("foo.txt"), "foo-1"))
          send(BlobAddedEvent(Path.of("bar.txt"), "bar-1"))
        },
      )
      assertEquals(BlobsSnapshot(null, mutableSetOf("foo-1", "bar-1"), mutableSetOf()), manager.snapshot)

      // update one
      manager.onBlobChangeEvent(BlobUpdatedEvent(Path.of("foo.txt"), "foo-1", "foo-2"))
      assertEquals(BlobsSnapshot(null, mutableSetOf("foo-2", "bar-1"), mutableSetOf()), manager.snapshot)

      // update the other
      manager.onBlobChangeEvent(BlobUpdatedEvent(Path.of("bar.txt"), "bar-1", "bar-2"))
      assertEquals(BlobsSnapshot(null, mutableSetOf("foo-2", "bar-2"), mutableSetOf()), manager.snapshot)

      assertEquals(0, setupMockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      // now add two more files to exceed the working set size to trigger a checkpoint
      manager.run(
        produce {
          send(BlobAddedEvent(Path.of("baz.txt"), "baz-1"))
          send(BlobAddedEvent(Path.of("qux.txt"), "qux-1"))
        },
      )
      assertEquals(BlobsSnapshot("checkpoint-1", mutableSetOf(), mutableSetOf()), manager.snapshot)

      // modify one recently added blob
      manager.onBlobChangeEvent(BlobUpdatedEvent(Path.of("baz.txt"), "baz-1", "baz-2"))
      assertEquals(BlobsSnapshot("checkpoint-1", mutableSetOf("baz-2"), mutableSetOf("baz-1")), manager.snapshot)

      // add the fifth file
      manager.onBlobChangeEvent(BlobAddedEvent(Path.of("quux.txt"), "quux-1"))
      assertEquals(BlobsSnapshot("checkpoint-1", mutableSetOf("baz-2", "quux-1"), mutableSetOf("baz-1")), manager.snapshot)

      // modify another recently added blob
      manager.onBlobChangeEvent(BlobUpdatedEvent(Path.of("qux.txt"), "qux-1", "qux-2"))
      assertEquals(BlobsSnapshot("checkpoint-2", mutableSetOf(), mutableSetOf()), manager.snapshot)

      // remove one of the recently added blobs
      manager.onBlobChangeEvent(BlobRemovedEvent(Path.of("quux.txt"), "quux-1"))
      assertEquals(BlobsSnapshot("checkpoint-2", mutableSetOf(), mutableSetOf("quux-1")), manager.snapshot)

      assertEquals(2, setupMockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
    }
}
