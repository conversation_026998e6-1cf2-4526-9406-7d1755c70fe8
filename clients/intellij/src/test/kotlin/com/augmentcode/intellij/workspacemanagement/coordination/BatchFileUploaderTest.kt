package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.api.BatchUploadRequest
import com.augmentcode.api.BatchUploadResponse
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeoutOrNull
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class BatchFileUploaderTest : AugmentBasePlatformTestCase() {
  private lateinit var uploadFile: BatchFileUploader
  private lateinit var inputChannel: RoughlySizedChannel<FileToUpload>
  private lateinit var outputChannel: RoughlySizedChannel<AugmentBlobState>
  private val gson = GsonUtil.createApiGson()

  override fun setUp() {
    super.setUp()
    // Create fresh channels for each test
    AugmentSettings.Companion.instance.apiToken = "test-token"
    AugmentSettings.Companion.instance.completionURL = "http://test-server"
    inputChannel = RoughlySizedChannel(Channel(Channel.Factory.UNLIMITED))
    outputChannel = RoughlySizedChannel(Channel(Channel.Factory.UNLIMITED))
    uploadFile =
      BatchFileUploader(
        scope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
        inputChannel = inputChannel,
        outputChannel = outputChannel,
      )
    uploadFile.startUpload()
  }

  override fun tearDown() {
    inputChannel.close()
    outputChannel.close()
    Disposer.dispose(uploadFile)
    super.tearDown()
  }

  @Test
  fun testSuccessfulUpload() =
    runTest {
      val mockEngine =
        MockEngine.Companion { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest = gson.fromJson(requestBody, BatchUploadRequest::class.java)

              respond(
                content =
                  gson.toJson(
                    BatchUploadResponse().apply {
                      blobNames =
                        uploadRequest.blobs.map {
                          AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                            it.path,
                            it.content,
                          )
                        }
                    },
                  ),
                status = HttpStatusCode.Companion.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val uploadRequest = createUploadRequest("test.txt", "test content")

      inputChannel.send(uploadRequest)

      // Wait for processing
      waitForAssertion({
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      })

      // Verify output
      val result =
        withTimeoutOrNull(100) {
          outputChannel.receive()
        }
      assertNotNull(result)
      assertEquals("test.txt", result!!.relativePath)
    }

  @Test
  fun testUploadFailure() =
    runTest {
      val mockEngine =
        MockEngine.Companion { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/batch-upload" ->
              respond(
                content = "Server error",
                status = HttpStatusCode.Companion.InternalServerError,
              )

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val uploadRequest = createUploadRequest("test.txt", "test content")

      inputChannel.send(uploadRequest)

      // Should try once and retry failed uploads 3 times, a total of 4 attempts. Failed uploads should not be send to output channel
      // loop from 1 to MAX_RETRY_COUNT inclusive checking if uploaded and asserting output channel is empty
      for (i in 1..FileToUpload.Companion.MAX_RETRY_COUNT) {
        waitForAssertion({
          assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" } >= i)
        }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS + 100)
        assertNull(outputChannel.tryReceive().getOrNull())
      }
      // Wait additional time to ensure no more retries happen
      advanceTimeBy(BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 2)
      assertEquals(
        FileToUpload.Companion.MAX_RETRY_COUNT + 1,
        mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" },
      )
    }

  @Test
  fun testBatchSizeLimit() =
    runTest {
      val mockEngine =
        MockEngine.Companion { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest = gson.fromJson(requestBody, BatchUploadRequest::class.java)

              // Verify batch size doesn't exceed limit
              assertTrue(
                "Batch size ${uploadRequest.blobs.size} exceeds MAX_BATCH_SIZE",
                uploadRequest.blobs.size <= BatchFileUploader.MAX_BATCH_SIZE,
              )

              respond(
                content =
                  gson.toJson(
                    BatchUploadResponse().apply {
                      blobNames =
                        uploadRequest.blobs.map {
                          AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                            it.path,
                            it.content,
                          )
                        }
                    },
                  ),
                status = HttpStatusCode.Companion.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      // Send more items than batch size
      val uploadRequests =
        (0 until (3 * BatchFileUploader.Companion.MAX_BATCH_SIZE)).map { i ->
          createUploadRequest("test$i.txt", "content$i")
        }
      uploadRequests.forEach { inputChannel.send(it) }

      // Should result in multiple batches
      waitForAssertion({
        assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" } >= 1)
      }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 20 + 100)
    }

  @Test
  fun testBatchContentSizeLimit() =
    runTest {
      val mockEngine =
        MockEngine.Companion { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest = gson.fromJson(requestBody, BatchUploadRequest::class.java)

              // Each batch should contain exactly one blob
              assertEquals(1, uploadRequest.blobs.size)

              respond(
                content =
                  gson.toJson(
                    BatchUploadResponse().apply {
                      blobNames =
                        uploadRequest.blobs.map {
                          AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                            it.path,
                            it.content,
                          )
                        }
                    },
                  ),
                status = HttpStatusCode.Companion.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      // Create two files, each one byte under the content limit
      val largeContent1 = "a".repeat(BatchFileUploader.MAX_BATCH_CONTENT_SIZE_BYTES - 1)
      val largeContent2 = "b".repeat(BatchFileUploader.MAX_BATCH_CONTENT_SIZE_BYTES - 1)

      val uploadRequest1 = createUploadRequest("large1.txt", largeContent1)
      val uploadRequest2 = createUploadRequest("large2.txt", largeContent2)

      inputChannel.send(uploadRequest1)
      inputChannel.send(uploadRequest2)

      // Should result in two separate batches
      waitForAssertion({
        assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 3 + 100)
    }

  @Test
  fun testPartialBatchFailure() {
    runTest {
      var requestCount = 0
      val mockEngine =
        MockEngine.Companion { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/batch-upload" -> {
              requestCount++
              if (requestCount == 1) {
                // First request fails
                respond(content = "Server error", status = HttpStatusCode.Companion.InternalServerError)
              } else {
                // Retry succeeds
                val requestBody = request.body.toByteArray().decodeToString()
                val uploadRequest = gson.fromJson(requestBody, BatchUploadRequest::class.java)
                respond(
                  content =
                    gson.toJson(
                      BatchUploadResponse().apply {
                        blobNames =
                          uploadRequest.blobs.map {
                            AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                              it.path,
                              it.content,
                            )
                          }
                      },
                    ),
                  status = HttpStatusCode.Companion.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val uploadRequest = createUploadRequest("test.txt", "test content")
      inputChannel.send(uploadRequest)

      // Should eventually succeed after retry
      waitForAssertion({
        assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" } >= 2)
      }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 3)

      val result = withTimeoutOrNull(1000) { outputChannel.receive() }
      assertNotNull(result)
      assertEquals("test.txt", result!!.relativePath)
    }
  }

  @Test
  fun testApiUnavailable() {
    runTest {
      // Clear API settings to make API unavailable
      AugmentSettings.Companion.instance.apiToken = null
      AugmentSettings.Companion.instance.completionURL = null

      val mockEngine =
        MockEngine.Companion { _ ->
          fail("No requests should be made when API is unavailable")
          respond(content = "", status = HttpStatusCode.Companion.OK)
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val uploadRequest = createUploadRequest("test.txt", "test content")
      inputChannel.send(uploadRequest)

      // Wait and verify no requests were made
      advanceTimeBy(BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 2)
      assertEquals(0, mockEngine.requestHistory.size)
      assertNull(outputChannel.tryReceive().getOrNull())
    }
  }

  private fun createUploadRequest(
    relPath: String,
    content: String,
    fileReference: VirtualFile? = null,
  ) = FileToUpload(
    rootPath = "/src",
    relPath = relPath,
    expectedBlobName = AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(relPath, content),
    virtualFile = fileReference ?: LightVirtualFile(relPath, content),
  )
}
