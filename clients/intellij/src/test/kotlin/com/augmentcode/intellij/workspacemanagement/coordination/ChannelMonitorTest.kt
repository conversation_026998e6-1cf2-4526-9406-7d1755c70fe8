package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.util.Disposer
import io.mockk.clearMocks
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ChannelMonitorTest {
  @OptIn(ExperimentalCoroutinesApi::class)
  @Test
  fun testLoggingOfChannels() =
    runTest {
      val noBackPressureChannel = RoughlySizedChannel<String>(Channel(Channel.Factory.UNLIMITED))
      val noBackPressureConfig =
        ChannelMonitorConfig(
          id = "no-backpressure-channel",
          channel = noBackPressureChannel,
          backpressureWarningThreshold = null,
        )

      val backPressureChannel = RoughlySizedChannel<String>(Channel(Channel.Factory.UNLIMITED))
      val backPressureConfig =
        ChannelMonitorConfig(
          id = "backpressure-channel",
          channel = backPressureChannel,
          backpressureWarningThreshold = 2,
        )

      val mockLogger = mockk<Logger>(relaxed = true)
      val monitor =
        ChannelMonitor(
          this,
          listOf(
            noBackPressureConfig,
            backPressureConfig,
          ),
          logger = mockLogger,
        )

      try {
        verify(exactly = 0) { mockLogger.info(any<String>()) }
        verify(exactly = 0) { mockLogger.warn(any<String>()) }

        // Ensure no logs on the first loop if there are no items in the channels
        advanceTimeBy(ChannelMonitor.Companion.MONITOR_JOB_INTERVAL_MS)
        verify(exactly = 1) { mockLogger.info(any<String>()) } // once because we log when we start the job
        verify(exactly = 0) { mockLogger.warn(any<String>()) }

        // Ensure we log when there are items in the channels
        noBackPressureChannel.trySend("item 1")
        backPressureChannel.trySend("item 1")
        advanceTimeBy(ChannelMonitor.Companion.MONITOR_JOB_INTERVAL_MS)
        verify(exactly = 3) { mockLogger.info(any<String>()) }
        verify(exactly = 0) { mockLogger.warn(any<String>()) }

        // Reset counters
        clearMocks(mockLogger)

        // Ensure we log when there are items in the channels + background is met
        noBackPressureChannel.trySend("item 2")
        noBackPressureChannel.trySend("item 3")
        backPressureChannel.trySend("item 2")
        backPressureChannel.trySend("item 3")
        advanceTimeBy(ChannelMonitor.Companion.MONITOR_JOB_INTERVAL_MS)
        verify(exactly = 2) { mockLogger.info(any<String>()) }
        verify(exactly = 1) { mockLogger.warn(any<String>()) }
      } finally {
        Disposer.dispose(monitor)
      }
    }
}
