package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import com.intellij.util.indexing.FileBasedIndex
import io.mockk.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path

@RunWith(JUnit4::class)
class WorkspaceCoordinationServiceTest : AugmentBasePlatformTestCase() {
  lateinit var scope: CoroutineScope

  override fun setUp() {
    super.setUp()
    scope = augmentHelpers().createCoroutineScope(Dispatchers.IO)
  }

  @Test
  fun testNoEnqueueWhenV3IndexingDisabled() {
    mockPluginStateService(false)
    val service = WorkspaceCoordinatorService.getInstance(project)

    service.enqueueFileForProcessing(Path.of("test"))

    val result = service.intakeChannel.tryReceive()
    assertFalse(result.isSuccess)
    assertTrue(result.isFailure)
  }

  @Test
  fun testEnqueueWhenV3IndexingEnabled() {
    mockPluginStateService(true)
    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    val filePath = Path.of("test")
    service.enqueueFileForProcessing(filePath)

    val result = service.intakeChannel.tryReceive()
    assertTrue(result.isSuccess)
    assertFalse(result.isFailure)
    assertEquals(filePath, result.getOrThrow())
  }

  @Test
  fun testDisposal() {
    mockPluginStateService(true)
    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    service.enqueueFileForProcessing(Path.of("test"))

    val result = service.intakeChannel.tryReceive()
    assertTrue(result.isSuccess)
    assertFalse(result.isFailure)
    assertFalse(result.isClosed)

    // Dispose of the service and verify the channel is closed
    Disposer.dispose(service)

    val result2 = service.intakeChannel.tryReceive()
    assertFalse(result2.isSuccess)
    assertTrue(result2.isFailure)
    assertTrue(result2.isClosed)
  }

  @Test
  fun testReindexOnSignIn() {
    val pluginStateService = mockPluginStateService(true)
    var listener: PluginStateListener? = null
    every { pluginStateService.subscribe(any(), any(), any()) } answers {
      listener = secondArg<PluginStateListener>()
    }

    val fileIndexService = mockFileBasedIndexService()
    every { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) } answers { /* no-op */ }

    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    assertNotNull(listener)

    verify(exactly = 0) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
    listener!!.onStateChange(PluginContext(true, DefaultFeatureFlags, null), PluginState.ENABLED)
    verify(exactly = 1) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
  }

  @Test
  fun testNoReindexOnSignInIfDisabled() {
    val pluginStateService = mockPluginStateService(false)
    var listener: PluginStateListener? = null
    every { pluginStateService.subscribe(any(), any(), any()) } answers {
      listener = secondArg<PluginStateListener>()
    }

    val fileIndexService = mockFileBasedIndexService()
    every { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) } answers { /* no-op */ }

    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    assertNotNull(listener)

    verify(exactly = 0) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
    listener!!.onStateChange(PluginContext(true, DefaultFeatureFlags, null), PluginState.ENABLED)
    verify(exactly = 0) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
  }

  fun mockPluginStateService(enableV3Indexing: Boolean): PluginStateService {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijIndexingV3Enabled = enableV3Indexing
        },
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )

    val mockPluginStateService = mockk<PluginStateService>(relaxed = true)
    every { mockPluginStateService.context } returns mockContext

    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    return mockPluginStateService
  }

  fun mockFileBasedIndexService(): FileBasedIndex {
    mockkStatic(FileBasedIndex::class)
    val mockIndex = mockk<FileBasedIndex>()
    every { FileBasedIndex.getInstance() } returns mockIndex
    return mockIndex
  }
}
