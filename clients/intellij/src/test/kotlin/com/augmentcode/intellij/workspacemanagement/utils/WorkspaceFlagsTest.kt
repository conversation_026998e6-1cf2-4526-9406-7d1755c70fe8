package com.augmentcode.intellij.workspacemanagement.utils

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class WorkspaceFlagsTest : AugmentBasePlatformTestCase() {
  val mockPluginStateService: PluginStateService = mockk(relaxed = true)

  override fun setUp() {
    super.setUp()
    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )
  }

  @Test
  fun testIsV3IndexingSignedOut() {
    every { mockPluginStateService.context } returns PluginContext(false, DefaultFeatureFlags, null)
    assertFalse(isV3IndexingEnabled())
  }

  @Test
  fun testIsV3IndexingDisabled() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijIndexingV3Enabled = false
        },
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext
    assertFalse(isV3IndexingEnabled())
  }

  @Test
  fun testIsV3IndexingEnabled() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijIndexingV3Enabled = true
        },
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext
    assertTrue(isV3IndexingEnabled())
  }
}
