package com.augmentcode.intellij.chat

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.application.ApplicationManager
import kotlinx.coroutines.*

class EditorTrackingServiceTest : AugmentBasePlatformTestCase() {
  fun testShouldReturnNullWhenNoSelectionTracked() {
    val service = EditorTrackingService.getInstance(project)

    runBlocking {
      assertNull(service.getCurrentEditorWithSelection())
    }
  }

  fun testShouldTrackEditorSelectionThroughSelectionModel() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "line1\nline2\nline3")
    val editor = myFixture.editor

    ApplicationManager.getApplication().runWriteAction {
      editor.selectionModel.setSelection(0, 5)
    }

    runBlocking {
      val trackedEditor = service.getCurrentEditorWithSelection()
      assertNotNull(trackedEditor)
      assertEquals(editor, trackedEditor)
    }
  }

  fun testShouldClearSelectionExplicitly() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")

    service.clearSelection()
    assertFalse(service.hasValidSelection())
  }

  fun testShouldHandleBackgroundThreadAccess() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")
    val editor = myFixture.editor

    ApplicationManager.getApplication().runWriteAction {
      editor.selectionModel.setSelection(0, 5)
    }

    runBlocking {
      val result =
        withContext(Dispatchers.IO) {
          service.getCurrentEditorWithSelection()
        }
      assertNotNull(result)
      assertEquals(editor, result)
    }
  }

  fun testShouldValidateCurrentSelection() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")
    val editor = myFixture.editor

    runBlocking {
      assertFalse(service.validateCurrentSelection())
    }

    ApplicationManager.getApplication().runWriteAction {
      editor.selectionModel.setSelection(0, 5)
    }

    runBlocking {
      assertTrue(service.validateCurrentSelection())
    }
  }

  fun testShouldGetEditorWindowInfo() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")
    val editor = myFixture.editor

    val windowInfo = service.getEditorWindowInfo(editor)
    assertNotNull(windowInfo)
    assertEquals(editor.virtualFile?.path, windowInfo!!.virtualFilePath)
    assertEquals(editor.hashCode(), windowInfo.editorHashCode)
    assertTrue(windowInfo.isValid)
  }

  fun testShouldFallbackToSelectedEditorWhenNoTrackedSelection() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")
    val editor = myFixture.editor

    runBlocking {
      val result = service.getCurrentEditorWithSelection()
      assertEquals(editor, result)
    }
  }
}
