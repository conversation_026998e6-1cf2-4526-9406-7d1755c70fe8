package com.augmentcode.intellij.workspacemanagement.checkpoint

import com.augmentcode.api.BlobsPayload
import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.CheckpointResult
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.channels.ReceiveChannel
import kotlinx.coroutines.channels.consumeEach
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.jetbrains.annotations.VisibleForTesting
import java.nio.file.Path

sealed class BlobChangeEvent(val path: Path)

class BlobAddedEvent(path: Path, val newBlobName: String) : BlobChangeEvent(path)

class BlobRemovedEvent(path: Path, val blobName: String) : BlobChangeEvent(path)

class BlobUpdatedEvent(path: Path, val originalBlobName: String, val newBlobName: String) : BlobChangeEvent(path)

data class BlobsSnapshot(
  var checkpointId: String? = null,
  var addedBlobs: MutableSet<String> = mutableSetOf(),
  var deletedBlobs: MutableSet<String> = mutableSetOf(),
) {
  val workingSetSize: Int
    get() = addedBlobs.size + deletedBlobs.size

  fun toPayload(): BlobsPayload {
    val result = BlobsPayload()
    result.checkpointId = checkpointId
    result.addedBlobs = addedBlobs.toHashSet() // new set
    result.deletedBlobs = deletedBlobs.toHashSet() // new set
    return result
  }
}

class CheckpointManager(private val maxWorkingSetSize: Int = 1_000) {
  @VisibleForTesting
  val snapshot = BlobsSnapshot()
  private val snapshotMutex = Mutex()

  suspend fun currentCheckpoint(): BlobsPayload = snapshotMutex.withLock { snapshot.toPayload() }

  suspend fun run(changesChannel: ReceiveChannel<BlobChangeEvent>) = changesChannel.consumeEach { event -> onBlobChangeEvent(event) }

  @VisibleForTesting
  suspend fun onBlobChangeEvent(event: BlobChangeEvent) {
    when (event) {
      is BlobAddedEvent -> {
        snapshotMutex.withLock {
          snapshot.addedBlobs.add(event.newBlobName)
        }
      }

      is BlobRemovedEvent -> {
        snapshotMutex.withLock {
          snapshot.deletedBlobs.add(event.blobName)
        }
      }

      is BlobUpdatedEvent -> {
        snapshotMutex.withLock {
          val wasRemovedBeforeRemoteUpdate = snapshot.addedBlobs.remove(event.originalBlobName)
          if (!wasRemovedBeforeRemoteUpdate) {
            // blob is not in the yet to-add set, so it must be in the checkpoint
            // therefore add it to the deleted set
            snapshot.deletedBlobs.add(event.originalBlobName)
          }

          snapshot.addedBlobs.add(event.newBlobName)
        }
      }
    }
    if (snapshot.workingSetSize >= maxWorkingSetSize) {
      val checkpointResult =
        AugmentAPI.instance.createCheckpoint(
          CheckpointBlobsRequest().apply {
            blobs = currentCheckpoint()
          },
        )
      when (checkpointResult) {
        is CheckpointResult.Success -> {
          snapshotMutex.withLock {
            snapshot.checkpointId = checkpointResult.response.newCheckpointId
            snapshot.addedBlobs.clear()
            snapshot.deletedBlobs.clear()
          }
        }

        is CheckpointResult.NeedsReset -> {
          thisLogger().warn("Checkpoint ${snapshot.checkpointId} requires a reset.")
          // todo: invalidate index
        }

        is CheckpointResult.OtherFailure -> {
          // Do nothing, will retry on next sync
          thisLogger().warn("Failed to refresh checkpoint, will retry on next sync")
        }
      }
    }
  }
}
