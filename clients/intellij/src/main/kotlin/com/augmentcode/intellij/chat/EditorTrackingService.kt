package com.augmentcode.intellij.chat

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.*
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.readAction
import com.intellij.openapi.application.smartReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.util.text.LineColumn
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

@Service(Service.Level.PROJECT)
class EditorTrackingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : SelectionListener, FileEditorManagerListener, Disposable {
  @Volatile
  private var lastEditorWithSelection: WeakReference<Editor>? = null

  @Volatile
  private var lastSelectionTimestamp: Long = 0

  companion object {
    fun getInstance(project: Project): EditorTrackingService = project.service<EditorTrackingService>()

    private const val SELECTION_VALIDITY_TIMEOUT_MS = 30_000L
  }

  init {
    registerListeners()
  }

  private fun registerListeners() {
    EditorFactory.getInstance().eventMulticaster.addSelectionListener(
      this,
      AugmentDisposable.getInstance(project),
    )
    val messageBusConnection = project.messageBus.connect(this)
    messageBusConnection.subscribe(
      FileEditorManagerListener.FILE_EDITOR_MANAGER,
      this,
    )
  }

  suspend fun getCurrentEditorWithSelection(): Editor? {
    val trackedEditor = getTrackedEditorIfValid()
    return trackedEditor ?: getFallbackEditor()
  }

  internal fun trackSelection(
    editor: Editor,
    selectionRange: TextRange,
  ) {
    if (selectionRange.isEmpty) {
      clearSelection()
      return
    }

    lastEditorWithSelection = WeakReference(editor)
    lastSelectionTimestamp = System.currentTimeMillis()
  }

  fun clearSelection() {
    lastEditorWithSelection = null
    lastSelectionTimestamp = 0
  }

  private suspend fun getTrackedEditorIfValid(): Editor? {
    val editorRef = lastEditorWithSelection ?: return null
    val editor = editorRef.get() ?: return null

    return if (isEditorValid(editor)) editor else null
  }

  private suspend fun isEditorValid(editor: Editor): Boolean {
    return try {
      if (editor.isDisposed) return false

      if (isSelectionStale()) return false

      readAction {
        editor.selectionModel.hasSelection()
      }
    } catch (e: Exception) {
      false
    }
  }

  private fun isSelectionStale(): Boolean {
    val age = System.currentTimeMillis() - lastSelectionTimestamp
    return age > SELECTION_VALIDITY_TIMEOUT_MS
  }

  private fun getFallbackEditor(): Editor? {
    return FileEditorManager.getInstance(project).selectedTextEditor
  }

  fun hasValidSelection(): Boolean {
    val editorRef = lastEditorWithSelection ?: return false
    val editor = editorRef.get() ?: return false

    return !editor.isDisposed &&
      !isSelectionStale() &&
      try {
        editor.selectionModel.hasSelection()
      } catch (e: Exception) {
        false
      }
  }

  fun getSelectionInfo(): SelectionInfo? {
    val editorRef = lastEditorWithSelection ?: return null
    val editor = editorRef.get() ?: return null

    if (editor.isDisposed || isSelectionStale()) return null

    return try {
      val virtualFile = editor.virtualFile ?: return null
      val selectionModel = editor.selectionModel

      if (!selectionModel.hasSelection()) return null

      SelectionInfo(
        virtualFile = virtualFile,
        selectionStart = selectionModel.selectionStart,
        selectionEnd = selectionModel.selectionEnd,
        timestamp = lastSelectionTimestamp,
      )
    } catch (e: Exception) {
      null
    }
  }

  suspend fun validateCurrentSelection(): Boolean {
    val editor = getTrackedEditorIfValid() ?: return false
    return readAction {
      try {
        editor.selectionModel.hasSelection()
      } catch (e: Exception) {
        false
      }
    }
  }

  fun getEditorWindowInfo(editor: Editor): EditorWindowInfo? {
    return try {
      val virtualFile = editor.virtualFile ?: return null

      EditorWindowInfo(
        virtualFilePath = virtualFile.path,
        editorHashCode = editor.hashCode(),
        isValid = !editor.isDisposed,
      )
    } catch (e: Exception) {
      null
    }
  }

  override fun selectionChanged(e: SelectionEvent) {
    trackSelection(e.editor, e.newRange)

    if (e.newRange.isEmpty) {
      ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
        FileRangesSelected.newBuilder().build(),
      )
      return
    }

    cs.launch(Dispatchers.IO) {
      val doc = e.editor.document
      val startLineCol: LineColumn? = StringUtil.offsetToLineColumn(doc.text, e.newRange.startOffset)
      val endLineCol: LineColumn? = StringUtil.offsetToLineColumn(doc.text, e.newRange.endOffset)

      if (startLineCol == null || endLineCol == null) {
        ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
          FileRangesSelected.newBuilder().build(),
        )
        return@launch
      }

      val rootAndPath =
        if (DumbService.getInstance(project).isDumb) {
          null
        } else if (e.editor.virtualFile == null) {
          null
        } else {
          AugmentRoot.findRelativePathWithRoot(project, e.editor.virtualFile)
        }

      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
      publisher.postMessageToWebview(
        FileRangesSelected
          .newBuilder()
          .addData(
            FileDetails
              .newBuilder()
              .setRepoRoot(rootAndPath?.rootPath ?: "")
              .setPathName(rootAndPath?.relativePath ?: "")
              .setFullRange(
                expandToLineBoundaries(startLineCol, endLineCol, doc),
              ).setOriginalCode(doc.getText(e.newRange)),
          ).build(),
      )
    }
  }

  private fun expandToLineBoundaries(
    startLineCol: LineColumn,
    endLineCol: LineColumn,
    document: Document,
  ): FullRange {
    var endLine = endLineCol.line
    if (endLineCol.column == 0 && endLine > startLineCol.line) {
      endLine -= 1
    }
    val endColumn = document.getLineEndOffset(endLine)

    return FullRange
      .newBuilder()
      .setStartLineNumber(startLineCol.line)
      .setStartColumn(0)
      .setEndLineNumber(endLine)
      .setEndColumn(endColumn)
      .build()
  }

  override fun selectionChanged(e: FileEditorManagerEvent) {
    cs.launch(Dispatchers.IO) {
      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)

      if (e.newFile == null || DumbService.getInstance(project).isDumb) {
        publisher.postMessageToWebview(
          CurrentlyOpenFiles.newBuilder().build(),
        )
      } else {
        val path = AugmentRoot.findRelativePathWithRoot(project, e.newFile!!)
        if (path == null) {
          publisher.postMessageToWebview(
            CurrentlyOpenFiles.newBuilder().build(),
          )
        } else {
          publisher.postMessageToWebview(
            CurrentlyOpenFiles
              .newBuilder()
              .addData(
                FileDetails
                  .newBuilder()
                  .setRepoRoot(path.rootPath)
                  .setPathName(path.relativePath),
              ).build(),
          )
        }
      }
    }
  }

  private suspend fun getCurrentlyOpenedFiles(): CurrentlyOpenFiles =
    smartReadAction(project) {
      val currentlyOpenFilesBuilder = CurrentlyOpenFiles.newBuilder()
      val editor = FileEditorManager.getInstance(project).selectedEditor
      if (editor != null) {
        val pathWithRoot = AugmentRoot.findRelativePathWithRoot(project, editor.file)
        if (pathWithRoot?.relativePath != null) {
          currentlyOpenFilesBuilder.addData(
            FileDetails
              .newBuilder()
              .setRepoRoot(pathWithRoot.rootPath)
              .setPathName(pathWithRoot.relativePath),
          )
        }
      }
      currentlyOpenFilesBuilder.build()
    }

  fun sendCurrentlyOpenedFiles() {
    cs.launch(Dispatchers.IO) {
      DumbService.getInstance(project).waitForSmartMode()

      if (FileEditorManager.getInstance(project).selectedEditor != null) {
        val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
        publisher.postMessageToWebview(getCurrentlyOpenedFiles())
      }
    }
  }

  override fun dispose() {
    clearSelection()
  }

  data class SelectionInfo(
    val virtualFile: VirtualFile,
    val selectionStart: Int,
    val selectionEnd: Int,
    val timestamp: Long,
  )

  data class ProjectContext(
    val projectName: String,
    val projectPath: String,
    val isOpen: Boolean,
  )

  data class EditorWindowInfo(
    val virtualFilePath: String,
    val editorHashCode: Int,
    val isValid: Boolean,
  )
}
