package com.augmentcode.intellij.workspacemanagement.indexing

import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.vfs.toNioPathOrNull
import com.intellij.util.indexing.DataIndexer
import com.intellij.util.indexing.FileContent
import kotlin.io.path.absolutePathString

/**
 * This indexer has one purpose, to take files from IntelliJ's indexing pipeline
 * and pass them to the WorkspaceManagement service for processing.
 */
class WorkspaceIndexer : DataIndexer<String, Void, FileContent> {
  private val logger = thisLogger()

  override fun map(inputData: FileContent): Map<String?, Void?> {
    val path = inputData.file.toNioPathOrNull()
    if (path == null) {
      logger.debug("Failed to convert virtual file to nio path: ${inputData.file.path}")
      return emptyMap()
    }
    // The enqueue function will do nothing if V3 indexing is disabled.
    WorkspaceCoordinatorService.getInstance(inputData.project).enqueueFileForProcessing(path)
    return mapOf(path.absolutePathString() to null)
  }
}
