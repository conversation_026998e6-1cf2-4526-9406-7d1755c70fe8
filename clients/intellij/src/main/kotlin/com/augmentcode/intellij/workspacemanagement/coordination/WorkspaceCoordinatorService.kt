package com.augmentcode.intellij.workspacemanagement.coordination
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import org.jetbrains.annotations.VisibleForTesting
import java.nio.file.Path

@Service(Service.Level.PROJECT)
class WorkspaceCoordinatorService(private val project: Project, scope: CoroutineScope) : Disposable {
  companion object {
    fun getInstance(project: Project): WorkspaceCoordinatorService {
      return project.service<WorkspaceCoordinatorService>()
    }

    private const val UPLOAD_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD = 10_000 // Placeholder. Tune this.
    private const val WAIT_FOR_INDEX_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD = 10_000 // Placeholder. Tune this.
  }

  private val logger = thisLogger()

  // These channels should be given to the processors as input/output channels.
  @VisibleForTesting
  internal val intakeChannel = Channel<Path>(Channel.UNLIMITED)

  // todo: filterChannel
  // todo: initialProbeChannel
  private val uploadChannel = RoughlySizedChannel<FileToUpload>(Channel(Channel.UNLIMITED))
  private val waitForIndexChannel = RoughlySizedChannel<AugmentBlobState>(Channel(Channel.UNLIMITED))
  // todo: checkpointChannel

  // Processors
  private val batchFileUploader: BatchFileUploader

  init {
    // todo: filter job
    // todo: initial probe job
    batchFileUploader = BatchFileUploader(scope, uploadChannel, waitForIndexChannel)
    Disposer.register(this, batchFileUploader)
    // todo: waitForIndex job
    // todo: checkpoint job

    // When the user signs in/out, we need to ensure we trigger a re-index
    PluginStateService.instance.subscribe(
      project.messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (state == PluginState.ENABLED) {
            syncWorkspace()
            return
          }
        }
      },
      triggerOnStateChange = true,
    )

    val channelMonitor =
      ChannelMonitor(
        scope,
        listOf(
          ChannelMonitorConfig(
            "Upload",
            uploadChannel,
            UPLOAD_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD,
          ),
          ChannelMonitorConfig(
            "WaitForIndex",
            waitForIndexChannel,
            WAIT_FOR_INDEX_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD,
          ),
          // todo: log filter channel size
          // todo: log initial probe channel size
          // todo: log checkpoint channel size
        ),
      )
    Disposer.register(this, channelMonitor)
  }

  /**
   * When the service is disposed we should close all channels.
   */
  override fun dispose() {
    intakeChannel.close()
    uploadChannel.close()
    waitForIndexChannel.close()
  }

  /**
   * Enqueues a file for processing. This is the intake for any files that we may want to upload and index.
   *
   * IMPORTANT: This method is called by the WorkspaceIndexer, so must be quick.
   */
  fun enqueueFileForProcessing(filePath: Path) {
    if (!isV3IndexingEnabled()) return

    val result = intakeChannel.trySend(filePath)
    if (result.isFailure) {
      logger.warn("Failed to enqueue file for processing: $filePath")
    }
  }

  fun syncWorkspace() {
    if (!isV3IndexingEnabled()) {
      logger.info("Skipping v3 indexing pipeline")
      return
    }

    logger.info("Syncing users workspace with Augment")
    FileBasedIndex.getInstance().requestRebuild(WORKSPACE_INDEX_ID)
  }
}
