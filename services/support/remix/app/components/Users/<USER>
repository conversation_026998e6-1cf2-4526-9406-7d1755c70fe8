import { useState } from "react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import {
  Button,
  Dialog,
  Flex,
  Text,
  Box,
  Checkbox,
  Badge,
  Separator,
} from "@radix-ui/themes";
import { InfoCircledIcon, Cross1Icon } from "@radix-ui/react-icons";
import { useToast } from "../ui/Toast";
import { type User, ErrorResponseSchema } from "../../schemas/users";

interface ViewSubscriptionButtonProps {
  user: User;
  tenantId: string;
  onCancellationSuccess?: (userId: string, tenantId: string) => void;
}

export default function ViewSubscriptionButton({
  user,
  tenantId,
  onCancellationSuccess,
}: ViewSubscriptionButtonProps) {
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [cancelImmediately, setCancelImmediately] = useState(false);
  const queryClient = useQueryClient();
  const toast = useToast();

  // Query to fetch subscription info
  const subscriptionQuery = useQuery({
    queryKey: ["subscription", user.id, tenantId],
    queryFn: async () => {
      const response = await fetch(
        `/api/users/${user.id}/tenants/${tenantId}/subscription`,
      );
      if (!response.ok) {
        throw new Error("Failed to fetch subscription info");
      }
      return response.json();
    },
    enabled: isInfoDialogOpen, // Only fetch when dialog is open
  });

  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (): Promise<void> => {
      const response = await fetch(
        `/api/users/${user.id}/tenants/${tenantId}/cancel-subscription`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            cancel_immediately: cancelImmediately,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to cancel subscription: ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      // Show success toast
      toast.success({
        title: "Subscription Cancelled",
        description: `Subscription for ${user.email} has been ${
          cancelImmediately
            ? "cancelled immediately"
            : "scheduled for cancellation at end of billing period"
        }`,
        duration: 5000,
      });

      // Invalidate and refetch users query to update the UI
      queryClient.invalidateQueries({ queryKey: ["users"] });
      // Also invalidate the subscription query to refresh the info
      queryClient.invalidateQueries({
        queryKey: ["subscription", user.id, tenantId],
      });

      // Call the success callback if provided
      onCancellationSuccess?.(user.id, tenantId);

      // Close both dialogs and reset state
      setIsConfirmDialogOpen(false);
      setIsInfoDialogOpen(false);
      setCancelImmediately(false);
    },
    onError: (error) => {
      console.error(
        `Failed to cancel subscription for user ${user.id}:`,
        error,
      );
      // Keep the dialog open so user can see the error and try again
    },
  });

  // Only show button if user has an orb subscription ID
  if (!user.orbSubscriptionId) {
    return null;
  }

  const handleViewInfoClick = () => {
    setIsInfoDialogOpen(true);
  };

  const handleCancelClick = () => {
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmCancellation = () => {
    cancelSubscriptionMutation.mutate();
  };

  return (
    <>
      <Button
        size="2"
        variant="soft"
        color="blue"
        onClick={handleViewInfoClick}
        aria-label="View subscription information"
      >
        <InfoCircledIcon />
        Subscription
      </Button>

      {/* Subscription Info Dialog */}
      <Dialog.Root open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
        <Dialog.Content style={{ maxWidth: 600 }}>
          <Dialog.Title>Subscription Information</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            View subscription details and manage subscription status.
          </Dialog.Description>

          {subscriptionQuery.isLoading && (
            <Box mb="4">
              <Text size="2">Loading subscription information...</Text>
            </Box>
          )}

          {subscriptionQuery.error && (
            <Box
              mb="4"
              p="3"
              style={{ backgroundColor: "var(--red-3)", borderRadius: "6px" }}
            >
              <Text size="2" color="red" weight="bold">
                Error: {subscriptionQuery.error.message}
              </Text>
            </Box>
          )}

          {subscriptionQuery.data && (
            <Box mb="4">
              <Flex direction="column" gap="4">
                {/* User Info */}
                <Box>
                  <Text size="2" weight="bold" mb="2">
                    User Information
                  </Text>
                  <Flex direction="column" gap="2">
                    <Flex justify="between">
                      <Text size="2">Email:</Text>
                      <Text size="2">{user.email}</Text>
                    </Flex>
                    <Flex justify="between">
                      <Text size="2">User ID:</Text>
                      <Text size="2" style={{ fontFamily: "monospace" }}>
                        {user.id}
                      </Text>
                    </Flex>
                  </Flex>
                </Box>

                <Separator size="4" />

                {/* Subscription Info */}
                {subscriptionQuery.data && (
                  <Box>
                    <Text size="2" weight="bold" mb="2">
                      Subscription Details
                    </Text>
                    <Flex direction="column" gap="2">
                      <Flex justify="between">
                        <Text size="2">Status:</Text>
                        <Badge
                          color={
                            subscriptionQuery.data.subscriptionStatus ===
                            "ACTIVE"
                              ? "green"
                              : subscriptionQuery.data.subscriptionStatus ===
                                  "ENDED"
                                ? "red"
                                : "orange"
                          }
                        >
                          {subscriptionQuery.data.subscriptionStatus ||
                            "UNKNOWN"}
                        </Badge>
                      </Flex>
                      <Flex justify="between">
                        <Text size="2">Plan ID:</Text>
                        <Text size="2" style={{ fontFamily: "monospace" }}>
                          {subscriptionQuery.data.externalPlanId}
                        </Text>
                      </Flex>

                      {/* Only show detailed info for active subscriptions */}
                      {subscriptionQuery.data.subscriptionStatus ===
                        "ACTIVE" && (
                        <>
                          <Flex justify="between">
                            <Text size="2">Seats:</Text>
                            <Text size="2">
                              {
                                subscriptionQuery.data
                                  .numberOfSeatsThisBillingCycle
                              }
                            </Text>
                          </Flex>
                          <Flex justify="between">
                            <Text size="2">Billing Period End:</Text>
                            <Text size="2">
                              {new Date(
                                subscriptionQuery.data.billingPeriodEnd,
                              ).toLocaleDateString()}
                            </Text>
                          </Flex>
                          <Flex justify="between">
                            <Text size="2">Usage Units Available:</Text>
                            <Text size="2">
                              {subscriptionQuery.data.usageUnitsAvailable}
                            </Text>
                          </Flex>
                          <Flex justify="between">
                            <Text size="2">Usage Units Used:</Text>
                            <Text size="2">
                              {
                                subscriptionQuery.data
                                  .usageUnitsUsedThisBillingCycle
                              }
                            </Text>
                          </Flex>
                          {subscriptionQuery.data.nextBillingCycleAmount && (
                            <Flex justify="between">
                              <Text size="2">Next Billing Amount:</Text>
                              <Text size="2">
                                ${subscriptionQuery.data.nextBillingCycleAmount}
                              </Text>
                            </Flex>
                          )}
                        </>
                      )}
                    </Flex>
                  </Box>
                )}

                {/* Orb IDs */}
                <Separator size="4" />
                <Box>
                  <Text size="2" weight="bold" mb="2">
                    Orb Information
                  </Text>
                  <Flex direction="column" gap="2">
                    <Flex justify="between">
                      <Text size="2">Subscription ID:</Text>
                      <Text size="2" style={{ fontFamily: "monospace" }}>
                        {subscriptionQuery.data.orbSubscriptionId}
                      </Text>
                    </Flex>
                    <Flex justify="between">
                      <Text size="2">Customer ID:</Text>
                      <Text size="2" style={{ fontFamily: "monospace" }}>
                        {subscriptionQuery.data.orbCustomerId}
                      </Text>
                    </Flex>
                  </Flex>
                </Box>

                {/* Cancel Subscription Section - only show if subscription is active */}
                {subscriptionQuery.data.subscriptionStatus === "ACTIVE" && (
                  <>
                    <Separator size="4" />
                    <Box>
                      <Box mb="2">
                        <Text size="2" weight="bold" color="orange">
                          Cancel Subscription
                        </Text>
                      </Box>
                      <Box mb="3">
                        <Text size="2" color="gray">
                          This will cancel the user&apos;s subscription. This
                          action cannot be undone.
                        </Text>
                      </Box>
                      <Flex align="center" gap="2" mb="3">
                        <Checkbox
                          checked={cancelImmediately}
                          onCheckedChange={(checked) =>
                            setCancelImmediately(checked === true)
                          }
                          id="cancel-immediately"
                        />
                        <Text size="2" asChild>
                          <label htmlFor="cancel-immediately">
                            Cancel immediately (instead of at end of billing
                            period)
                          </label>
                        </Text>
                      </Flex>

                      {cancelSubscriptionMutation.error && (
                        <Box
                          mb="3"
                          p="3"
                          style={{
                            backgroundColor: "var(--red-3)",
                            borderRadius: "6px",
                          }}
                        >
                          <Text size="2" color="red" weight="bold">
                            Error: {cancelSubscriptionMutation.error.message}
                          </Text>
                        </Box>
                      )}

                      <Button
                        color="orange"
                        onClick={handleCancelClick}
                        disabled={cancelSubscriptionMutation.isPending}
                        size="2"
                      >
                        <Cross1Icon />
                        {cancelSubscriptionMutation.isPending
                          ? "Cancelling..."
                          : `Cancel Subscription${cancelImmediately ? " Immediately" : ""}`}
                      </Button>
                    </Box>
                  </>
                )}
              </Flex>
            </Box>
          )}

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Close
              </Button>
            </Dialog.Close>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>

      {/* Confirmation Dialog for Cancellation */}
      <Dialog.Root
        open={isConfirmDialogOpen}
        onOpenChange={setIsConfirmDialogOpen}
      >
        <Dialog.Content style={{ maxWidth: 500 }}>
          <Dialog.Title>Confirm Subscription Cancellation</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            Are you sure you want to cancel this user&apos;s subscription? This
            action cannot be undone.
          </Dialog.Description>

          <Box mb="4">
            <Text size="2" weight="bold">
              User:
            </Text>
            <Text size="2">{user.email}</Text>
          </Box>

          {cancelSubscriptionMutation.error && (
            <Box
              mb="4"
              p="3"
              style={{
                backgroundColor: "var(--red-3)",
                borderRadius: "6px",
              }}
            >
              <Text size="2" color="red" weight="bold">
                Error: {cancelSubscriptionMutation.error.message}
              </Text>
            </Box>
          )}

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Cancel
              </Button>
            </Dialog.Close>
            <Button
              color="orange"
              onClick={handleConfirmCancellation}
              disabled={cancelSubscriptionMutation.isPending}
            >
              {cancelSubscriptionMutation.isPending
                ? "Cancelling..."
                : `Confirm Cancellation${cancelImmediately ? " (Immediate)" : ""}`}
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
