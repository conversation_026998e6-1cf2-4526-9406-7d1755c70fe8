/* eslint-disable @typescript-eslint/no-explicit-any */
type MemberNames<T extends Record<string, string | number>> = {
  [K in keyof T]: T[K] extends number ? K : never;
}[keyof T];

type MemberValues<T extends Record<string, string | number>> =
  T[MemberNames<T>];

// 2) The two type-level mappings you need:
type StringValue<T extends Record<string, string | number>, V> = {
  [K in MemberNames<T>]: T[K] extends V ? K : never;
}[MemberNames<T>];

type NumericValue<
  T extends Record<string, string | number>,
  K extends MemberNames<T>,
> = T[K];

/**
 * Builds a `const` object from a numeric enum.
 * Convert between numeric and string values with `of`
 * @example
 * enum SubscriptionEnum {
 *   UNKNOWN = 0,
 *   UPCOMING = 1,
 *   ACTIVE = 2,
 *   ENDED = 3,
 * }
 * const Subscription = toConstObject(SubscriptionEnum);
 * type Subscription = toConstObject.infer<typeof Subscription>;
 * Subscription.ACTIVE // "ACTIVE"
 * Subscription.toEnumValue(SubscriptionEnum.ACTIVE) // "ACTIVE"
 * Subscription.fromEnumValue(Subscription.ACTIVE) // 2
 */
export function toConstObject<T extends Record<string, string | number>>(
  enumObj: T,
): {
  [K in MemberNames<T>]: K;
} & {
  fromEnumValue<V extends MemberValues<T> | undefined>(
    value: V,
  ): V extends undefined ? undefined : StringValue<T, V>;
  toEnumValue<V extends MemberNames<T> | undefined>(
    value: V,
  ): V extends undefined ? undefined : NumericValue<T, NonNullable<V>>;
} {
  const constObj = Object.fromEntries(
    Object.keys(enumObj)
      .filter((key) => typeof enumObj[key] === "number")
      .map((key) => [key, key]),
  ) as any;
  function toEnumValue(value: string) {
    if (value == null) return undefined;
    if (typeof value !== "string" || !Object.hasOwn(constObj, value)) {
      throw new Error(`Invalid enum value: ${value}`);
    }
    return enumObj[value];
  }
  function fromEnumValue(value: number) {
    if (value == null) return undefined;
    if (typeof value !== "number" || !Object.hasOwn(enumObj, value)) {
      throw new Error(`Invalid enum value: ${value}`);
    }
    return enumObj[value];
  }
  return Object.assign(constObj, { fromEnumValue, toEnumValue });
}

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace toConstObject {
  export type infer<T extends Record<string, any>> = Exclude<
    keyof T,
    "toEnumValue" | "fromEnumValue"
  >;
}
