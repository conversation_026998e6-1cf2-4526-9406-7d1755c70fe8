import { ActionFunctionArgs } from "@remix-run/node";
import { Duration } from "@bufbuild/protobuf";
import { ConnectError, Code } from "@connectrpc/connect";
import { getTeamManagementClient } from "../.server/grpc/team-management";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { Scope } from "~services/token_exchange/token_exchange_pb";
import type { ErrorResponse } from "../schemas/users";
import { CancelSubscriptionRequestSchema } from "../schemas/users";
import { logger } from "@augment-internal/logging";

export const action = async ({ request, params }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  const { userId, tenantId } = params;

  if (!userId || !tenantId) {
    const errorResponse: ErrorResponse = {
      error: "User ID and Tenant ID are required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Parse and validate request body
  let cancelImmediately: boolean;
  try {
    const requestData = await request.json().catch(() => ({}));
    const validatedData = CancelSubscriptionRequestSchema.parse(requestData);
    cancelImmediately = validatedData.cancel_immediately;
  } catch {
    const errorResponse: ErrorResponse = {
      error: "Invalid request body",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  // Get signed token for authentication
  let signedToken: string;
  try {
    const tokenResponse =
      await getTokenExchangeClient().getSignedTokenForIAPToken({
        tenantId: tenantId,
        iapToken: IAPJWT,
        scopes: [Scope.AUTH_RW], // Write access required for subscription cancellation
        expiration: new Duration({ seconds: BigInt(60 * 60) }), // 1 hour
      });
    signedToken = tokenResponse.signedToken;
  } catch (error) {
    // Handle errors from token exchange
    if (error instanceof ConnectError) {
      logger.error(`Token exchange error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };

      // Return 401 for unauthenticated, 403 for permission denied, 500 for all other errors
      let status = 500;
      if (error.code === Code.Unauthenticated) {
        status = 401;
      } else if (error.code === Code.PermissionDenied) {
        status = 403;
      }

      return Response.json(errorResponse, { status });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in token exchange: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred during authentication",
    };
    return Response.json(errorResponse, { status: 500 });
  }

  // Cancel subscription
  try {
    await getTeamManagementClient().cancelSubscription(
      {
        userId,
        tenantId,
        cancelImmediately,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    console.info(
      `Successfully cancelled subscription for user ${userId} in tenant ${tenantId}`,
    );

    return Response.json(
      {
        message: "Subscription cancelled successfully",
        cancelled_immediately: cancelImmediately,
      },
      { status: 200 },
    );
  } catch (error) {
    // Handle errors from cancelSubscription
    if (error instanceof ConnectError) {
      logger.error(
        `CancelSubscription error: ${error.code} - ${error.message}`,
      );

      const errorResponse: ErrorResponse = {
        error: error.message,
      };

      // Return 401 for unauthenticated, 403 for permission denied, 500 for all other errors
      let status = 500;
      if (error.code === Code.Unauthenticated) {
        status = 401;
      } else if (error.code === Code.PermissionDenied) {
        status = 403;
      }

      return Response.json(errorResponse, { status });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in cancelSubscription: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while cancelling subscription",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
