import { LoaderFunctionArgs } from "@remix-run/router";
import { Code, ConnectError } from "@connectrpc/connect";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { getTeamManagementClient } from "../.server/grpc/team-management";
import { connectCodeToHttpStatus } from "../utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_exchange_pb";
import { Duration } from "@bufbuild/protobuf";
import type { ErrorResponse } from "../schemas/users";
import { GetUserOrbInfoResponseSchemaV2 } from "../schemas/users";
import { logger } from "@augment-internal/logging";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { userId, tenantId } = params;

  if (!userId || !tenantId) {
    const errorResponse: ErrorResponse = {
      error: "User ID and Tenant ID are required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Get signed token for authentication
  let signedToken: string;
  try {
    const tokenResponse =
      await getTokenExchangeClient().getSignedTokenForIAPToken({
        tenantId: tenantId,
        iapToken: IAPJWT,
        scopes: [Scope.AUTH_R], // Read access for subscription info
        expiration: new Duration({ seconds: BigInt(60 * 60) }), // 1 hour
      });
    signedToken = tokenResponse.signedToken;
  } catch (error) {
    // Handle errors from token exchange
    if (error instanceof ConnectError) {
      logger.error(`Token exchange error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };

      // Return 401 for unauthenticated, 403 for permission denied, 500 for all other errors
      let status = 500;
      if (error.code === Code.Unauthenticated) {
        status = 401;
      } else if (error.code === Code.PermissionDenied) {
        status = 403;
      }

      return Response.json(errorResponse, { status });
    }

    // For non-ConnectError errors, return a generic server error
    console.error(`Unexpected error in token exchange: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred during authentication",
    };
    return Response.json(errorResponse, { status: 500 });
  }

  // Get user Orb info directly from auth central via gRPC
  try {
    const teamManagementClient = getTeamManagementClient();

    const orbInfoResponse = await teamManagementClient.getUserOrbInfo(
      { userId },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    // Validate the response with Zod schema
    try {
      const validatedResponse =
        GetUserOrbInfoResponseSchemaV2.parse(orbInfoResponse);
      return Response.json(validatedResponse, { status: 200 });
    } catch (validationError) {
      logger.error(`Response validation error: ${validationError}`);
      logger.error(
        `Failed response data: ${JSON.stringify(orbInfoResponse, null, 2)}`,
      );
      const errorResponse: ErrorResponse = {
        error: "Invalid response format from subscription service",
      };
      return Response.json(errorResponse, { status: 500 });
    }
  } catch (error) {
    // Handle errors from gRPC calls
    if (error instanceof ConnectError) {
      console.error(`gRPC error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error fetching subscription info: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "Failed to fetch subscription information",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
