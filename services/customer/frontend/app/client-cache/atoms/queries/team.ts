import { atomWithQuery } from "jotai-tanstack-query";
import { teamPlanChangePendingQueryOptions } from "../../queries";
import { teamPlanChangePendingBaseAtom } from "../pending";

export const teamPlanChangePendingQueryAtom = atomWithQuery((get) => {
  const isPlanChangeInProgress = get(teamPlanChangePendingBaseAtom);
  return {
    ...teamPlanChangePendingQueryOptions,
    refetchInterval: isPlanChangeInProgress ? 1_000 : false,
  };
});
