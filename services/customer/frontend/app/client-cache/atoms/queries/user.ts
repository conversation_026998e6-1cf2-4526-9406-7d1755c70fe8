import { atomWithQuery } from "jotai-tanstack-query";
import { userQueryOptions } from "../../queries";
import {
  userPlanChangePendingBaseAtom,
  subscriptionCreationPendingBaseAtom,
} from "../pending";

export const userQueryAtom = atomWithQuery((get) => {
  const isPlanChangeInProgress = get(userPlanChangePendingBaseAtom);
  const isSubscriptionCreationInProgress = get(
    subscriptionCreationPendingBaseAtom,
  );
  const shouldPoll = isPlanChangeInProgress || isSubscriptionCreationInProgress;
  return {
    ...userQueryOptions,
    refetchInterval: shouldPoll ? 1_000 : false,
  };
});
