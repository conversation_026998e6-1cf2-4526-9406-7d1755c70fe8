import { atom } from "jotai";
import { createPendingAtomWithEffect } from "./create-pending-atom-with-effect";
import { userQuery<PERSON>tom } from "../queries/user";
import { teamPlanChangePendingQueryAtom } from "../queries/team";
import type { UserApiGETResponseSchema } from "app/schemas/user";
import type { TeamPlanChangePendingSchema } from "app/schemas/team";
import {
  teamPlanChangePendingBaseAtom,
  userPlanChangePendingBaseAtom,
} from "../pending";

export const userPlanChangePendingAtom = createPendingAtomWithEffect<
  UserApiGETResponseSchema,
  Error
>({
  pendingAtom: userPlanChangePendingBaseAtom,
  queryAtom: userQueryAtom,
  filters: { queryKey: ["subscription"] },
  checkPending: (data: UserApiGETResponseSchema | undefined) =>
    data?.plan.pending === true,
});

export const teamPlanChangePendingAtom = createPendingAtomWithEffect<
  TeamPlanChangePendingSchema,
  Error
>({
  pendingAtom: teamPlanChangePendingBaseAtom,
  queryAtom: teamPlanChangePendingQueryAtom,
  filters: { queryKey: ["subscription"] },
  checkPending: (data: TeamPlanChangePendingSchema | undefined) =>
    data?.isPending === true,
});

export const planChangeInProgressAtom = atom(
  (get) => get(userPlanChangePendingAtom) || get(teamPlanChangePendingAtom),
);
