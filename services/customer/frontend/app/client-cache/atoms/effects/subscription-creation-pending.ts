import { userQuery<PERSON><PERSON> } from "../queries";
import { createPendingAtomWithEffect } from "./create-pending-atom-with-effect";
import type { UserApiGETResponseSchema } from "app/schemas/user";
import { subscriptionCreationPendingBaseAtom } from "../pending";

export const subscriptionCreationPendingAtom = createPendingAtomWithEffect<
  UserApiGETResponseSchema,
  Error
>({
  pendingAtom: subscriptionCreationPendingBaseAtom,
  queryAtom: userQueryAtom,
  filters: { queryKey: ["subscription"] },
  checkPending: (data: UserApiGETResponseSchema | undefined) =>
    data?.isSubscriptionPending === true,
});
