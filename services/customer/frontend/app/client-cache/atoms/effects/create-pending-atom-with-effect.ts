import type { Writable<PERSON>tom, Primitive<PERSON>tom } from "jotai/vanilla";
import { withAtomEffect } from "jotai-effect";
import { queryClientAtom } from "jotai-tanstack-query";
import type { QueryFilters } from "@tanstack/react-query";
import type { AtomWithQueryResult } from "jotai-tanstack-query";

type PendingTemplateConfig<TData, TError> = {
  pendingAtom: PrimitiveAtom<boolean>;
  queryAtom: WritableAtom<AtomWithQueryResult<TData, TError>, [], void>;
  filters?: QueryFilters;
  checkPending: (data: TData | undefined) => boolean;
};

export function createPendingAtomWithEffect<TData, TError>({
  pendingAtom,
  queryAtom,
  filters,
  checkPending,
}: PendingTemplateConfig<TData, TError>): ReturnType<typeof withAtomEffect> {
  return withAtomEffect(pendingAtom, (get, set) => {
    const isPending = get(pendingAtom);
    const query = get(queryAtom);
    const queryClient = get(queryClientAtom);

    if (!isPending && !query.isLoading && checkPending(query.data)) {
      set(pendingAtom, true);
    }

    if (isPending && !query.isLoading && !checkPending(query.data)) {
      queryClient.removeQueries(filters);
      set(pendingAtom, false);
    }
  });
}
