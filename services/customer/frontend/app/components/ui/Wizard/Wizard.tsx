import {
  Children,
  createContext,
  useContext,
  useEffect,
  useId,
  useState,
  type ReactNode,
  isValidElement,
  Fragment,
  cloneElement,
} from "react";
import type { ModalProps } from "../Modal";
import { isNonNullable } from "app/utils/guards";
import { clamp } from "app/utils/number";
import { dialogOpenAtom, type CloseFnProp } from "../Modal/Modal";
import { Dialog } from "@radix-ui/themes";
import { useAtom } from "jotai";
import { cn } from "app/utils/style";

type WizardContextValue = {
  isInsideWizard: boolean;
};

const WizardContext = createContext<WizardContextValue>({
  isInsideWizard: false,
});

export function WizardProvider({ children }: { children: ReactNode }) {
  return (
    <WizardContext.Provider value={{ isInsideWizard: true }}>
      {children}
    </WizardContext.Provider>
  );
}

export function useWizardContext() {
  return useContext(WizardContext);
}

type WizardChildrenProp =
  | ReactNode
  | ReactNode[]
  | ((props: WizardPageControls) => ReactNode | ReactNode[]);

type WizardProps = Omit<
  ModalProps,
  "footer" | "title" | "description" | "children" | "footer"
> & {
  pageNumber?: number;
  setPageNumber?: (pageNumber: number) => void;
  children: WizardChildrenProp;
};

type WizardPageControls = {
  pageNumber: number;
  pageCount: number;
  setPage: (pageNumber: number) => void;
  close: CloseFnProp;
  next: () => void;
  previous: () => void;
};

export function Wizard({
  children,
  trigger,
  isOpen: externalIsOpen,
  onOpenChange,
  pageNumber,
  setPageNumber,
  className,
  ...rest
}: WizardProps) {
  const isOpenControlled = isNonNullable(externalIsOpen);
  const isPageControlled =
    isNonNullable(pageNumber) && isNonNullable(setPageNumber);
  // Get the global dialog state
  const [dialogOpen, setDialogOpen] = useAtom(dialogOpenAtom);
  const id = useId();
  const isOpen = isOpenControlled ? externalIsOpen : dialogOpen === id;
  const [internalPageNumber, setInternalPageNumber] = useState(0);
  const currentPageNumber = isPageControlled ? pageNumber : internalPageNumber;
  const setPage = isPageControlled ? setPageNumber : setInternalPageNumber;

  const safeSetPage = (pageNumber: number) => {
    const safePage = clamp(pageNumber, 0, allPages.length - 1);
    setPage(safePage);
  };

  useEffect(() => {
    return () => {
      if (isOpen && dialogOpen === id) {
        setDialogOpen(null);
      }
    };
  }, [setDialogOpen, isOpen, dialogOpen, id]);

  function handleOpenChange(isOpen: boolean) {
    if (isOpen && dialogOpen !== null && dialogOpen !== id) {
      console.warn("Another modal is already open");
      return;
    }

    const shouldChange = onOpenChange?.(isOpen);
    if (shouldChange === false) return;
    setDialogOpen(isOpen ? id : null);
  }

  const withClose = ((fn) =>
    typeof fn === "function"
      ? async () => {
          const shouldClose = await fn();
          if (shouldClose === false) return;
          handleOpenChange(false);
        }
      : handleOpenChange(false)) as CloseFnProp;

  // First pass: render with dummy pageControls to get page count
  const dummyPageControls = {
    pageNumber: 0,
    pageCount: 0,
    setPage: () => {},
    close: withClose,
    next: () => {},
    previous: () => {},
  };

  const dummyAllPages = normalizeChildren(children, dummyPageControls);

  // Second pass: render with real pageControls
  const pageControls = {
    pageNumber: currentPageNumber,
    pageCount: dummyAllPages.length,
    setPage: safeSetPage,
    close: withClose,
    next: () => safeSetPage(currentPageNumber + 1),
    previous: () => safeSetPage(currentPageNumber - 1),
  };

  const allPages = normalizeChildren(children, pageControls);

  const safePage = clamp(currentPageNumber, 0, allPages.length - 1);

  const currentPage = allPages[safePage];

  return (
    <>
      {trigger?.(() => handleOpenChange(true))}
      <WizardProvider>
        <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
          <Dialog.Content
            maxWidth="600px"
            aria-modal="true"
            role="dialog"
            aria-label={"Wizard"}
            {...rest}
            asChild
          >
            <div className={cn(className, "modal-content")}>
              {cloneElement(currentPage, { key: safePage })}
            </div>
          </Dialog.Content>
        </Dialog.Root>
      </WizardProvider>
    </>
  );
}

function normalizeChildren(
  children: WizardChildrenProp,
  pageControls: WizardPageControls,
) {
  const childrenResult =
    typeof children === "function" ? children(pageControls) : children;
  if (Array.isArray(childrenResult)) {
    return childrenResult;
  }
  if (isValidElement(childrenResult) && childrenResult.type === Fragment) {
    return childrenResult.props.children;
  }
  return Children.toArray(childrenResult);
}

// todo aria-label={title ? String(title) : "Wizard"}
// todo aria-describedby={description ? String(description) : undefined}
