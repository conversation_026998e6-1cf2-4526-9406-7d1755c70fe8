import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { <PERSON> } from "./Wizard";
import { Button, Text } from "@radix-ui/themes";
import Modal from "../Modal";

const meta = {
  title: "UI/Wizard",
  component: Wizard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Wizard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: <Text>This is the content of the wizard</Text>,
  },
  render: () => (
    <Wizard trigger={(open) => <Button onClick={open}>Open Wizard</Button>}>
      {({ close, next, previous, pageNumber, pageCount, setPage }) => (
        <>
          <Modal
            title="Wizard"
            description="This is a wizard"
            footer={
              <>
                <Button variant="soft" onClick={close}>
                  Cancel in footer
                </Button>
                <Button onClick={previous}>Previous in footer</Button>
                <Button onClick={next}>Next in footer</Button>
              </>
            }
          >
            <h1>
              Page {pageNumber + 1}/{pageCount}
            </h1>
            <Text>This is the first page</Text>
            <div>
              <Button onClick={close}>Close in body</Button>
              <Button onClick={next}>Next in body</Button>
              <Button onClick={() => setPage(3)}>Skip to page 3</Button>
            </div>
          </Modal>
          <Modal
            title="Wizard p2"
            description="This is a wizard (page 2)"
            footer={
              <>
                <Button variant="soft" onClick={close}>
                  Cancel in footer
                </Button>
                <Button onClick={previous}>Previous in footer</Button>
                <Button onClick={next}>Next in footer</Button>
              </>
            }
          >
            <h1>
              Page {pageNumber + 1}/{pageCount}
            </h1>
            <Text>This is the second page</Text>
            <div>
              <Button onClick={close}>Close in body</Button>
              <Button onClick={previous}>Previous in body</Button>
              <Button onClick={next}>Next in body</Button>
            </div>
          </Modal>
          <Modal
            title="Wizard p3"
            description="This is a wizard (page 3)"
            footer={
              <>
                <Button variant="soft" onClick={close}>
                  Cancel in footer
                </Button>
                <Button onClick={previous}>Previous in footer</Button>
                <Button onClick={next}>Next in footer</Button>
              </>
            }
          >
            <h1>
              Page {pageNumber + 1}/{pageCount}
            </h1>
            <Text>This is the third page</Text>
            <div>
              <Button onClick={close}>Close in body</Button>
              <Button onClick={previous}>Previous in body</Button>
            </div>
            <style scoped>
              {`:scope {
              min-height: 600px;
            }`}
            </style>
          </Modal>
        </>
      )}
    </Wizard>
  ),
};

export const ManyPagesArray: Story = {
  args: {
    children: <Text>This is the content of the wizard</Text>,
  },
  render: () => (
    <Wizard trigger={(open) => <Button onClick={open}>Open Wizard</Button>}>
      {({ close, next, previous, pageNumber, pageCount, setPage }) =>
        new Array(20).fill(0).map((_i, index) => (
          <Modal
            title={`Wizard p${index + 1}`}
            description={`This is a wizard (page ${index + 1})`}
            key={index}
            footer={
              <>
                {" "}
                <Text>THE FOOTER! {pageNumber}</Text>
                <Button variant="soft" onClick={close}>
                  Cancel
                </Button>
                <Button onClick={previous}>Previous</Button>
                <Button onClick={next}>Next</Button>
              </>
            }
          >
            <h1>
              Page {pageNumber + 1}/{pageCount}
            </h1>
            <div>
              <Text>This is page {index + 1}</Text>
              <div>
                <Button onClick={() => setPage(pageNumber + 5)}>
                  Go to page {pageNumber + 5}
                </Button>
              </div>
              <style scoped>
                {`:scope {
                min-height: ${pageNumber * 40}px;
              }`}
              </style>
            </div>
          </Modal>
        ))
      }
    </Wizard>
  ),
};
