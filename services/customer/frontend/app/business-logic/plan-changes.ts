import {
  isCommunityPlanOption,
  isPaidPlanOption,
  isTrialPlanOption,
  type PlanOptionSchema,
} from "app/schemas/plan";

/**
 * Determines changing from currentPlanId to targetPlanId is immediate (true) or not (false).
 * Non-immediate changes are scheduled for the end of the billing cycle. Currently upgrades are immediate,
 * while downgrades are scheduled for the end of the billing cycle. Also changes TO
 * and FROM community are immediate.
 * @param currentPlanId
 * @param targetPlanId
 * @param plans the data from the /api/plans endpoint
 * @returns boolean
 */
export function isChangeImmediate(
  currentPlanId: string | undefined,
  targetPlanId: string,
  plans: Pick<PlanOptionSchema, "id" | "price" | "augmentPlanType">[],
): boolean {
  if (!currentPlanId) return true; // No current plan, treat as upgrade (immediate)

  const currentPlan = plans.find((plan) => plan.id === currentPlanId);
  const targetPlan = plans.find((plan) => plan.id === targetPlanId);

  if (!currentPlan || !targetPlan) {
    throw new Error(
      `Plan not found: ${!currentPlan ? currentPlanId : targetPlanId}`,
    );
  }

  // Community plan changes are always immediate
  if (isCommunityPlanOption(targetPlan)) return true;

  // Changes from community or trial to paid plans are always immediate (upgrades)
  if (isCommunityPlanOption(currentPlan) || isTrialPlanOption(currentPlan)) {
    return true;
  }

  // For paid-to-paid changes, compare prices to determine upgrade vs downgrade
  if (isPaidPlanOption(currentPlan) && isPaidPlanOption(targetPlan)) {
    const currentPrice = parseFloat(currentPlan.price);
    const targetPrice = parseFloat(targetPlan.price);

    // If we can't parse prices, throw an error to indicate invalid pricing data
    if (isNaN(currentPrice) || isNaN(targetPrice)) {
      throw new Error(
        `Invalid pricing data: current plan price "${currentPlan.price}", target plan price "${targetPlan.price}"`,
      );
    }

    // Higher price = upgrade (immediate), lower/same price = downgrade (end of billing period)
    return targetPrice > currentPrice;
  }

  // Default to upgrade for any other cases
  return true;
}
