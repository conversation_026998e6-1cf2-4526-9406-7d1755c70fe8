import { describe, it, expect } from "vitest";
import { isChangeImmediate } from "./plan-changes";
import type { PlanOptionSchema } from "app/schemas/plan";

describe("isChangeImmediate", () => {
  // Mock plan data for testing
  const mockPlans: Pick<
    PlanOptionSchema,
    "id" | "price" | "augmentPlanType"
  >[] = [
    {
      id: "community-plan",
      augmentPlanType: "community",
      price: "0",
    },
    {
      id: "trial-plan",
      augmentPlanType: "trial",
      price: "0",
    },
    {
      id: "basic-plan",
      augmentPlanType: "paid",
      price: "10.00",
    },
    {
      id: "pro-plan",
      augmentPlanType: "paid",
      price: "25.00",
    },
    {
      id: "enterprise-plan",
      augmentPlanType: "paid",
      price: "50.00",
    },
    // Plans with invalid pricing for error testing
    {
      id: "invalid-price-plan",
      augmentPlanType: "paid",
      price: "invalid-price",
    },
  ];

  describe("immediate changes (returns true)", () => {
    describe("no current plan scenario", () => {
      it("should return true when currentPlanId is undefined", () => {
        const result = isChangeImmediate(undefined, "basic-plan", mockPlans);
        expect(result).toBe(true);
      });

      it("should return true when currentPlanId is null", () => {
        const result = isChangeImmediate(undefined, "pro-plan", mockPlans);
        expect(result).toBe(true);
      });
    });

    describe("changes to community plan", () => {
      it("should return true when changing from paid to community plan", () => {
        const result = isChangeImmediate(
          "basic-plan",
          "community-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });

      it("should return true when changing from trial to community plan", () => {
        const result = isChangeImmediate(
          "trial-plan",
          "community-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });

      it("should return true when changing from enterprise to community plan", () => {
        const result = isChangeImmediate(
          "enterprise-plan",
          "community-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });
    });

    describe("upgrades from community/trial", () => {
      it("should return true when changing from community to basic paid plan", () => {
        const result = isChangeImmediate(
          "community-plan",
          "basic-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });

      it("should return true when changing from community to pro paid plan", () => {
        const result = isChangeImmediate(
          "community-plan",
          "pro-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });

      it("should return true when changing from trial to basic paid plan", () => {
        const result = isChangeImmediate("trial-plan", "basic-plan", mockPlans);
        expect(result).toBe(true);
      });

      it("should return true when changing from trial to enterprise paid plan", () => {
        const result = isChangeImmediate(
          "trial-plan",
          "enterprise-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });
    });

    describe("paid plan upgrades", () => {
      it("should return true when upgrading from basic to pro plan", () => {
        const result = isChangeImmediate("basic-plan", "pro-plan", mockPlans);
        expect(result).toBe(true);
      });

      it("should return true when upgrading from basic to enterprise plan", () => {
        const result = isChangeImmediate(
          "basic-plan",
          "enterprise-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });

      it("should return true when upgrading from pro to enterprise plan", () => {
        const result = isChangeImmediate(
          "pro-plan",
          "enterprise-plan",
          mockPlans,
        );
        expect(result).toBe(true);
      });
    });
  });

  describe("non-immediate changes (returns false)", () => {
    describe("paid plan downgrades", () => {
      it("should return false when downgrading from enterprise to pro plan", () => {
        const result = isChangeImmediate(
          "enterprise-plan",
          "pro-plan",
          mockPlans,
        );
        expect(result).toBe(false);
      });

      it("should return false when downgrading from enterprise to basic plan", () => {
        const result = isChangeImmediate(
          "enterprise-plan",
          "basic-plan",
          mockPlans,
        );
        expect(result).toBe(false);
      });

      it("should return false when downgrading from pro to basic plan", () => {
        const result = isChangeImmediate("pro-plan", "basic-plan", mockPlans);
        expect(result).toBe(false);
      });

      it("should return false when target price equals current price", () => {
        // Create two plans with the same price for this test
        const samePricePlans: Pick<
          PlanOptionSchema,
          "id" | "price" | "augmentPlanType"
        >[] = [
          ...mockPlans,
          {
            id: "same-price-plan",
            augmentPlanType: "paid",
            price: "10.00", // Same as basic-plan
          },
        ];

        const result = isChangeImmediate(
          "basic-plan",
          "same-price-plan",
          samePricePlans,
        );
        expect(result).toBe(false);
      });
    });
  });

  describe("error scenarios", () => {
    describe("invalid pricing data", () => {
      it("should throw error when current plan price cannot be parsed", () => {
        expect(() => {
          isChangeImmediate("invalid-price-plan", "basic-plan", mockPlans);
        }).toThrow(
          'Invalid pricing data: current plan price "invalid-price", target plan price "10.00"',
        );
      });

      it("should throw error when target plan price cannot be parsed", () => {
        expect(() => {
          isChangeImmediate("basic-plan", "invalid-price-plan", mockPlans);
        }).toThrow(
          'Invalid pricing data: current plan price "10.00", target plan price "invalid-price"',
        );
      });

      it("should throw error when both plan prices cannot be parsed", () => {
        const invalidPlans: Pick<
          PlanOptionSchema,
          "id" | "price" | "augmentPlanType"
        >[] = [
          ...mockPlans,
          {
            id: "another-invalid-plan",
            augmentPlanType: "paid",
            price: "not-a-number",
          },
        ];

        expect(() => {
          isChangeImmediate(
            "invalid-price-plan",
            "another-invalid-plan",
            invalidPlans,
          );
        }).toThrow(
          'Invalid pricing data: current plan price "invalid-price", target plan price "not-a-number"',
        );
      });
    });

    describe("missing plans", () => {
      it("should throw when current plan is not found in plans array", () => {
        expect(() => {
          isChangeImmediate("non-existent-plan", "basic-plan", mockPlans);
        }).toThrow("Plan not found: non-existent-plan");
      });

      it("should throw when target plan is not found in plans array", () => {
        expect(() => {
          isChangeImmediate("basic-plan", "non-existent-plan", mockPlans);
        }).toThrow("Plan not found: non-existent-plan");
      });

      it("should throw when both plans are not found in plans array", () => {
        expect(() => {
          isChangeImmediate(
            "non-existent-current",
            "non-existent-target",
            mockPlans,
          );
        }).toThrow("Plan not found: non-existent-current");
      });

      it("should throw when plans array is empty", () => {
        expect(() => {
          isChangeImmediate("basic-plan", "pro-plan", []);
        }).toThrow("Plan not found: basic-plan");
      });
    });
  });
});
