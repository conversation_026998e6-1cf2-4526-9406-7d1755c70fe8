/* eslint-disable no-console */

import * as crypto from "crypto";
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import {
  chromium,
  type Browser,
  type BrowserContext,
  type Page,
} from "playwright";

const CONFIG = {
  projectId: "1035750215372",
  secretId: "davidm-e2e-customerui", // pragma: allowlist secret
  customerUiUrl:
    process.env.CUSTOMER_UI_URL ||
    "https://app.dev-david.us-central1.dev.augmentcode.com",
  videoDir: "./e2e-tests/media",
} as const;

// ───────────────────────────────────────────────────────────────────────────────
// Utilities
// ───────────────────────────────────────────────────────────────────────────────

function generateTestUser() {
  const uid = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
  return {
    email: `userA_e2e_customerui_${uid}@augm.io`,
    idp_user_id: `test|${uid}`,
  } as const;
}

async function getSecret(
  projectId = CONFIG.projectId,
  secretId = CONFIG.secretId,
  versionId = "latest",
) {
  const client = new SecretManagerServiceClient();
  const name = `projects/${projectId}/secrets/${secretId}/versions/${versionId}`;
  const [res] = await client.accessSecretVersion({ name });
  const val = res.payload?.data?.toString();
  if (!val) throw new Error(`Secret '${secretId}' is empty`);
  return val;
}

function createHmacBearerToken(payload: object, secret: string) {
  const data = Buffer.from(JSON.stringify(payload)).toString("base64url");
  const sig = crypto
    .createHmac("sha256", secret)
    .update(data)
    .digest("base64url");
  return `${data}.${sig}`;
}

async function withAuthenticatedContext<T>(
  bearerToken: string,
  run: (page: Page) => Promise<T>,
) {
  let browser: Browser | undefined;
  let context: BrowserContext | undefined;
  let page: Page | undefined;
  try {
    browser = await chromium.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    context = await browser.newContext({
      extraHTTPHeaders: {
        Authorization: `Bearer ${bearerToken}`,
        "User-Agent": "E2E-LoginTeamLogout/1.0",
      },
      recordVideo: {
        dir: CONFIG.videoDir,
        size: { width: 1280, height: 720 },
      },
    });
    page = await context.newPage();
    return await run(page);
  } finally {
    if (page) await page.close();
    if (context) await context.close();
    if (browser) await browser.close();
  }
}

// ───────────────────────────────────────────────────────────────────────────────
// Page helpers
// ───────────────────────────────────────────────────────────────────────────────

async function acceptTerms(page: Page) {
  console.log("accepting terms");
  await page.waitForLoadState("networkidle");
  const onSignup = await page
    .locator('h3:has-text("Welcome to Augment Code")')
    .count();
  if (!onSignup) return;
  const tosCheckbox = page.locator("#terms-of-service-checkbox");
  await tosCheckbox.check({ force: true });

  // TODO - understand why this delay is needed
  await page.waitForTimeout(1000);

  const signupButton = page.locator("#signup-button");
  // await signupButton.waitFor({ timeout: 5000 });
  await signupButton.isEnabled({ timeout: 10_000 });
  await page.screenshot({
    path: "e2e-tests/media/2-signup-complete.png",
  });
  await signupButton.click();

  // TODO - understand why this delay is needed
  await page.waitForTimeout(4000);

  await page.waitForLoadState("networkidle");
  await page.screenshot({
    path: "e2e-tests/media/2-signup-complete-b.png",
  });
}

async function navigateToTeam(page: Page) {
  console.log("navigating to team page");

  // TODO - understand why this delay is needed
  await page.waitForTimeout(5000);

  await page.goto(`${CONFIG.customerUiUrl}/account/team`, {
    waitUntil: "networkidle",
  });
  await page.screenshot({
    path: "e2e-tests/media/3-team-page.png",
  });
  await page
    .locator('h1:has-text("Team Members")')
    .waitFor({ timeout: 10_000 });
}

async function logout(page: Page) {
  console.log("logging out");
  const form = page.locator('form[action="/logout"]');
  await form.waitFor({ timeout: 10_000 });
  const logoutBtn = form.locator('button[type="submit"]', {
    hasText: "Logout",
  });
  await logoutBtn.click();
  await page.screenshot({
    path: "e2e-tests/media/4-logout-complete.png",
  });
}

// ───────────────────────────────────────────────────────────────────────────────
// Test runner
// ───────────────────────────────────────────────────────────────────────────────

export async function runLoginTeamLogout() {
  console.log("Running Login → Team → Logout E2E", "=".repeat(40));
  const testUser = generateTestUser();
  console.log(`Running E2E for ${testUser.user_email}`);

  const secret = await getSecret();
  const token = createHmacBearerToken(testUser, secret);

  await withAuthenticatedContext(token, async (page) => {
    await page.goto(CONFIG.customerUiUrl, { waitUntil: "networkidle" });
    await acceptTerms(page);
    await navigateToTeam(page);
    await logout(page);
    console.log("✅ Login → Team → Logout succeeded");
  });
}

if (import.meta.url === `file://${process.argv[1]}`) {
  runLoginTeamLogout();
}
