/**
  Defines third-party agent-chat model configurations used across deployment files
 */

local agent_v2_model_agnostic = {
  client_type:: error 'Must override client_type',
  model_name:: error 'Must override model_name',
  prompt_formatter_name: 'agent-binks-claude-v2',
  temperature: 0,
  max_output_tokens: 1024 * 8,  // 8k for response
  token_apportionment: {
    prefix_len: 1024 * 2,
    suffix_len: 1024 * 2,
    path_len: 256,  // Not used by the agent mode prompt formatter but I'm also not allowed to make this 0
    message_len: 0,  // Not used by the agent mode prompt formatter
    selected_code_len: -1,  // Deprecated field: Not used by the structured-binks prompt formatter
    chat_history_len: 0,  // Not used by the agent mode prompt formatter
    retrieval_len_per_each_user_guided_file: 0,  // Not used by the agent mode prompt formatter
    retrieval_len_for_user_guided: 0,  // Not used by the agent mode prompt formatter
    retrieval_len: 0,  // No retrieval for agent mode
    max_prompt_len: 1024 * 200,
    tool_results_len: 1024 * 120,  // 120k for tool results
    token_budget_to_trigger_truncation: 1024 * 120,
  },
};
local agent_v3_model_agnostic = agent_v2_model_agnostic + {
  prompt_formatter_name: 'agent-binks-claude-v3',
  token_apportionment+: {
    selected_code_len: 8192,
  },
};
local agent_model_agnostic = agent_v3_model_agnostic + {
  prompt_formatter_name: error 'Must override prompt_formatter_name',
};
local agent_v2_150k_model_agnostic = agent_v2_model_agnostic + {
  token_apportionment: agent_v2_model_agnostic.token_apportionment + {
    // Reduce prompt-length fields by 25%
    max_prompt_len: 1024 * 150,
    tool_results_len: 1024 * 90,  // this doesn't seem like it does anything
    token_budget_to_trigger_truncation: 1024 * 90,
  },
};
local agent_v3_150k_model_agnostic = agent_v3_model_agnostic + {
  token_apportionment: agent_v3_model_agnostic.token_apportionment + {
    // Reduce prompt-length fields by 25%
    max_prompt_len: 1024 * 150,
    tool_results_len: 1024 * 90,  // this doesn't seem like it does anything
    token_budget_to_trigger_truncation: 1024 * 90,
  },
};
{
  agent_v2_model_agnostic: agent_v2_model_agnostic,
  agent_v3_model_agnostic: agent_v3_model_agnostic,
  agent_model_agnostic: agent_model_agnostic,
  agent_v2_150k_model_agnostic: agent_v2_150k_model_agnostic,
  agent_v3_150k_model_agnostic: agent_v3_150k_model_agnostic,
}
