package main

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
)

// createTestAgent creates a test agent with the given ID and status
func createTestAgent(agentId string, status remoteagentsproto.AgentStatus) *remoteagentsproto.Agent {
	return &remoteagentsproto.Agent{
		RemoteAgentId: agentId,
		Status:        status,
		CreatedAt:     timestamppb.New(time.Now()),
		UpdatedAt:     timestamppb.New(time.Now()),
	}
}

// TestNewAgentListProcessor tests the NewAgentListProcessor function
func TestNewAgentListProcessor(t *testing.T) {
	// Test with nil timestamp
	processor := NewAgentListProcessor(nil)
	assert.Nil(t, processor.clientTimestamp)
	assert.True(t, processor.isFirstRequest)
	assert.NotNil(t, processor.lastAgentMap)
	assert.Empty(t, processor.lastAgentMap)

	// Test with non-nil timestamp
	timestamp := timestamppb.New(time.Now())
	processor = NewAgentListProcessor(timestamp)
	assert.Equal(t, timestamp, processor.clientTimestamp)
	assert.True(t, processor.isFirstRequest) // Always true initially
	assert.NotNil(t, processor.lastAgentMap)
	assert.Empty(t, processor.lastAgentMap)
}

// TestProcessAgentListFirstRequest tests the ProcessAgentList method for the first request
func TestProcessAgentListFirstRequest(t *testing.T) {
	// Create a processor with nil timestamp (first request)
	processor := NewAgentListProcessor(nil)

	// Create test agents
	agent1 := createTestAgent("agent1", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)
	agent2 := createTestAgent("agent2", remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE)
	agents := []*remoteagentsproto.Agent{agent1, agent2}

	// Process the agent list
	maxAgents := int32(10)
	maxActiveAgents := int32(5)
	updates := processor.ProcessAgentList(agents, maxAgents, maxActiveAgents)

	// Verify the updates
	assert.Equal(t, 1, len(updates))
	assert.Equal(t, remoteagentsproto.AgentListUpdateType_AGENT_LIST_ALL_AGENTS, updates[0].Type)
	assert.Equal(t, 2, len(updates[0].AllAgents))
	assert.Equal(t, &maxAgents, updates[0].MaxAgents)
	assert.Equal(t, &maxActiveAgents, updates[0].MaxActiveAgents)

	// Verify the internal state
	assert.False(t, processor.isFirstRequest)
	assert.Equal(t, 2, len(processor.lastAgentMap))
	assert.NotNil(t, processor.lastAgentMap["agent1"])
	assert.NotNil(t, processor.lastAgentMap["agent2"])
}

// TestProcessAgentListWithMatchingTimestamp tests the ProcessAgentList method with a matching timestamp
func TestProcessAgentListWithMatchingTimestamp(t *testing.T) {
	// Create a timestamp
	timestamp := timestamppb.New(time.Now())

	// Create a processor with the timestamp
	processor := NewAgentListProcessor(timestamp)

	// Create initial agents
	agent1 := createTestAgent("agent1", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)
	agent2 := createTestAgent("agent2", remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE)
	initialAgents := []*remoteagentsproto.Agent{agent1, agent2}

	// Process the initial agent list to set up the internal state
	maxAgents := int32(10)
	maxActiveAgents := int32(5)
	updates := processor.ProcessAgentList(initialAgents, maxAgents, maxActiveAgents)
	assert.Equal(t, 1, len(updates)) // Should get AGENT_LIST_ALL_AGENTS for first request

	// Create updated agents
	agent1Updated := createTestAgent("agent1", remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE) // Status changed
	agent3 := createTestAgent("agent3", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)     // New agent
	updatedAgents := []*remoteagentsproto.Agent{agent1Updated, agent3}                          // agent2 removed

	// Process the updated agent list
	updates = processor.ProcessAgentList(updatedAgents, maxAgents, maxActiveAgents)

	// Verify the updates - should get incremental updates
	assert.Equal(t, 3, len(updates))

	// Check for the specific update types
	var hasUpdated, hasAdded, hasDeleted bool
	for _, update := range updates {
		switch update.Type {
		case remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_UPDATED:
			hasUpdated = true
			assert.Equal(t, "agent1", update.Agent.RemoteAgentId)
		case remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_ADDED:
			hasAdded = true
			assert.Equal(t, "agent3", update.Agent.RemoteAgentId)
		case remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_DELETED:
			hasDeleted = true
			assert.Equal(t, "agent2", *update.DeletedAgentId)
		}
	}

	assert.True(t, hasUpdated, "Should have an AGENT_UPDATED update")
	assert.True(t, hasAdded, "Should have an AGENT_ADDED update")
	assert.True(t, hasDeleted, "Should have an AGENT_DELETED update")

	// Verify the internal state
	assert.Equal(t, 2, len(processor.lastAgentMap))
	assert.NotNil(t, processor.lastAgentMap["agent1"])
	assert.NotNil(t, processor.lastAgentMap["agent3"])
	assert.Nil(t, processor.lastAgentMap["agent2"])
}

// TestProcessAgentListWithNonMatchingTimestamp tests that only first request gets full state
func TestProcessAgentListWithNonMatchingTimestamp(t *testing.T) {
	// Create a timestamp for the client
	clientTimestamp := timestamppb.New(time.Now().Add(-1 * time.Hour)) // 1 hour ago

	// Create a processor with the client timestamp
	processor := NewAgentListProcessor(clientTimestamp)

	// Create initial agents
	agent1 := createTestAgent("agent1", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)
	agent2 := createTestAgent("agent2", remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE)
	initialAgents := []*remoteagentsproto.Agent{agent1, agent2}

	// Process the initial agent list to set up the internal state
	maxAgents := int32(10)
	maxActiveAgents := int32(5)
	updates := processor.ProcessAgentList(initialAgents, maxAgents, maxActiveAgents)
	assert.Equal(t, 1, len(updates)) // Should get AGENT_LIST_ALL_AGENTS for first request

	// Create updated agents
	agent1Updated := createTestAgent("agent1", remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE) // Status changed
	agent3 := createTestAgent("agent3", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)     // New agent
	updatedAgents := []*remoteagentsproto.Agent{agent1Updated, agent3}                          // agent2 removed

	// Process the updated agent list
	updates = processor.ProcessAgentList(updatedAgents, maxAgents, maxActiveAgents)

	// Verify the updates - should get incremental updates only
	// Even though timestamps don't match, only first request gets full state
	assert.Equal(t, 3, len(updates)) // Should get individual updates: DELETED, UPDATED, ADDED

	// Check for the specific update types (order may vary)
	var hasUpdated, hasAdded, hasDeleted bool
	for _, update := range updates {
		switch update.Type {
		case remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_UPDATED:
			hasUpdated = true
			assert.Equal(t, "agent1", update.Agent.RemoteAgentId)
		case remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_ADDED:
			hasAdded = true
			assert.Equal(t, "agent3", update.Agent.RemoteAgentId)
		case remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_DELETED:
			hasDeleted = true
			assert.Equal(t, "agent2", *update.DeletedAgentId)
		}
	}
	assert.True(t, hasUpdated, "Should have AGENT_UPDATED")
	assert.True(t, hasAdded, "Should have AGENT_ADDED")
	assert.True(t, hasDeleted, "Should have AGENT_DELETED")

	// Verify the internal state
	assert.Equal(t, 2, len(processor.lastAgentMap))
	assert.NotNil(t, processor.lastAgentMap["agent1"])
	assert.NotNil(t, processor.lastAgentMap["agent3"])
	assert.Nil(t, processor.lastAgentMap["agent2"])
}

// TestNoChanges tests the case where there are no changes to the agent list
func TestNoChanges(t *testing.T) {
	// Create a processor
	processor := NewAgentListProcessor(nil)

	// Create initial agents
	agent1 := createTestAgent("agent1", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)
	agent2 := createTestAgent("agent2", remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE)
	initialAgents := []*remoteagentsproto.Agent{agent1, agent2}

	// Process the initial agent list to set up the internal state
	maxAgents := int32(10)
	maxActiveAgents := int32(5)
	updates := processor.ProcessAgentList(initialAgents, maxAgents, maxActiveAgents)
	assert.Equal(t, 1, len(updates)) // Should get AGENT_LIST_ALL_AGENTS for first request

	// Process the same agent list again (should get no updates since no changes and not first request)
	updates = processor.ProcessAgentList(initialAgents, maxAgents, maxActiveAgents)

	// Verify no updates
	assert.Equal(t, 0, len(updates))

	// Verify the internal state remains the same
	assert.Equal(t, 2, len(processor.lastAgentMap))
	assert.NotNil(t, processor.lastAgentMap["agent1"])
	assert.NotNil(t, processor.lastAgentMap["agent2"])
}
