// Package main implements the Remote Agents server functionality.
//
// This file contains the AgentListProcessor, which is responsible for processing
// agent list data and generating appropriate streaming updates for clients.
//
// AgentListProcessor is separated from the AgentListStreamer to facilitate unit
// testing by isolating the logic from BigTable, gRPC streams, and other dependencies.
//
// It handles:
//  1. Processing initial agent list when clients first connect to a stream
//  2. Detecting and generating updates for agent status changes, additions, and deletions
//  3. Managing timestamps to ensure only new updates are sent to clients
//  4. Tracking agent state changes to minimize unnecessary updates
package main

import (
	"time"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
)

// AgentListProcessor handles the business logic of processing agent list updates
type AgentListProcessor struct {
	// The timestamp provided by the client (if any) - currently unused but kept for future optimization
	clientTimestamp *timestamppb.Timestamp
	lastAgentMap    map[string]*remoteagentsproto.Agent
	// Tracks if this is the very first request to this processor instance
	// Set to false after the first ProcessAgentList call
	isFirstRequest bool
}

// NewAgentListProcessor creates a new AgentListProcessor
func NewAgentListProcessor(clientTimestamp *timestamppb.Timestamp) *AgentListProcessor {
	// clientTimestamp is currently unused but kept for future timestamp-based optimization
	return &AgentListProcessor{
		clientTimestamp: clientTimestamp,
		lastAgentMap:    make(map[string]*remoteagentsproto.Agent),
		isFirstRequest:  true, // Always true initially
	}
}

// ProcessAgentList processes new agent list data and generates appropriate updates
func (p *AgentListProcessor) ProcessAgentList(
	agents []*remoteagentsproto.Agent,
	maxAgents int32,
	maxActiveAgents int32,
) []*remoteagentsproto.AgentListUpdate {
	var updates []*remoteagentsproto.AgentListUpdate
	currentTime := timestamppb.New(time.Now())

	// Send full state only on the first request
	// After the first request, only incremental updates are sent
	// TODO: Implement timestamp-based optimization to avoid sending full state when client already has up-to-date data
	if p.isFirstRequest {
		update := &remoteagentsproto.AgentListUpdate{
			Type:            remoteagentsproto.AgentListUpdateType_AGENT_LIST_ALL_AGENTS,
			UpdateTimestamp: currentTime,
			AllAgents:       agents,
			MaxAgents:       &maxAgents,
			MaxActiveAgents: &maxActiveAgents,
		}
		updates = append(updates, update)

		// Update our internal state
		p.updateInternalState(agents)
		p.isFirstRequest = false

		return updates
	}

	// Create a map of current agents for efficient lookup
	currentAgentMap := make(map[string]*remoteagentsproto.Agent)
	for _, agent := range agents {
		currentAgentMap[agent.RemoteAgentId] = agent
	}

	// Check for new agents and updated agents
	for agentId, currentAgent := range currentAgentMap {
		lastAgent, existed := p.lastAgentMap[agentId]

		if !existed {
			// This is a new agent
			update := &remoteagentsproto.AgentListUpdate{
				Type:            remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_ADDED,
				UpdateTimestamp: currentTime,
				Agent:           currentAgent,
			}
			updates = append(updates, update)
		} else if !proto.Equal(currentAgent, lastAgent) {
			// This agent has been updated
			update := &remoteagentsproto.AgentListUpdate{
				Type:            remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_UPDATED,
				UpdateTimestamp: currentTime,
				Agent:           currentAgent,
			}
			updates = append(updates, update)
		}
	}

	// Check for deleted agents
	for agentId := range p.lastAgentMap {
		if _, exists := currentAgentMap[agentId]; !exists {
			// This agent has been deleted
			update := &remoteagentsproto.AgentListUpdate{
				Type:            remoteagentsproto.AgentListUpdateType_AGENT_LIST_AGENT_DELETED,
				UpdateTimestamp: currentTime,
				DeletedAgentId:  &agentId,
				MaxAgents:       &maxAgents,
				MaxActiveAgents: &maxActiveAgents,
			}
			updates = append(updates, update)
		}
	}

	// Update our internal state if we have updates
	if len(updates) > 0 {
		p.updateInternalState(agents)
	}

	return updates
}

// updateInternalState updates the processor's internal state with the current agent list
func (p *AgentListProcessor) updateInternalState(agents []*remoteagentsproto.Agent) {
	// Clear the existing map
	p.lastAgentMap = make(map[string]*remoteagentsproto.Agent)

	// Add all current agents to the map
	for _, agent := range agents {
		// Create a deep copy of the agent to avoid reference issues
		agentCopy := proto.Clone(agent).(*remoteagentsproto.Agent)
		p.lastAgentMap[agent.RemoteAgentId] = agentCopy
	}
}
