// Package main implements the Remote Agents server functionality.
//
// This file contains the AgentListStreamer, which is responsible for streaming
// agent list updates to clients. It implements a server-side streaming gRPC endpoint
// that efficiently delivers real-time updates about agent status changes, new agents,
// and agent deletions.
//
// Instead of clients repeatedly polling for updates, the server maintains a long-lived
// connection and streams updates as they occur.
//
// Features of the implementation:
//  1. Polling of BigTable for agent list changes
//  2. Support for reconnection scenarios through timestamp tracking
//  3. Periodic forced responses to verify client connection liveness
//  4. Real-time notifications for agent lifecycle events
package main

import (
	"context"
	"errors"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/rs/zerolog/log"
)

// Constants for agent list streaming
const (
	// Interval at which we poll for agent list updates
	AgentListPollingInterval = 5 * time.Second

	// Maximum number of iterations without a response.
	//
	// In the actix framework used by API proxy, the only way to detect that a client has
	// closed the connection is to attempt to send a response. If the send fails, the
	// connection is closed. When API proxy detects that the connection is closed, it will
	// cancel the stream.
	//
	// For that reason, it is important to periodically send a response even if there are
	// no updates.
	AgentListMaxSkippedIterations = 2
)

// AgentListStreamer defines the interface for streaming agent list updates
type AgentListStreamer interface {
	// StreamAgentList streams agent list updates for a user with real-time updates
	StreamAgentList(
		ctx context.Context,
		stream remoteagentsproto.RemoteAgents_ListAgentsStreamServer,
		req *remoteagentsproto.ListAgentsStreamRequest,
		userId string,
		tenantID string,
		requestContext *requestcontext.RequestContext,
	) error
}

// bigTableAgentListStreamer implements AgentListStreamer using BigTable
type bigTableAgentListStreamer struct {
	bigtableProxyClient bigtableproxy.BigtableProxyClient
	memstoreClient      memstoreclient.MemstoreClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// NewBigTableAgentListStreamer creates a new bigTableAgentListStreamer
func NewBigTableAgentListStreamer(bigtableProxyClient bigtableproxy.BigtableProxyClient, memstoreClient memstoreclient.MemstoreClient, featureFlagHandle featureflags.FeatureFlagHandle) AgentListStreamer {
	return &bigTableAgentListStreamer{
		bigtableProxyClient: bigtableProxyClient,
		memstoreClient:      memstoreClient,
		featureFlagHandle:   featureFlagHandle,
	}
}

// StreamAgentList implements AgentListStreamer.StreamAgentList
func (s *bigTableAgentListStreamer) StreamAgentList(
	ctx context.Context,
	stream remoteagentsproto.RemoteAgents_ListAgentsStreamServer,
	req *remoteagentsproto.ListAgentsStreamRequest,
	userId string,
	tenantID string,
	requestContext *requestcontext.RequestContext,
) error {
	// Create a processor to handle the business logic
	processor := NewAgentListProcessor(req.LastUpdateTimestamp)

	// Keep track of skipped iterations to periodically verify the connection is still alive
	skippedIterations := 0

	// Timer for polling - start with immediate execution
	timer := time.NewTimer(0) // 0 duration for immediate first execution
	defer timer.Stop()

	// Create a channel for the Pub/Sub subscription
	messageChan, cancelSubscription, err := SubscribeToAgentListNotifications(ctx, requestContext, s.memstoreClient, userId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to subscribe to agent list notifications for user %s", userId)
		// Continue without Pub/Sub, falling back to polling
	} else if cancelSubscription != nil {
		defer cancelSubscription()
	}

	// Main streaming loop
	for {
		select {
		case <-ctx.Done():
			err := context.Cause(ctx)
			if errors.Is(err, context.Canceled) {
				log.Ctx(ctx).Info().
					Str("error", err.Error()).
					Msg("Agent list stream ended due to context cancellation")
			} else {
				log.Ctx(ctx).Warn().
					Str("error", err.Error()).
					Msg("Agent list stream ended due to context error")
			}
			return err
		case message := <-messageChan:
			// Process the Pub/Sub notification
			err := processAgentListNotification(
				ctx,
				message,
				userId,
				tenantID,
				requestContext,
				s.bigtableProxyClient,
				processor,
				stream,
				&skippedIterations,
				s.featureFlagHandle,
			)
			if err != nil {
				return err
			}

		case <-timer.C:
			// Reset the timer for the next iteration
			timer.Reset(AgentListPollingInterval)

			// Process the timer event using the same function as Pub/Sub notifications
			// We pass nil for the message since we're not processing a notification
			err := processAgentListNotification(
				ctx,
				nil, // No message for timer events
				userId,
				tenantID,
				requestContext,
				s.bigtableProxyClient,
				processor,
				stream,
				&skippedIterations,
				s.featureFlagHandle,
			)
			if err != nil {
				return err
			}

			// If we have no meaningful updates and we've reached the max skipped iterations,
			// send an empty response to verify the connection is still alive
			if skippedIterations > AgentListMaxSkippedIterations {
				// Create an empty response
				emptyResponse := &remoteagentsproto.ListAgentsStreamResponse{
					Updates: []*remoteagentsproto.AgentListUpdate{},
				}

				// Send the empty response
				if err := stream.Send(emptyResponse); err != nil {
					log.Ctx(ctx).Warn().Err(err).Msgf("Failed to send empty agent list update for user %s", userId)
					return err
				}

				// Reset the counter after sending a response
				skippedIterations = 0
			}
		}
	}
}

// processAgentListNotification processes a Pub/Sub notification for agent list changes
func processAgentListNotification(
	ctx context.Context,
	message []byte,
	userId string,
	tenantID string,
	requestContext *requestcontext.RequestContext,
	bigtableProxyClient bigtableproxy.BigtableProxyClient,
	processor *AgentListProcessor,
	stream remoteagentsproto.RemoteAgents_ListAgentsStreamServer,
	skippedIterations *int,
	featureFlagHandle featureflags.FeatureFlagHandle,
) error {
	// Check if this is a Pub/Sub notification or a timer event
	if message != nil {
		// We received a notification via Pub/Sub
		log.Ctx(ctx).Debug().Msgf("Received Pub/Sub notification for user %s", userId)

		// Parse the message to get the agent ID and operation type
		notification, err := UnmarshallAgentListNotification(message)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to parse Pub/Sub notification for user %s", userId)
			return nil
		}

		log.Ctx(ctx).Debug().Msgf("Parsed agent list notification: %+v", notification)
	} else {
		// This is a timer event, not a Pub/Sub notification
		log.Ctx(ctx).Debug().Msgf("Processing timer event for user %s", userId)

		// Increment the counter for iterations without meaningful updates
		*skippedIterations++
	}

	// Fetch the current agent list from BigTable
	agentMap, err := readAllRemoteAgentsForUser(ctx, requestContext, bigtableProxyClient, tenantID, userId, getAgentDeletionTTLDays(featureFlagHandle))
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to read agent list for user %s", userId)
		return nil
	}

	// Convert map to slice
	agents := make([]*remoteagentsproto.Agent, 0, len(agentMap))
	for _, agent := range agentMap {
		agents = append(agents, agent)
	}

	// Get the max agents configuration
	max_num_agents_for_user, err := max_remote_agents_per_user.Get(featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get maximum number of agents per user")
		return err
	}

	max_num_active_agents_for_user, err := max_active_remote_agents_per_user.Get(featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get maximum number of active agents per user")
		return err
	}

	// Process agent list updates
	updates := processor.ProcessAgentList(agents, int32(max_num_agents_for_user), int32(max_num_active_agents_for_user))

	// Check if we have any meaningful updates to send
	if len(updates) == 0 {
		// No updates to send - for both Pub/Sub notifications and timer events,
		// we just return (keepalive handling is done by the caller for timer events)
		return nil
	}

	// Create a single response with all updates
	combinedResponse := &remoteagentsproto.ListAgentsStreamResponse{
		Updates: updates,
	}

	// Send the combined response
	if err := stream.Send(combinedResponse); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to send combined agent list update for user %s", userId)
		return err
	}

	// Reset the skipped iterations counter since we sent a meaningful response
	*skippedIterations = 0

	log.Ctx(ctx).Debug().Msgf("Sent %d agent list updates for user %s", len(updates), userId)
	return nil
}
