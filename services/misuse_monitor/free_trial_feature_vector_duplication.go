package main

import (
	"context"
	"fmt"
	"strconv"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialFeatureVectorDuplicationDryRunFlag = featureflags.NewBoolFlag("free_trial_feature_vector_duplication_dry_run", true)

type FreeTrialFeatureVectorDuplicationJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	tenantCache         tenantwatcherclient.TenantCache
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*FreeTrialFeatureVectorDuplicationJob)(nil)

func NewFreeTrialFeatureVectorDuplicationJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialFeatureVectorDuplicationJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialFeatureVectorDuplicationJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-feature-vector-duplicate",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialFeatureVectorDuplicationJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialFeatureVectorDuplicationJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting suspect users: %w", err)
	}

	log.Info().Msgf("Total of %d suspect users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error suspending users: %w", err)
	}

	return nil
}

type featureVectorSuspect struct {
	ID              string   `bigquery:"opaque_user_id"`
	TenantID        string   `bigquery:"tenant_id"`
	MatchReason     string   `bigquery:"match_reason"`
	InactiveIds     []string `bigquery:"inactive_ids"`
	TrialIds        []string `bigquery:"trial_ids"`
	ProfessionalIds []string `bigquery:"professional_ids"`
	CommunityIds    []string `bigquery:"community_ids"`
	TeamIds         []string `bigquery:"team_ids"`
	EnterpriseIds   []string `bigquery:"enterprise_ids"`
}

func (m *FreeTrialFeatureVectorDuplicationJob) getSuspects(ctx context.Context) ([]*featureVectorSuspect, error) {
	// Construct the query.
	query := m.bqClient.Query(`
	DECLARE empty_hash STRING DEFAULT 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855';
	CREATE TEMP FUNCTION arrays_overlap(arr1 ARRAY<STRING>, arr2 ARRAY<STRING>)
	RETURNS BOOL AS (
		(SELECT COUNT(*) FROM UNNEST(arr1) AS val WHERE val IN UNNEST(arr2)) > 0
	);
	WITH
	vector_values AS (
		SELECT
			opaque_user_id,
			JSON_EXTRACT_SCALAR(feature_vector, '$.0') AS vscode,
			JSON_EXTRACT_SCALAR(feature_vector, '$.1') AS machine_id,
			JSON_EXTRACT_SCALAR(feature_vector, '$.2') AS os,
			JSON_EXTRACT_SCALAR(feature_vector, '$.3') AS cpu,
			JSON_EXTRACT_SCALAR(feature_vector, '$.4') AS memory,
			JSON_EXTRACT_SCALAR(feature_vector, '$.5') AS num_cpus,
			JSON_EXTRACT_SCALAR(feature_vector, '$.6') AS hostname,
			JSON_EXTRACT_SCALAR(feature_vector, '$.7') AS arch,
			JSON_EXTRACT_SCALAR(feature_vector, '$.8') AS username,
			JSON_EXTRACT_SCALAR(feature_vector, '$.9') AS mac_addresses,
			JSON_EXTRACT_SCALAR(feature_vector, '$.10') AS os_release,
			JSON_EXTRACT_SCALAR(feature_vector, '$.11') AS kernel_version,
			JSON_EXTRACT_SCALAR(feature_vector, '$.17') AS home_directory_ino
		FROM feature_vector_report
	),
	feature_data AS (
		SELECT
			opaque_user_id,
			ARRAY_AGG(DISTINCT CASE WHEN vscode != empty_hash THEN vscode END IGNORE NULLS) AS vscode_values,
			ARRAY_AGG(DISTINCT CASE WHEN machine_id != empty_hash THEN machine_id END IGNORE NULLS) AS machine_id_values,
			ARRAY_AGG(DISTINCT CASE WHEN os != empty_hash THEN os END IGNORE NULLS) AS os_values,
			ARRAY_AGG(DISTINCT CASE WHEN cpu != empty_hash THEN cpu END IGNORE NULLS) AS cpu_values,
			ARRAY_AGG(DISTINCT CASE WHEN memory != empty_hash THEN memory END IGNORE NULLS) AS memory_values,
			ARRAY_AGG(DISTINCT CASE WHEN num_cpus != empty_hash THEN num_cpus END IGNORE NULLS) AS num_cpus_values,
			ARRAY_AGG(DISTINCT CASE WHEN hostname != empty_hash THEN hostname END IGNORE NULLS) AS hostname_values,
			ARRAY_AGG(DISTINCT CASE WHEN arch != empty_hash THEN arch END IGNORE NULLS) AS arch_values,
			ARRAY_AGG(DISTINCT CASE WHEN username != empty_hash THEN username END IGNORE NULLS) AS username_values,
			ARRAY_AGG(DISTINCT CASE WHEN mac_addresses != empty_hash THEN mac_addresses END IGNORE NULLS) AS mac_addresses_values,
			ARRAY_AGG(DISTINCT CASE WHEN os_release != empty_hash THEN os_release END IGNORE NULLS) AS os_release_values,
			ARRAY_AGG(DISTINCT CASE WHEN kernel_version != empty_hash THEN kernel_version END IGNORE NULLS) AS kernel_version_values,
			ARRAY_AGG(DISTINCT CASE WHEN home_directory_ino != empty_hash THEN home_directory_ino END IGNORE NULLS) AS home_directory_ino_values
		FROM vector_values
		GROUP BY opaque_user_id
	),
	-- identify users
	user_id AS (
		SELECT
			id AS opaque_user_id,
			tenant_ids[0] AS tenant_id,
			orb_subscription_id AS subscription_id,
			LOWER(email) AS email,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE'
			) as suspended,
			suspension_exempt as exempt
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id AS tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
				THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- determine subscription type
	sub AS (
		SELECT
			subscription_id,
			CASE
				WHEN orb_status = 'ORB_STATUS_ACTIVE'
				THEN CASE
					WHEN external_plan_id = 'orb_trial_plan'
					THEN 'TRIAL'
					ELSE 'ACTIVE'
				END
				ELSE 'INACTIVE'
			END AS subscription_category
		FROM subscription
	),
	-- build user profile from all the above
	profile AS (
		SELECT
			feature_data.*,
			user_id.tenant_id,
			user_id.subscription_id,
			user_id.email,
			user_id.suspended,
			user_id.exempt,
			CASE
				WHEN sub.subscription_category = 'ACTIVE'
				THEN tier.tier
				WHEN sub.subscription_category = 'INACTIVE'
				THEN CASE
					-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
					WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
					THEN tier.tier
					ELSE 'INACTIVE'
				END
				ELSE sub.subscription_category
			END as category,
			tier.tier,
			sub.subscription_category
		FROM user_id
		JOIN feature_data ON feature_data.opaque_user_id = user_id.opaque_user_id
		JOIN tier ON user_id.tenant_id = tier.tenant_id
		JOIN sub ON user_id.subscription_id = sub.subscription_id
	),
	-- Want to know all detected trial users as suspects.
	suspect AS (
		SELECT
		profile.*
		FROM profile
		WHERE category = 'TRIAL'
		AND NOT profile.suspended
		AND NOT profile.exempt
	),
	matches AS (
		SELECT
			suspect.opaque_user_id,
			suspect.tenant_id,
			CASE
				WHEN arrays_overlap(suspect.machine_id_values, other.machine_id_values)
				AND arrays_overlap(suspect.hostname_values, other.hostname_values)
				AND arrays_overlap(suspect.username_values, other.username_values)
				AND arrays_overlap(suspect.mac_addresses_values, other.mac_addresses_values)
				THEN 1  -- FULL_MATCH
				WHEN arrays_overlap(suspect.machine_id_values, other.machine_id_values)
					AND arrays_overlap(suspect.hostname_values, other.hostname_values)
					AND arrays_overlap(suspect.username_values, other.username_values)
				THEN 2 -- MACHINE_HOST_USER_MATCH
				WHEN arrays_overlap(suspect.mac_addresses_values, other.mac_addresses_values)
					AND arrays_overlap(suspect.hostname_values, other.hostname_values)
					AND arrays_overlap(suspect.username_values, other.username_values)
				THEN 3 -- MAC_ADDR_HOST_USER_MATCH
				WHEN arrays_overlap(suspect.machine_id_values, other.machine_id_values)
				THEN 4  -- MACHINE_ID_MATCH
				WHEN arrays_overlap(suspect.mac_addresses_values, other.mac_addresses_values)
				THEN 5  -- MAC_ADDR_MATCH
				WHEN arrays_overlap(suspect.home_directory_ino_values, other.home_directory_ino_values)
				THEN 6  -- HOME_DIRECTORY_INO_MATCH
			END as match_reason,
			other.category as other_category,
			other.opaque_user_id as other_user_id
		FROM suspect
		JOIN profile AS other
		ON suspect.opaque_user_id != other.opaque_user_id
		AND suspect.email != other.email
		WHERE arrays_overlap(suspect.machine_id_values, other.machine_id_values)
		OR arrays_overlap(suspect.mac_addresses_values, other.mac_addresses_values)
		OR arrays_overlap(suspect.home_directory_ino_values, other.home_directory_ino_values)
	)
	SELECT
		opaque_user_id,
		tenant_id,
		CASE MIN(match_reason)
			WHEN 1 THEN 'FULL_MATCH'
			WHEN 2 THEN 'MACHINE_HOST_USER_MATCH'
			WHEN 3 THEN 'MAC_ADDR_HOST_USER_MATCH'
			WHEN 4 THEN 'MACHINE_ID_MATCH'
			WHEN 5 THEN 'MAC_ADDR_MATCH'
			WHEN 6 THEN 'HOME_DIRECTORY_INO_MATCH'
		END as match_reason,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'INACTIVE' THEN other_user_id END IGNORE NULLS) AS inactive_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'TRIAL' THEN other_user_id END IGNORE NULLS) AS trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'PROFESSIONAL' THEN other_user_id END IGNORE NULLS) AS professional_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'COMMUNITY' THEN other_user_id END IGNORE NULLS) AS community_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'TEAM' THEN other_user_id END IGNORE NULLS) AS team_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'ENTERPRISE' THEN other_user_id END IGNORE NULLS) AS enterprise_ids
	FROM matches
	GROUP BY 1, 2
	ORDER BY ARRAY_LENGTH(trial_ids) + ARRAY_LENGTH(inactive_ids) DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var featureVectorSuspects []*featureVectorSuspect
	for {
		var row featureVectorSuspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			featureVectorSuspects = append(featureVectorSuspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(featureVectorSuspects))
	return featureVectorSuspects, nil
}

func (m *FreeTrialFeatureVectorDuplicationJob) suspendSuspects(
	ctx context.Context,
	suspects []*featureVectorSuspect,
) error {
	dryRun, err := freeTrialFeatureVectorDuplicationDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 200

	sessionId := requestcontext.NewRandomRequestSessionId()
	for _, suspect := range suspects {

		tenant, err := m.tenantCache.GetTenant(suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "tenant_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting tenant %s", suspect.TenantID)
			continue
		}

		// Do the blocking.
		token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
			ctx, suspect.TenantID, tenant.ShardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW},
		)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "token_exchange_error", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting token for tenant %s", suspect.TenantID)
			continue
		}
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

		// Suspensions are issued for duplicate inactive, trial, professional and community accounts only.
		// Ignore duplicate teams and enterprise accounts if those are the only duplicates.
		if len(suspect.InactiveIds) == 0 && len(suspect.TrialIds) == 0 && len(suspect.ProfessionalIds) == 0 && len(suspect.CommunityIds) == 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "ineligible_duplicate", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping suspect user %s in tenant %s, no inactive, trial, professional or community duplicates", suspect.ID, suspect.TenantID)
			continue
		}

		// Rolling out slowly. Hold off machine id only matches for now.
		if suspect.MatchReason == "MACHINE_ID_MATCH" {
			MisuseActionOutcome.WithLabelValues(m.jobName, "machine_id_match", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping suspect user %s in tenant %s: machine ID match", suspect.ID, suspect.TenantID)
			continue
		}

		// Check if the user is exempt or already suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, suspect.ID, &suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", suspect.ID, suspect.TenantID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt in tenant %s", suspect.ID, suspect.TenantID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse in tenant %s", suspect.ID, suspect.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		evidence := fmt.Sprintf("Duplicate accounts via feature vectors %s: inactive: %d trial: %d professional: %d community: %d team: %d enterprise: %d",
			suspect.MatchReason, len(suspect.InactiveIds), len(suspect.TrialIds), len(suspect.ProfessionalIds), len(suspect.CommunityIds), len(suspect.TeamIds), len(suspect.EnterpriseIds))
		log.Info().Msgf("Misuse monitor detected free trial duplication via feature vectors by user %s in tenant %s. %s",
			suspect.ID, suspect.TenantID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, suspect.ID, suspect.TenantID, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", suspect.ID, suspect.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, suspect.ID, suspect.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
