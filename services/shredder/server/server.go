package main

import (
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type shredderServer struct {
	pb.UnimplementedShredderServer

	featureFlagHandle featureflags.FeatureFlagHandle
}

func NewShredderServer(featureFlagHandle featureflags.FeatureFlagHandle) *shredderServer {
	return &shredderServer{
		featureFlagHandle: featureFlagHandle,
	}
}

func (s *shredderServer) Close() {
}
