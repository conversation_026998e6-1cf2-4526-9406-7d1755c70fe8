load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")

proto_library(
    name = "shredder_proto",
    srcs = ["shredder.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "shredder_go_proto",
    importpath = "github.com/augmentcode/augment/services/shredder/proto",
    proto = ":shredder_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)
