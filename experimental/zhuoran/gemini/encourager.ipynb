{"cells": [{"cell_type": "code", "execution_count": null, "id": "13d3c1d7", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "id": "998d3323", "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/projects/gemini/replay_batch_results2.pkl\", \"rb\"\n", ") as f:\n", "    results = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "764b073e", "metadata": {}, "outputs": [], "source": ["for request_index, request_results in enumerate(results):\n", "    for round_index, round_results in enumerate(request_results):\n", "        for trial_index, result in enumerate(round_results):\n", "            if result[-1] is None:\n", "                result = (*result[:-1], [])\n", "\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/projects/gemini/replay_batch_results2.pkl\", \"wb\"\n", ") as f:\n", "    pickle.dump(results, f)"]}, {"cell_type": "code", "execution_count": null, "id": "a0a6a6e2", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "from research.tools.chat_replay.replay_utils import count_tool_calls\n", "\n", "lazy_delegation_confusion = np.zeros((4, 4))\n", "lack_of_action_confusion = np.zeros((4, 4))\n", "disagreement_indices = []\n", "for request_index, request_results in enumerate(results):\n", "    for round_index, round_results in enumerate(request_results):\n", "        for trial_index, result in enumerate(round_results):\n", "            text_length, tool_calls, probing_results, response, prompt_output = result\n", "            pro_lazy_delegation, pro_lack_of_action = probing_results[\"pro\"].values()\n", "            flash_lazy_delegation, flash_lack_of_action = probing_results[\n", "                \"flash\"\n", "            ].values()\n", "            pro_standalone_lazy_delegation, pro_standalone_lack_of_action = (\n", "                probing_results[\"pro_standalone\"].values()\n", "            )\n", "            flash_standalone_lazy_delegation, flash_standalone_lack_of_action = (\n", "                probing_results[\"flash_standalone\"].values()\n", "            )\n", "            lazy_delegation_confusion[0] += pro_lazy_delegation\n", "            lazy_delegation_confusion[1] += flash_lazy_delegation\n", "            lazy_delegation_confusion[2] += pro_standalone_lazy_delegation\n", "            lazy_delegation_confusion[3] += flash_standalone_lazy_delegation\n", "            lazy_delegation_confusion[:, 0] -= pro_lazy_delegation\n", "            lazy_delegation_confusion[:, 1] -= flash_lazy_delegation\n", "            lazy_delegation_confusion[:, 2] -= pro_standalone_lazy_delegation\n", "            lazy_delegation_confusion[:, 3] -= flash_standalone_lazy_delegation\n", "            lack_of_action_confusion[0] += pro_lack_of_action\n", "            lack_of_action_confusion[1] += flash_lack_of_action\n", "            lack_of_action_confusion[2] += pro_standalone_lack_of_action\n", "            lack_of_action_confusion[3] += flash_standalone_lack_of_action\n", "            lack_of_action_confusion[:, 0] -= pro_lack_of_action\n", "            lack_of_action_confusion[:, 1] -= flash_lack_of_action\n", "            lack_of_action_confusion[:, 2] -= pro_standalone_lack_of_action\n", "            lack_of_action_confusion[:, 3] -= flash_standalone_lack_of_action\n", "            tool_call_count = count_tool_calls(response[1:])\n", "            if (\n", "                (\n", "                    0\n", "                    < (\n", "                        pro_lazy_delegation\n", "                        + flash_lazy_delegation\n", "                        + pro_standalone_lazy_delegation\n", "                        + flash_standalone_lazy_delegation\n", "                    )\n", "                    < 4\n", "                    or 0\n", "                    < (\n", "                        pro_lack_of_action\n", "                        + flash_lack_of_action\n", "                        + pro_standalone_lack_of_action\n", "                        + flash_standalone_lack_of_action\n", "                    )\n", "                    < 4\n", "                )\n", "                and tool_call_count == 0\n", "                and len(response) > 1\n", "            ):\n", "                disagreement_indices.append((request_index, round_index, trial_index))\n", "\n", "print(lazy_delegation_confusion)\n", "print(lack_of_action_confusion)\n", "print(len(disagreement_indices))"]}, {"cell_type": "code", "execution_count": null, "id": "0f3d09ed", "metadata": {}, "outputs": [], "source": ["disagreement_metaindex = 0"]}, {"cell_type": "code", "execution_count": null, "id": "ae184649", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from research.tools.chat_replay.replay_utils import print_response\n", "\n", "print(disagreement_metaindex)\n", "request_index, round_, trial = disagreement_indices[disagreement_metaindex]\n", "result = results[request_index][round_][trial]\n", "text_length, tool_calls, probing_results, response, prompt_output = result\n", "print(text_length, tool_calls)\n", "print(json.dumps(probing_results, indent=4))\n", "print_response(\n", "    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)\n", ")\n", "disagreement_metaindex += 1"]}, {"cell_type": "code", "execution_count": null, "id": "b6bc389d", "metadata": {}, "outputs": [], "source": ["# request_index = 3\n", "# round_ = 3\n", "# trial = 1\n", "# result = results[request_index][round_][trial]"]}, {"cell_type": "code", "execution_count": null, "id": "1d6497e5", "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_response\n", "\n", "text_length, tool_calls, probing_results, response, prompt_output = result\n", "response = response or []\n", "print(text_length, tool_calls)\n", "print_response(\n", "    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9f53d7f6", "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_chat_history, print_request\n", "\n", "print_chat_history(prompt_output.chat_history, text_limit=100, tool_limit=100)\n", "print(\"=\" * 81)\n", "print_request(prompt_output.message)\n", "print(\"-\" * 81)\n", "print_response(\n", "    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dc04181d", "metadata": {}, "outputs": [], "source": ["LAZY_DELEGATION_PROMPT = \"\"\"Please answer the following questions:\n", "\n", "1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.\n", "2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?\n", "\n", "Your task is to PURELY answer the question above without taking any other action.\n", "\n", "Your answer must end with a clear Yes-No for each question, in the following format:\n", "<answer id=1>Yes</answer>\n", "<answer id=2>No</answer>\n", "\"\"\".strip()"]}, {"cell_type": "code", "execution_count": null, "id": "c757418d", "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    append_to_prompt_output,\n", "    run_model,\n", "    TOOL_DEFINITIONS,\n", ")\n", "\n", "probing_prompt_output = append_to_prompt_output(\n", "    results[request_index][round_][trial][-1],\n", "    response[1:],\n", "    LAZY_DELEGATION_PROMPT,\n", ")\n", "probing_response = run_model(\n", "    probing_prompt_output,\n", "    tool_definitions=TOOL_DEFINITIONS,\n", "    base_model_version=\"gemini2.5-flash\",\n", "    client_type=\"google_genai\",\n", "    yield_final_parameters=True,\n", ")\n", "print_response(\n", "    probing_response[1:],  # type: ignore\n", "    tool_limit=int(1e9),\n", "    string_limit=int(1e9),\n", "    text_limit=int(1e9),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a4ac795b", "metadata": {}, "outputs": [], "source": ["LAZY_DELEGATION_STANDALONE_PROMPT = \"\"\"The previous message is a single turn in an AI agent's execution trajectory. Please answer the following questions:\n", "\n", "1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.\n", "2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?\n", "\n", "Your task is to PURELY answer the question above without taking any other action.\n", "\n", "Your answer must end with a clear Yes-No for each question, in the following format:\n", "<answer id=1>Yes</answer>\n", "<answer id=2>No</answer>\n", "\"\"\".strip()"]}, {"cell_type": "code", "execution_count": null, "id": "24ec27de", "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput\n", "from base.prompt_format.common import Exchange\n", "from research.tools.chat_replay.replay_utils import (\n", "    run_model,\n", "    TOOL_DEFINITIONS,\n", ")\n", "\n", "probing_prompt_output = StructuredChatPromptOutput(\n", "    system_prompt=\"\",\n", "    chat_history=[Exchange(request_message=[], response_text=response[1:])],\n", "    message=LAZY_DELEGATION_STANDALONE_PROMPT,\n", "    retrieved_chunks_in_prompt=[],\n", ")\n", "probing_response = run_model(\n", "    probing_prompt_output,\n", "    tool_definitions=TOOL_DEFINITIONS,\n", "    base_model_version=\"gemini2.5-flash\",\n", "    client_type=\"google_genai\",\n", "    yield_final_parameters=True,\n", ")\n", "print_response(\n", "    probing_response[1:],  # type: ignore\n", "    tool_limit=int(1e9),\n", "    string_limit=int(1e9),\n", "    text_limit=int(1e9),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8a4ddf57", "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    append_to_prompt_output,\n", "    run_model,\n", "    TOOL_DEFINITIONS,\n", ")\n", "\n", "remedy_prompt_output = append_to_prompt_output(\n", "    results[request_index][round_][trial][-1],\n", "    response[1:],\n", "    \"\"\"Perform the action yourself, instead of asking the user to perform it.\n", "Perform the exact action(s) you asked the user to perform.\"\"\",\n", ")\n", "remedy_response = run_model(\n", "    remedy_prompt_output,\n", "    tool_definitions=TOOL_DEFINITIONS,\n", "    base_model_version=\"gemini2.5\",\n", "    client_type=\"google_genai\",\n", "    yield_final_parameters=True,\n", ")\n", "print_response(\n", "    remedy_response[1:],  # type: ignore\n", "    tool_limit=int(1e9),\n", "    string_limit=int(1e9),\n", "    text_limit=int(1e9),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c8d5bb58", "metadata": {}, "outputs": [], "source": ["# from research.tools.chat_replay.replay_utils import (\n", "#     run_model,\n", "#     print_response,\n", "#     fix_tool_calls,\n", "#     TOOL_DEFINITIONS,\n", "# )\n", "\n", "# prompt_output = prompt_outputs[request_index][round_][trial]\n", "# response = run_model(\n", "#     prompt_output,\n", "#     tool_definitions=TOOL_DEFINITIONS,\n", "#     base_model_version=\"gemini2.5-pro-prev\",\n", "#     client_type=\"google_genai\",\n", "#     yield_final_parameters=True,\n", "# )\n", "# print_response(\n", "#     response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)\n", "# )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}