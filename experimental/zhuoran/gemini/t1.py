from google.genai.types import (
    Content,
    FunctionCall,
    FunctionDeclaration,
    FunctionResponse,
    GenerateContentConfig,
    HarmBlockThreshold,
    HarmCategory,
    Part,
    SafetySetting,
    Schema,
    Tool,
    Type,
)

parameters = {
    "model": "gemini-2.5-pro-preview-03-25",
    "contents": [
        Content(
            parts=[
                Part(
                    video_metadata=None,
                    thought=None,
                    code_execution_result=None,
                    executable_code=None,
                    file_data=None,
                    function_call=None,
                    function_response=None,
                    inline_data=None,
                    text="The backend now expects tool name in tool result nodes. Edit the frontend to supply it.",
                )
            ],
            role="user",
        ),
        Content(
            parts=[
                Part(
                    video_metadata=None,
                    thought=None,
                    code_execution_result=None,
                    executable_code=None,
                    file_data=None,
                    function_call=FunctionCall(
                        id="0",
                        args={
                            "information_request": "Find frontend code that constructs and sends tool result nodes to the backend. Look for structures related to tool calls, tool results, and backend communication."
                        },
                        name="codebase-retrieval",
                    ),
                    function_response=None,
                    inline_data=None,
                    text=None,
                ),
                Part(
                    video_metadata=None,
                    thought=None,
                    code_execution_result=None,
                    executable_code=None,
                    file_data=None,
                    function_call=None,
                    function_response=None,
                    inline_data=None,
                    text="""Okay, I can help with that. I need to find where the frontend code constructs the tool result node to send to the backend and add the `tool_name` field there.

First, I'll search the codebase to locate the relevant files and code sections.
""",
                ),
            ],
            role="model",
        ),
        Content(
            parts=[
                Part(
                    video_metadata=None,
                    thought=None,
                    code_execution_result=None,
                    executable_code=None,
                    file_data=None,
                    function_call=None,
                    function_response=FunctionResponse(
                        id="",
                        name="tool_called_in_previous_turn",
                        response={
                            "output": """The following code sections were retrieved:
Path: clients/sidecar/node-process/src/tools/tool-methods.ts
import { Connection } from "vscode-languageserver";
import { ToolsModel } from "@augment/sidecar-libs/src/tools/tools-model";
import { getLogger } from "../logging";
import {
  ToolCallRequest,
  ToolCallResponse,
  ToolCheckSafeRequest,
  ToolCheckSafeResponse,
  ToolCancelRunRequest,
  ToolDefinition,
  ChatMode as ProtoChatMode,
  ChangeChatModeRequest,
  ToolsStateResponse,
  GetToolStatusForSettingsPanelResponse,
  ToolDefinitionWithSettings,
  ToolIdentifier,
  GetToolStatusForSettingsPanelRequest,
  SetMcpServersRequest,
} from "$clients/sidecar/node-process/protos/tools_pb";
import { ToolDefinitionWithSettings as LibToolDefinitionWithSettings } from "@augment/sidecar-libs/src/tools/tool-types";
import { ChatHistoryItem } from "$clients/sidecar/node-process/protos/chat_pb";
...
      logger.info(`Calling ${request.name} tool`);
      const result = await toolsModel.callTool(
        request.requestId,
        request.toolUseId,
        request.name,
        (request.input?.toJson() ?? {}) as Record<string, unknown>,
        request.history.map((item: ChatHistoryItem): Exchange => {
          return {
            request_message: item.requestMessage,
            response_text: item.responseText,
            request_id: item.requestId,
            // request_nodes?: ChatRequestNode[],
            // response_nodes?: ChatResultNode[],
          };
        }),
        request.conversationId,
      );
      return new ToolCallResponse({
        text: result.text,
        isError: result.isError,
      }).toJson();
    },
  );
...
Path: clients/sidecar/node-process/src/tools/remote-info-source.ts
import {
  RemoteInfoSource,
  RemoteToolDefinition,
  RemoteToolResponseStatus,
  RunRemoteToolResult,
} from "@augment/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
  convertIntToToolSafety,
  RemoteToolId,
} from "@augment/sidecar-libs/src/tools/tool-types";
import { Connection } from "vscode-languageserver";
import { sendRequestWithTimeout } from "../connection-utils";
import {
  RetrieveRemoteToolsResponse,
  CallRemoteToolRequest,
  CallRemoteToolResponse,
  FilterToolsWithExtraInputResponse,
  FilterToolsWithExtraInputRequest,
  RetrieveRemoteToolsRequest,
} from "$clients/sidecar/node-process/protos/tools_pb";
import { getLogger } from "../logging";
...

  public async runRemoteTool(
    requestId: string,
    toolName: string,
    toolInputJson: string,
    toolId: RemoteToolId,
    _signal: AbortSignal,
  ): Promise<RunRemoteToolResult> {
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/tools/call-remote-tool",
      new CallRemoteToolRequest({
        name: toolName,
        // Stringify here to reduce the risk of integers being parsed as doubles.
        input: toolInputJson,
        toolId: toolId,
        requestId: requestId,
      }).toJson(),
    );
    if (!jsonResponse) {
      return {
        toolOutput: "",
        toolResultMessage: "Failed to run remote tool: no response from host",
        status: RemoteToolResponseStatus.ExecutionUnknownStatus,
      };
    }
...
Path: clients/sidecar/node-process/src/client-interfaces/api-client.ts
...
  AgentInterruptionData,
  RememberToolCallData,
  AgentTracingData,
  MemoriesFileOpenData,
  InitialOrientationData,
  GenericTracingData,
  StringStats as StringStatsProto,
  TimedStringStats,
} from "$clients/sidecar/node-process/protos/api-client_pb";
import {
  RemoteToolId,
  ToolDefinition,
} from "@augment/sidecar-libs/src/tools/tool-types";
import {
  ChatHistoryItem,
  ChatStreamRequest,
  ChatStreamResponse,
} from "$clients/sidecar/node-process/protos/chat_pb";
import { JsonValue } from "@bufbuild/protobuf";
import {
  ClassifyAndDistillDebugFlag,
  FlushMemoriesDebugFlag,
  InitialOrientationDebugFlag,
  RememberToolCallDebugFlag,
  StringStats,
  Timed,
} from "@augment/sidecar-libs/src/metrics/types";
...
Path: clients/intellij/src/main/kotlin/com/augmentcode/intellij/sidecar/tools/WriteProcessTool.kt
...

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val writeRequest = convertStruct(input) { WriteProcessInputSchema.newBuilder() }.build()

    val processHandler =
      ExecutionManager.getInstance(project).getRunningProcesses()
        .find { AugmentTerminalInfo.TERMINAL_INFO_KEY.get(it)?.terminalId == writeRequest.terminalId }
    if (processHandler == null) {
      return ToolCallResponse.newBuilder()
        .setText("Terminal ${writeRequest.terminalId} not found")
        .setIsError(true)
        .build()
    }
...
Path: clients/common/webviews/src/apps/chat/models/tools-webview-model.ts
...

    let wasCancelled = false;
    let nextRequestNodes: ChatRequestNode[] = [];
    try {
      // Call the tool
      const result = clipResultIfLarge(
        await this._extensionClient.callTool(
          requestId,
          toolUseId,
          toolName,
          toolInput,
          chatHistory,
          conversationId,
        ),
        ToolsWebviewModel.maxResultBytes,
      );

      // Update the tool use state based on the result
      let nextPhase = result.isError ? ToolUsePhase.error : ToolUsePhase.completed;
      if (this._activeToolCancelled) {
        nextPhase = ToolUsePhase.cancelled;
        this._activeToolCancelled = false;
        wasCancelled = true;
      }

      this._conversationModel.updateToolUseState(requestId, toolUseId, {
        phase: nextPhase,
        result: result,
      });
...
Path: clients/intellij/src/main/kotlin/com/augmentcode/intellij/sidecar/tools/ReadProcessTool.kt
...

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val killRequest = convertStruct(input) { ReadProcessInputSchema.newBuilder() }.build()

    val processHandler =
      ExecutionManager.getInstance(project).getRunningProcesses()
        .find { AugmentTerminalInfo.TERMINAL_INFO_KEY.get(it)?.terminalId == killRequest.terminalId }
    if (processHandler == null) {
      return ToolCallResponse.newBuilder()
        .setText("Terminal ${killRequest.terminalId} not found")
        .setIsError(true)
        .build()
    }
...
Path: clients/vscode/src/__mocks__/mock-api-server.ts
import { Blobs } from "@augment/sidecar-libs/src/api/types";
import {
    ChatMode,
    ChatRequestNode,
    ChatResultNode,
    Exchange,
    ReplacementText,
} from "@augment/sidecar-libs/src/chat/chat-types";
import {
    AgentCodebaseRetrievalResult,
    AgentRequestEvent,
    AgentSessionEvent,
    ChatResult,
    ToolUseRequestEvent,
} from "@augment/sidecar-libs/src/client-interfaces/api-client-types";
import { RunRemoteToolResult } from "@augment/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
    ExtraToolInput,
    RemoteToolId,
    ToolDefinition,
} from "@augment/sidecar-libs/src/tools/tool-types";
import { VCSChange } from "@augment/sidecar-libs/src/vcs/watcher/types";
import { BlobNameCalculator } from "@augment/sidecar-libs/src/workspace/blob-name-calculator";
...
Path: clients/vscode/src/client-interfaces/sidecar-api-client.ts
import { ChatMode, ChatRequestNode, Exchange } from "@augment/sidecar-libs/src/chat/chat-types";
import { IAPIClient } from "@augment/sidecar-libs/src/client-interfaces/api-client";
import {
    AgentCodebaseRetrievalResult,
    AgentRequestEvent,
    AgentSessionEvent,
    ChatResult,
    ToolUseRequestEvent,
} from "@augment/sidecar-libs/src/client-interfaces/api-client-types";
import { RemoteToolId, ToolDefinition } from "@augment/sidecar-libs/src/tools/tool-types";

import { APIServer } from "../augment-api";
import { WorkspaceManager } from "../workspace/workspace-manager";
...
Path: clients/sidecar/libs/src/tools/sidecar-tools/codebase-retrieval.ts
...

  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    const toolRequestId = createRequestId();
    try {
      const informationRequest = toolInput.information_request as string;
      const result = await getAPIClient().agentCodebaseRetrieval(
        toolRequestId,
        informationRequest,
        chatHistory,
        this._maxRetrievalSize,
        abortSignal,
      );
      const truncatedResult = truncate(
        result.formattedRetrieval,
        this._maxRetrievalSize,
      );
      return successToolResponse(truncatedResult, toolRequestId);
    } catch (e: unknown) {
      return errorToolResponse(
        `Failed to retrieve codebase information: ${e instanceof Error ? e.message : String(e)}`,
        toolRequestId,
      );
    }
  }
}
...
Path: clients/sidecar/node-process/protos/tools.proto
...

message ToolCallRequest {
  string requestId = 1;
  string toolUseId = 2;
  string name = 3;
  google.protobuf.Struct input = 4;
  repeated com.augmentcode.sidecar.rpc.chat.ChatHistoryItem history = 5;
  string conversationId = 6;
}

message ToolCallResponse {
  string text = 1;
  bool isError = 2;
}

message ToolCheckSafeRequest {
  string name = 1;
  google.protobuf.Struct input = 2;
}

message ToolCheckSafeResponse {
  bool isSafe = 1;
}

message ToolCancelRunRequest {
  string requestId = 1;
  string toolUseId = 2;
}

message RetrieveRemoteToolsRequest {
  repeated RemoteToolId supportedTools = 1;
}

message RetrieveRemoteToolsResponse {
  repeated RemoteToolDefinition tools = 1;
}
...
Path: clients/vscode/src/augment-api.ts
...
        return await this.callApi<RunRemoteToolResult>(
            requestId,
            config,
            "agents/run-remote-tool",
            {
                tool_name: toolName,
                tool_input_json: toolInputJson,
                tool_id: toolId,
                ...extraToolInputJson,
            },
            (json) => this.convertToRunRemoteToolResult(json as BackRunRemoteToolResult),
            config.chat.url,
            120000,
            undefined,
            signal
        );
    }

    private convertToRevokeToolAccessResult(
        resp: BackRevokeToolAccessResult
    ): RevokeToolAccessResult {
        return {
            status: resp.status,
        };
    }
...
Path: clients/intellij/src/main/java/com/augmentcode/api/ChatResultNode.java
package com.augmentcode.api;

import javax.annotation.Nullable;

public class ChatResultNode {
  public Integer id;
  public Integer type;
  public String content;
  @Nullable
  public ChatResultToolUse toolUse;
  @Nullable
  public ChatResultAgentMemory agentMemory;
}
...
Path: clients/intellij/src/main/kotlin/com/augmentcode/intellij/api/Converts.kt
...

fun convertListToChatRequestNodes(nodes: List<ChatRequestNodeProto>): List<ChatRequestNode> {
  return nodes.map {
    ChatRequestNode().apply {
      id = it.id
      type = it.type
      textNode =
        if (it.hasTextNode()) {
          ChatRequestText().apply { content = it.textNode.content }
        } else {
          null
        }
      toolResultNode =
        if (it.hasToolResultNode()) {
          ChatRequestToolResult().apply {
            toolUseId = it.toolResultNode.toolUseId
            content = it.toolResultNode.content
            isError = it.toolResultNode.isError
            requestId = it.toolResultNode.requestId
          }
        } else {
          null
        }
      image_node =
        if (it.hasImageNode()) {
          ChatRequestImage().apply {
            image_data = it.imageNode.imageData
            format = it.imageNode.format.number
          }
        } else {
          null
        }
...
Path: clients/vscode/src/webview-panels/preference-panel.ts
...

    private async sendResultToBackend(
        requestIds: Pair<string>,
        apiServer: APIServer,
        modelIdPair: Pair<string>,
        result: ChatPreferenceResult
    ): Promise<void> {
        let textFeedback = `${result.textFeedback}\\nMODEL_IDS_START_LABEL\\n${modelIdPair?.a}\\n${modelIdPair?.b}\\nMODEL_IDS_END_LABEL\\nIMPLICIT_EXTERNAL_SOURCES_START_LABEL\\n${
            this._externalSources?.map((source) => source.name).join(", ") ?? ""
        }\\nIMPLICIT_EXTERNAL_SOURCES_END_LABEL\\nUSER_SPECIFIED_EXTERNAL_SOURCES_START_LABEL\\n${
            this._userSpecifiedExternalSources?.join(", ") ?? ""
        }\\nUSER_SPECIFIED_EXTERNAL_SOURCES_END_LABEL\\n`;
        // Send result to the backend
        await retryWithBackoff<void>(async () => {
            try {
                /* eslint-disable @typescript-eslint/naming-convention */
...
Path: clients/sidecar/libs/src/chat/chat-types.ts
...

export interface ChatRequestToolResult {
  /* eslint-disable @typescript-eslint/naming-convention */
  tool_use_id: string;
  content: string;
  is_error: boolean;
  // If the tool is implemented by Augment services
  request_id?: string;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatResultNode {
  /* eslint-disable @typescript-eslint/naming-convention */
  id: number;
  type: ChatResultNodeType;
  content: string;
  tool_use?: ChatResultToolUse;
  agent_memory?: ChatResultAgentMemory;
  /* eslint-enable @typescript-eslint/naming-convention */
}
...
Path: research/tools/chat_replay/replay_utils.py
...

    for match in tool_matches:
        # Add text node for content before tool result
        if match.start() > last_end:
            text_content = request_message[last_end : match.start()].strip()
            if text_content:
                request_nodes.append(
                    ChatRequestNode(
                        id=len(request_nodes),
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(content=text_content),
                        tool_result_node=None,
                    )
                )

        # Add tool result node
        tool_use_id = match.group(1).strip()
        content = match.group(2).strip()
        is_error = match.group(3).strip().lower() == "true"

        request_nodes.append(
            ChatRequestNode(
                id=len(request_nodes),
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id=tool_use_id, content=content, is_error=is_error
                ),
            )
        )
        last_end = match.end()
...
Path: tools/bazel_runner/web/frontend/src/routes/schedule_runs.tsx
...

  const onRunFormFinish = (values: RunCommitScheduleForm) => {
    console.log("Success:", values);

    // store the last value of the targets to pre-fill it next time
    if (lastTargetsValue) {
      setLastTargetsValue(values.targets);
    }

    let targets = values.targets
      .split("\\n")
      .filter((t: string) => t.length > 0);

    const schedule: ScheduleRunData = {
      checkout: {
        owner: values.owner,
        repoName: values.repo,
        commitCheckout: {
          branch: values.branch,
          ref: values.ref,
        },
      },
      targets: targets,
      env: values.env,
      notification: values.notification,
    };
    console.log(`${JSON.stringify(schedule)}`);
    scheduleRun(schedule).then((runId) => {
      navigate(`/run/${runId}`);
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };
...

  const onRunFormFinish = (values: RunPullRequestScheduleForm) => {
    console.log("Success:", values);

    // store the last value of the targets to pre-fill it next time
    if (values.targets) {
      setLastTargetsValue(values.targets);
    }

    let targets = (values.targets || "")
      .split("\\n")
      .filter((t: string) => t.length > 0);

    const schedule: ScheduleRunData = {
      checkout: {
        owner: values.owner,
        repoName: values.repo,
        pullRequestCheckout: {
          pullRequestNumber: values.pullRequestNumber,
        },
      },
      targets: targets,
      env: values.env,
      notification: values.notification,
    };
    console.log(`${JSON.stringify(schedule)}`);
    scheduleRun(schedule).then((runId) => {
      navigate(`/run/${runId}`);
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };
...
Path: clients/sidecar/node-process/protos/chat.proto
...

message ChatRequestToolResult {
  string tool_use_id = 1;
  string content = 2;
  bool is_error = 3;
  optional string request_id = 4;
  optional string tool_name = 5;
}

// Matches the ChatResultNodeType from public_api.proto
enum ChatResultNodeType {
  RAW_RESPONSE = 0;
  SUGGESTED_QUESTIONS = 1;
  MAIN_TEXT_FINISHED = 2;
  WORKSPACE_FILE_CHUNKS = 3;
  RELEVANT_SOURCES = 4;
  TOOL_USE = 5;
}

message ChatResultNode {
  int32 id = 1;
  ChatResultNodeType type = 2;
  string content = 3;
  optional ChatResultToolUse toolUse = 4 [json_name = "tool_use"];
  optional ChatResultAgentMemory agentMemory = 5 [json_name = "agent_memory"];
}
...
Path: tools/bazel_runner/server/bazel_runner_rpc_server.py
...
                        and other_run_info.test_spec.test_execution.checkout.owner
                        == request.test_execution.checkout.owner
                        and other_run_info.requestor == request.requestor
                        and not other_run_info.state.is_final()
                    ):
                        logging.info(
                            "Canceling creation based on superseded run: %s",
                            other_run_id,
                        )
                        context.abort(
                            grpc.StatusCode.ALREADY_EXISTS,
                            "Superseded by run {}".format(other_run_id),
                        )

            run_id = ulid.ULID().to_uuid()
            logging.info("Creating run %s", run_id)
            self.persistence.create_run(
                run_id,
                request.test_execution,
                requestor=request.requestor,
                tags=request.tags,
                supersedes=request.supersedes,
                test_selection=(
                    request.test_selection
                    if request.HasField("test_selection")
                    else None
                ),
                notification=(
                    request.notification if request.HasField("notification") else None
                ),
            )
...
Path: clients/intellij/src/main/proto/intellij-chat.proto
...

// Matches the ChatResultNodeType from public_api.proto
enum ChatResultNodeType {
  RAW_RESPONSE = 0;
  SUGGESTED_QUESTIONS = 1;
  MAIN_TEXT_FINISHED = 2;
  WORKSPACE_FILE_CHUNKS = 3;
  RELEVANT_SOURCES = 4;
  TOOL_USE = 5;
}

message ChatResultNode {
  int32 id = 1;
  ChatResultNodeType type = 2;
  string content = 3;
  optional ChatResultToolUse toolUse = 4 [json_name = "tool_use"];
  optional ChatResultAgentMemory agentMemory = 5 [json_name = "agent_memory"];
}

message ChatResultAgentMemory {
  string content = 1;
}

message ChatResultToolUse {
  string toolUseId = 1 [json_name = "tool_use_id"];
  string toolName = 2 [json_name = "tool_name"];
  string inputJson = 3 [json_name = "input_json"];
}
...
Path: tools/bzl/node_sea/src/node_sea.ts
...

  try {
    fs.writeFileSync(seaConfigPath, JSON.stringify(seaConfigContent, null, 2));
    console.log(`SEA Config: Created.`);
  } catch (error) {
    console.error(`SEA Config Error: ${error.message}.`);
    return 1;
  }

  /// SEA Blob: Build using Node.js.

  console.log(
    `SEA Blob: \\${Nodejs Path} --experimental-sea-config \\${SEA Config}.`,
  );
  const result = spawnSync(
    nodePath,
    ["--experimental-sea-config", seaConfigPath],
    {
      stdio: "inherit",
    },
  );
  if (result.error) {
    console.error(`SEA Blob..."""
                        },
                    ),
                    inline_data=None,
                    text=None,
                )
            ],
            role="user",
        ),
    ],
    "config": GenerateContentConfig(
        http_options=None,
        system_instruction="""# Role
You are Augment Agent developed by Augment Code, an agentic coding AI assistant, based on the Claude 3.7 Sonnet model by Anthropic, with access to the developer\'s codebase through Augment\'s world-leading context engine and integrations.
You can read from and write to the codebase using the provided tools.

# Preliminary tasks
Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.
Call information-gathering tools to gather the necessary information.
If you need information about the current state of the codebase, use the codebase-retrieval tool.

# Planning
Once you have performed preliminary rounds of information-gathering, come up with a low-level, extremely detailed plan for the actions you want to take.
Provide a bulleted list of each file you think you need to change.
Be sure to be careful and exhaustive.
Feel free to think about in a chain of thought first.
If, in the course of planning, you realize you need more information, feel free to perform more information-gathering steps.
Once you have a plan, outline this plan to the user.

# Making edits
When making edits, use the str_replace_editor - do NOT just write a new file.
Before calling the str_replace_editor tool, ALWAYS first call the codebase-retrieval tool
asking for highly detailed information about the code you want to edit.
Ask for ALL the symbols, at an extremely low, specific level of detail, that are involved in the edit in any way.
Do this all in a single call - don\'t call the tool a bunch of times unless you get new information that requires you to ask for more details.
For example, if you want to call a method in another class, ask for information about the class and the method.
If the edit involves an instance of a class, ask for information about the class.
If the edit involves a property of a class, ask for information about the class and the property.
If several of the above apply, ask for all of them in a single call.
When in any doubt, include the symbol or object.
When making changes, be very conservative and respect the codebase.

# Following instructions
Focus on doing what the user asks you to do.
Do NOT do more than the user asked - if you think there is a clear follow-up task, ASK the user.
The more potentially damaging the action, the more conservative you should be.
For example, do NOT perform any of these actions without explicit permission from the user:
- Committing or pushing code
- Changing the status of a ticket
- Merging a branch
- Installing dependencies
- Deploying code

# Testing
You are very good at writing unit tests and making them work. If you write
code, suggest to the user to test the code by writing tests and running them.
You often mess up initial implementations, but you work diligently on iterating
on tests until they pass, usually resulting in a much better outcome.
Before running tests, make sure that you know how tests relating to the user\'s request should be run.

# Displaying code
When showing the user code from existing file, don\'t wrap it in normal markdown ```.
Instead, ALWAYS wrap code you want to show the user in `<augment_code_snippet>` and  `</augment_code_snippet>`  XML tags.
Provide both `path=` and `mode="EXCERPT"` attributes to the tag.
Use four backticks (````) instead of three.

Example:
<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
````python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name
    ...
````
</augment_code_snippet>

If you fail to wrap code in this way, it will not be visible to the user.
BE VERY BRIEF BY ONLY PROVIDING <10 LINES OF THE CODE. If you give correct XML structure, it will be parsed into a clickable code block, and the user can always click it to see the part in the full file.

# Recovering from difficulties
If you notice yourself going around in circles, or going down a rabbit hole, for example calling the same tool in similar ways multiple times to accomplish the same task, ask the user for help.

# Final
After executing all the steps in the plan, reason out loud whether there are any futher changes that need to be made.
If so, please repeat the planning process.
If you have made code edits, suggest writing or updating tests and executing those tests to make sure the changes are correct.



# Memories
Here are the memories from previous interactions between the AI assistant (you) and the user:
```
# Code Presentation
- Format code excerpts from the repository using the XML format to make them clickable.

# Code Tracing
- When tracing code, use search tools to find actual definitions and call sites rather than making assumptions about what \'would be\' there.
```
# Summary of most important instructions
- Search for information to carry out the user request
- Always make a detailed plan before taking any action
- Make sure you have all the information before making edits
- Focus on following user instructions and ask before carrying out any actions beyond the user\'s instructions
- Wrap code excerpts in `<augment_code_snippet>` XML tags according to provided example
- If you find yourself repeatedly calling tools without making progress, ask the user for help
""",
        temperature=0.0,
        top_p=None,
        top_k=None,
        candidate_count=None,
        max_output_tokens=8192,
        stop_sequences=None,
        response_logprobs=None,
        logprobs=None,
        presence_penalty=None,
        frequency_penalty=None,
        seed=None,
        response_mime_type=None,
        response_schema=None,
        routing_config=None,
        model_selection_config=None,
        safety_settings=[
            SafetySetting(
                method=None,
                category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=HarmBlockThreshold.BLOCK_NONE,
            ),
            SafetySetting(
                method=None,
                category=HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=HarmBlockThreshold.BLOCK_NONE,
            ),
            SafetySetting(
                method=None,
                category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=HarmBlockThreshold.BLOCK_NONE,
            ),
            SafetySetting(
                method=None,
                category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=HarmBlockThreshold.BLOCK_NONE,
            ),
            SafetySetting(
                method=None,
                category=HarmCategory.HARM_CATEGORY_UNSPECIFIED,
                threshold=HarmBlockThreshold.BLOCK_NONE,
            ),
        ],
        tools=[
            Tool(
                function_declarations=[
                    FunctionDeclaration(
                        response=None,
                        description="Save a file. Use this tool to create new files.  It cannot modify existing files.",
                        name="save-file",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "file_path": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The path of the file to save.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "file_content": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The content of the file to save.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "add_last_line_newline": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Whether to add a newline at the end of the file (default: true).",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.BOOLEAN,
                                ),
                            },
                            property_ordering=None,
                            required=["file_path", "file_content"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="""
Custom editing tool for viewing, creating and editing files
* `path` is a file path relative to the workspace root
* command `view` displays the result of applying `cat -n`.
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.


Notes for using the `str_replace` command:
* Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on
* The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers
* Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE
* The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file
* Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries
* The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.

Notes for using the `insert` command:
* Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on
* The `insert_line_1` parameter specifies the line number after which to insert the new string
* The `insert_line_1` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line_1: 0`
* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.

Notes for using the `view` command:
* Strongly prefer to use larger ranges of at least 500 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use view command to read the file before editing it.
""",
                        name="str-replace-editor",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "command": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The commands to run. Allowed options are: 'view', 'str_replace', 'insert'.",
                                    enum=["view", "str_replace", "insert"],
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "path": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "view_range": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                                    enum=None,
                                    format=None,
                                    items=Schema(
                                        example=None,
                                        pattern=None,
                                        default=None,
                                        max_length=None,
                                        min_length=None,
                                        min_properties=None,
                                        max_properties=None,
                                        any_of=None,
                                        description=None,
                                        enum=None,
                                        format=None,
                                        items=None,
                                        max_items=None,
                                        maximum=None,
                                        min_items=None,
                                        minimum=None,
                                        nullable=None,
                                        properties={},
                                        property_ordering=None,
                                        required=None,
                                        title=None,
                                        type=Type.INTEGER,
                                    ),
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.ARRAY,
                                ),
                                "insert_line_1": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                ),
                                "new_str_1": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "old_str_1": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Required parameter of `str_replace` command containing the string in `path` to replace.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "old_str_start_line_number_1": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                ),
                                "old_str_end_line_number_1": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                ),
                            },
                            property_ordering=None,
                            required=["command", "path"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="""Call this tool when user asks you:
- to remember something
- to create memory/memories

Use this tool only with information that can be useful in the long-term.
Do not use this tool for temporary information.
""",
                        name="remember",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "memory": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The concise (1 sentence) memory to remember.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                )
                            },
                            property_ordering=None,
                            required=["memory"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Open a URL in the default browser.",
                        name="open-browser",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "url": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The URL to open in the browser.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                )
                            },
                            property_ordering=None,
                            required=["url"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="""Use this tool to request information from the codebase.
It will return relevant snippets for the requested information.""",
                        name="codebase-retrieval",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "information_request": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="A description of the information you need.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                )
                            },
                            property_ordering=None,
                            required=["information_request"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="""Launch a new process.
If wait is specified, waits up to that many seconds for the process to complete.
If the process completes within wait seconds, returns its output.
If it doesn't complete within wait seconds, returns partial output and process ID.
If wait is not specified, returns immediately with just the process ID.
The process's stdin is always enabled, so you can use write_process to send input if needed.""",
                        name="launch-process",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "command": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The shell command to execute",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                                "wait": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Optional: number of seconds to wait for the command to complete.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.NUMBER,
                                ),
                                "cwd": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Working directory for the command. If not supplied, uses the current working directory.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                            },
                            property_ordering=None,
                            required=["command"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Kill a process by its process ID.",
                        name="kill-process",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "process_id": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Process ID to kill.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                )
                            },
                            property_ordering=None,
                            required=["process_id"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Read output from a running process.",
                        name="read-process",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "process_id": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Process ID to read from.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                )
                            },
                            property_ordering=None,
                            required=["process_id"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Write input to a process's stdin.",
                        name="write-process",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "process_id": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Process ID to write to.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                ),
                                "input_text": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Text to write to the process's stdin.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                ),
                            },
                            property_ordering=None,
                            required=["process_id", "input_text"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="List all known processes and their states.",
                        name="list-processes",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={},
                            property_ordering=None,
                            required=[],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Wait for a process to complete or timeout.",
                        name="wait-process",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "process_id": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Process ID to wait for.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.INTEGER,
                                ),
                                "wait": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Number of seconds to wait for the process to complete.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.NUMBER,
                                ),
                            },
                            property_ordering=None,
                            required=["process_id", "wait"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="""Search the web for information. Returns results in markdown format.
Each result includes the URL, title, and a snippet from the page if available.

This tool uses Google's Custom Search API to find relevant web pages.""",
                        name="web-search",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description="Input schema for the web search tool.",
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "query": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The search query to send.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title="Query",
                                    type=Type.STRING,
                                ),
                                "num_results": Schema(
                                    example=None,
                                    pattern=None,
                                    default=5,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="Number of results to return",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=10.0,
                                    min_items=None,
                                    minimum=1.0,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title="Num Results",
                                    type=Type.INTEGER,
                                ),
                            },
                            property_ordering=None,
                            required=["query"],
                            title="WebSearchInput",
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Execute a shell command. The OS is win32. The shell is 'powershell'.",
                        name="shell",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "command": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The shell command to execute.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                )
                            },
                            property_ordering=None,
                            required=["command"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                    FunctionDeclaration(
                        response=None,
                        description="Fetches data from a webpage and converts it into Markdown.",
                        name="web-fetch",
                        parameters=Schema(
                            example=None,
                            pattern=None,
                            default=None,
                            max_length=None,
                            min_length=None,
                            min_properties=None,
                            max_properties=None,
                            any_of=None,
                            description=None,
                            enum=None,
                            format=None,
                            items=None,
                            max_items=None,
                            maximum=None,
                            min_items=None,
                            minimum=None,
                            nullable=None,
                            properties={
                                "url": Schema(
                                    example=None,
                                    pattern=None,
                                    default=None,
                                    max_length=None,
                                    min_length=None,
                                    min_properties=None,
                                    max_properties=None,
                                    any_of=None,
                                    description="The URL to fetch.",
                                    enum=None,
                                    format=None,
                                    items=None,
                                    max_items=None,
                                    maximum=None,
                                    min_items=None,
                                    minimum=None,
                                    nullable=None,
                                    properties={},
                                    property_ordering=None,
                                    required=None,
                                    title=None,
                                    type=Type.STRING,
                                )
                            },
                            property_ordering=None,
                            required=["url"],
                            title=None,
                            type=Type.OBJECT,
                        ),
                    ),
                ],
                retrieval=None,
                google_search=None,
                google_search_retrieval=None,
                code_execution=None,
            )
        ],
        tool_config=None,
        labels=None,
        cached_content=None,
        response_modalities=None,
        media_resolution=None,
        speech_config=None,
        audio_timestamp=None,
        automatic_function_calling=None,
        thinking_config=None,
    ),
}
