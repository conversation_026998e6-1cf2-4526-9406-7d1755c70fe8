import concurrent.futures
import pickle
import re

import tqdm

from base.prompt_format.common import (
    ChatResultNodeType,
    Exchange,
    ResponseMessage,
    try_get_response_message_as_text,
)
from base.prompt_format_chat import (
    get_structured_chat_prompt_formatter_by_name,
)
from research.tools.chat_replay.replay_infra import get_input_and_documents
from research.tools.chat_replay.replay_utils import (
    AGENT_TOKEN_APPORTIONMENT,
    TOOL_DEFINITIONS,
    StructuredChatPromptOutput,
    append_to_prompt_output,
    fix_tool_calls,
    run_model,
    truncate_prompt_output,
)

REQUEST_IDS = """
57c67ae4-b37f-4493-8be8-9a50bd494523
2e0dd9b8-f960-4297-942b-59d48fd676cf
c07b2109-8dd8-4113-8b75-86fe37971c84
41e3be9b-9272-4a8a-b674-f406248b3ce6
5dd29356-ee58-4ada-bab8-316ef9f43d9b
7fbfcfc6-3a1f-4df6-ae2c-ddfe108a9b95
""".strip().splitlines()
# REQUEST_IDS = """
# 57c67ae4-b37f-4493-8be8-9a50bd494523
# """.strip().splitlines()
TRIAL_COUNT = 10

PROBING_PROMPT = """Please answer the following questions:

1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.
2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?

Your task is to PURELY answer the question above without taking any other action.

Your answer must end with a clear Yes-No for each question, in the following format:
<answer id=1>Yes</answer>
<answer id=2>No</answer>
""".strip()

STANDALONE_PROBING_PROMPT = """The previous message is a single turn in an AI agent's execution trajectory. Please answer the following questions:

1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.
2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?

Your task is to PURELY answer the question above without taking any other action.

Your answer must end with a clear Yes-No for each question, in the following format:
<answer id=1>Yes</answer>
<answer id=2>No</answer>
""".strip()

EMPTY_RESPONSE_REMEDY_PROMPT = "Continue."

LAZY_DELEGATION_REMEDY_PROMPT = """Perform the action yourself, instead of asking the user to perform it. Perform the exact action(s) you asked the user to perform."""

LACK_OF_ACTION_REMEDY_PROMPT = "Perform the action you said you would perform in the previous turn by calling the appropriate function."


def describe_response(response):
    text_length = 0
    tool_calls = []
    if isinstance(response, str):
        text_length = len(response)
    else:
        for node in response:
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                text_length += len(node.content)
            elif node.type == ChatResultNodeType.TOOL_USE:
                tool_calls.append(node.tool_use.name)
    return response, text_length, tool_calls


def get_probing_answers(response) -> tuple[bool, bool]:
    text = try_get_response_message_as_text(response)
    pattern = r"<answer id=(\d+)>(.*?)</answer>"
    matches = re.findall(pattern, text, re.DOTALL)
    answers = {
        int(answer_id): answer_value.strip() for answer_id, answer_value in matches
    }
    return answers.get(1, "No") == "Yes", answers.get(2, "No") == "Yes"


def run_single_trial(
    request_index, round_, trial, prompt_output, tool_definitions, dry_run=False
):
    # if round_ != 1:
    #     return request_index, round_, trial, 0, [], {}, [], prompt_output
    if dry_run:
        return request_index, round_, trial, 0, [], {}, [], prompt_output
    # prompt_output = fix_tool_calls(prompt_output)
    try:
        prompt_output = fix_tool_calls(prompt_output)
        response = run_model(
            prompt_output,
            tool_definitions=tool_definitions,
            base_model_version="gemini2.5-0506",
            client_type="google_genai",
            yield_final_parameters=True,
        )

        response, text_length, tool_calls = describe_response(response)
    except Exception:
        return request_index, round_, trial, None, [], {}, [], prompt_output
    try:
        pro_lazy_delegation, pro_lack_of_action = False, False
        flash_lazy_delegation, flash_lack_of_action = False, False
        pro_standalone_lazy_delegation, pro_standalone_lack_of_action = False, False
        flash_standalone_lazy_delegation, flash_standalone_lack_of_action = False, False
        probing_prompt_output = append_to_prompt_output(
            prompt_output,
            response[1:],
            PROBING_PROMPT,
        )
        pro_probing_response = run_model(
            probing_prompt_output,
            tool_definitions=TOOL_DEFINITIONS,
            base_model_version="gemini2.5-0506",
            client_type="google_genai",
            yield_final_parameters=True,
        )
        pro_lazy_delegation, pro_lack_of_action = get_probing_answers(
            pro_probing_response
        )
        flash_probing_response = run_model(
            probing_prompt_output,
            tool_definitions=TOOL_DEFINITIONS,
            base_model_version="gemini2.5-flash",
            client_type="google_genai",
            yield_final_parameters=True,
        )
        flash_lazy_delegation, flash_lack_of_action = get_probing_answers(
            flash_probing_response
        )
        standalone_probing_prompt_output = StructuredChatPromptOutput(
            system_prompt="",
            chat_history=prompt_output.chat_history[-1:],
            message=STANDALONE_PROBING_PROMPT,
            retrieved_chunks_in_prompt=[],
        )
        print(standalone_probing_prompt_output)
        pro_standalone_probing_response = run_model(
            standalone_probing_prompt_output,
            tool_definitions=TOOL_DEFINITIONS,
            base_model_version="gemini2.5-0506",
            client_type="google_genai",
            yield_final_parameters=True,
        )
        pro_standalone_lazy_delegation, pro_standalone_lack_of_action = (
            get_probing_answers(pro_standalone_probing_response)
        )
        flash_standalone_probing_response = run_model(
            standalone_probing_prompt_output,
            tool_definitions=TOOL_DEFINITIONS,
            base_model_version="gemini2.5-flash",
            client_type="google_genai",
            yield_final_parameters=True,
        )
        flash_standalone_lazy_delegation, flash_standalone_lack_of_action = (
            get_probing_answers(flash_standalone_probing_response)
        )
        probing_results = {
            "pro": {
                "lazy_delegation": pro_lazy_delegation,
                "lack_of_action": pro_lack_of_action,
            },
            "flash": {
                "lazy_delegation": flash_lazy_delegation,
                "lack_of_action": flash_lack_of_action,
            },
            "pro_standalone": {
                "lazy_delegation": pro_standalone_lazy_delegation,
                "lack_of_action": pro_standalone_lack_of_action,
            },
            "flash_standalone": {
                "lazy_delegation": flash_standalone_lazy_delegation,
                "lack_of_action": flash_standalone_lack_of_action,
            },
        }
        return (
            request_index,
            round_,
            trial,
            text_length,
            tool_calls,
            probing_results,
            response,
            prompt_output,
        )
    except Exception as e:
        print(f"Probing error: {e}")
        return (
            request_index,
            round_,
            trial,
            text_length,
            tool_calls,
            {},
            response,
            prompt_output,
        )


def convert_probing_results_to_string(probing_results) -> str:
    if not probing_results:
        return "N/A"
    pro_lazy_delegation = probing_results["pro"]["lazy_delegation"]
    pro_lack_of_action = probing_results["pro"]["lack_of_action"]
    flash_lazy_delegation = probing_results["flash"]["lazy_delegation"]
    flash_lack_of_action = probing_results["flash"]["lack_of_action"]
    pro_standalone_lazy_delegation = probing_results["pro_standalone"][
        "lazy_delegation"
    ]
    pro_standalone_lack_of_action = probing_results["pro_standalone"]["lack_of_action"]
    flash_standalone_lazy_delegation = probing_results["flash_standalone"][
        "lazy_delegation"
    ]
    flash_standalone_lack_of_action = probing_results["flash_standalone"][
        "lack_of_action"
    ]
    lazy_delegation_count = sum(
        [
            pro_lazy_delegation,
            flash_lazy_delegation,
            pro_standalone_lazy_delegation,
            flash_standalone_lazy_delegation,
        ]
    )
    lack_of_action_count = sum(
        [
            pro_lack_of_action,
            flash_lack_of_action,
            pro_standalone_lack_of_action,
            flash_standalone_lack_of_action,
        ]
    )
    return f"{lazy_delegation_count}, {lack_of_action_count}"


prompt_formatter = get_structured_chat_prompt_formatter_by_name(
    "agent-binks-claude-v3", AGENT_TOKEN_APPORTIONMENT
)
prompt_outputs = []
for request_id in REQUEST_IDS:
    chat_prompt_input, _ = get_input_and_documents(request_id)
    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
    last_round = len(prompt_output.chat_history)  # type: ignore
    request_prompt_outputs = []
    for round in range(last_round + 1):
        truncated_prompt_output, next_response = truncate_prompt_output(
            prompt_output, round
        )
        fixed_prompt_output = fix_tool_calls(truncated_prompt_output)
        request_prompt_outputs.append([fixed_prompt_output] * TRIAL_COUNT)
    prompt_outputs.append(request_prompt_outputs)


flat_prompt_outputs = []
# for request_prompt_outputs in prompt_outputs:
#     for round_prompt_outputs in request_prompt_outputs:
#         flat_prompt_outputs.extend(round_prompt_outputs)
for request_index, request_prompt_outputs in enumerate(prompt_outputs):
    for round_, round_prompt_outputs in enumerate(request_prompt_outputs):
        for trial, prompt_output in enumerate(round_prompt_outputs):
            flat_prompt_outputs.append((request_index, round_, trial, prompt_output))

parallelism = 100
with concurrent.futures.ThreadPoolExecutor(max_workers=parallelism) as executor:
    futures = [
        executor.submit(
            run_single_trial,
            request_index,
            round_,
            trial,
            prompt_output,
            TOOL_DEFINITIONS,
            False,
        )
        for request_index, round_, trial, prompt_output in flat_prompt_outputs
    ]
    flat_results = []
    for future in tqdm.tqdm(
        concurrent.futures.as_completed(futures),
        total=len(flat_prompt_outputs),
        desc="Processing prompts",
    ):
        flat_results.append(future.result())

results = [
    [
        [[None, None, {}, []] for _ in range(TRIAL_COUNT)]
        for _ in range(len(prompt_outputs[request_index]))
    ]
    for request_index in range(len(prompt_outputs))
]
for result in flat_results:
    (
        request_index,
        round_,
        trial,
        text_length,
        tool_calls,
        probing_results,
        response,
        prompt_output,
    ) = result
    results[request_index][round_][trial] = (
        text_length,
        tool_calls,
        probing_results,
        response,
        prompt_output,
    )

with open("/mnt/efs/augment/user/zhuoran/gemini/replay_batch_results2.txt", "w") as f:
    for request_index, request_results in enumerate(results):
        print(f"Request {request_index}")
        print(f"Request {request_index}", file=f)
        for round_index, round_results in enumerate(request_results):
            print(f"    Round {round_index}")
            print(f"    Round {round_index}", file=f)
            for trial_index, result in enumerate(round_results):
                print(
                    f"        Trial {trial_index}",
                    result[:-3],
                    convert_probing_results_to_string(result[-3]),
                )
                print(
                    f"        Trial {trial_index}",
                    result[:-3],
                    convert_probing_results_to_string(result[-3]),
                    file=f,
                )


with open("/mnt/efs/augment/user/zhuoran/gemini/replay_batch_results2.pkl", "wb") as f:
    pickle.dump(results, f)
