from typing import Dict

# data is retrieved from this dashboard:
# https://grafana.us-central1.prod.augmentcode.com/d/eelwln5bx301sa/total-token-counts?from=now-7d&to=now
# for the last 7 days
# in millions
INPUT_TOKENS = 2134410381 / 1e6
OUTPUT_TOKENS = 9375375933 / 1e6
CACHE_READ_TOKENS = 1175812141317 / 1e6
CACHE_WRITE_TOKENS = 178482963769 / 1e6

PRICING = {
    "claude-3-7-sonnet": {
        "input": 3,
        "output": 15,
        "cache_read": 0.3,
        "cache_write": 3.75,
    },
    "claude-3-5-haiku": {
        "input": 0.8,
        "output": 4,
        "cache_read": 0.08,
        "cache_write": 1,
    },
}


def calc_cost(model_rates: Dict[str, float]) -> float:
    model_to_tokens = {
        model: {
            "input": INPUT_TOKENS * rate,
            "output": OUTPUT_TOKENS * rate,
            "cache_read": CACHE_READ_TOKENS * rate,
            "cache_write": CACHE_WRITE_TOKENS if rate > 0 else 0,
        }
        for model, rate in model_rates.items()
    }
    model_to_cost = {
        model: sum(
            count * PRICING[model][token_type] for token_type, count in tokens.items()
        )
        for model, tokens in model_to_tokens.items()
    }
    return sum(model_to_cost.values())


def print_cost_by_rates():
    model_rates_set = [
        {"claude-3-7-sonnet": (10 - i) / 10, "claude-3-5-haiku": i / 10}
        for i in range(11)
    ]
    base_cost = calc_cost(model_rates_set[0])
    print(f"Base cost in $M: {base_cost / 1e6:.2f}")
    print(f"{'Sonnet':^10} {'Haiku':^10} {'Relative cost':^15}")
    print("-" * 35)
    for model_rates in model_rates_set:
        cost = calc_cost(model_rates)
        relative_cost = cost / base_cost
        print(
            f"{model_rates['claude-3-7-sonnet']:^10.1f} {model_rates['claude-3-5-haiku']:^10.1f} {relative_cost:^15.2f}"
        )


if __name__ == "__main__":
    print_cost_by_rates()
