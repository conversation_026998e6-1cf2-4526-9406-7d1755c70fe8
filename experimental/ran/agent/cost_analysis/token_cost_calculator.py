"""
Module for calculating token costs for different AI models.
"""

from enum import Enum
from typing import Dict, Tuple


class <PERSON><PERSON>ode<PERSON>(Enum):
    """Enum representing different Claude models."""

    CLAUDE_3_OPUS = "claude-3-opus"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_HAIKU = "claude-3-haiku"
    CLAUDE_3_5_SONNET = "claude-3-5-sonnet"
    CLAUDE_3_5_HAIKU = "claude-3-5-haiku"
    CLAUDE_3_7_SONNET = "claude-3-7-sonnet"


# Official Anthropic API pricing per million tokens in USD as of November 2024
# Format: (input_cost_per_million, output_cost_per_million, cache_read_cost_per_million, cache_write_cost_per_million)
# Source: https://docs.anthropic.com/en/docs/about-claude/models/all-models#model-pricing
CLAUDE_PRICING: Dict[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>[float, float, float, float]] = {
    ClaudeModel.CLAUDE_3_OPUS: (15.0, 75.0, 1.50, 18.75),
    ClaudeModel.CLAUDE_3_SONNET: (3.0, 15.0, 0.30, 3.75),
    <PERSON>Model.CLAUDE_3_HAIKU: (0.25, 1.25, 0.03, 0.30),
    ClaudeModel.CLAUDE_3_5_SONNET: (3.0, 15.0, 0.30, 3.75),
    <PERSON>Model.CLAUDE_3_5_HAIKU: (0.80, 4.0, 0.08, 1.0),
    ClaudeModel.CLAUDE_3_7_SONNET: (3.0, 15.0, 0.30, 3.75),
}


def calculate_claude_cost(
    prompt_tokens: int,
    completion_tokens: int,
    cache_read_tokens: int = 0,
    cache_write_tokens: int = 0,
    model: ClaudeModel = ClaudeModel.CLAUDE_3_5_SONNET,
) -> float:
    """
    Calculate the cost of using a Claude model based on prompt, completion, and cache tokens.

    Args:
        prompt_tokens: Number of tokens in the prompt (input to the model)
        completion_tokens: Number of tokens in the completion (output from the model)
        cache_read_tokens: Number of tokens read from cache (default: 0)
        cache_write_tokens: Number of tokens written to cache (default: 0)
        model: The Claude model to use (default: CLAUDE_3_5_SONNET)

    Returns:
        The cost in USD
    """
    if model not in CLAUDE_PRICING:
        raise ValueError(f"Unknown model: {model}")

    (
        input_cost_per_million,
        output_cost_per_million,
        cache_read_cost_per_million,
        cache_write_cost_per_million,
    ) = CLAUDE_PRICING[model]

    # Convert to millions and calculate cost
    prompt_cost = (prompt_tokens / 1_000_000) * input_cost_per_million
    completion_cost = (completion_tokens / 1_000_000) * output_cost_per_million
    cache_read_cost = (cache_read_tokens / 1_000_000) * cache_read_cost_per_million
    cache_write_cost = (cache_write_tokens / 1_000_000) * cache_write_cost_per_million

    return prompt_cost + completion_cost + cache_read_cost + cache_write_cost


def format_cost(cost: float) -> str:
    """
    Format the cost as a string with appropriate units.

    Args:
        cost: The cost in USD

    Returns:
        A formatted string representation of the cost
    """
    if cost >= 1.0:
        return f"${cost:.2f}"
    else:
        # Convert to cents for small amounts
        cents = cost * 100
        return f"{cents:.2f}¢"


if __name__ == "__main__":
    # Example usage
    test_input_tokens = 10000
    test_output_tokens = 2000
    test_cache_read_tokens = 5000
    test_cache_write_tokens = 3000

    print(f"Input tokens: {test_input_tokens:,}")
    print(f"Output tokens: {test_output_tokens:,}")
    print(f"Cache read tokens: {test_cache_read_tokens:,}")
    print(f"Cache write tokens: {test_cache_write_tokens:,}")
    print()

    print("Official Anthropic API Pricing (November 2024)")
    print("=" * 50)
    print()

    # Calculate costs without cache operations
    print("Costs without cache operations:")
    for model in ClaudeModel:
        cost = calculate_claude_cost(test_input_tokens, test_output_tokens, model=model)
        formatted_cost = format_cost(cost)
        print(f"{model.value}: {formatted_cost}")

    print()

    # Calculate costs with cache operations
    print("Costs with cache operations:")
    for model in ClaudeModel:
        cost = calculate_claude_cost(
            prompt_tokens=test_input_tokens,
            completion_tokens=test_output_tokens,
            cache_read_tokens=test_cache_read_tokens,
            cache_write_tokens=test_cache_write_tokens,
            model=model,
        )
        formatted_cost = format_cost(cost)
        print(f"{model.value}: {formatted_cost}")

    print()

    # Calculate cache savings
    print("Cache savings (compared to processing all tokens as input/output):")
    for model in ClaudeModel:
        # Cost if all tokens were processed normally
        full_cost = calculate_claude_cost(
            prompt_tokens=test_input_tokens
            + test_cache_read_tokens
            + test_cache_write_tokens,
            completion_tokens=test_output_tokens,
            model=model,
        )

        # Cost with cache operations
        cache_cost = calculate_claude_cost(
            prompt_tokens=test_input_tokens,
            completion_tokens=test_output_tokens,
            cache_read_tokens=test_cache_read_tokens,
            cache_write_tokens=test_cache_write_tokens,
            model=model,
        )

        savings = full_cost - cache_cost
        savings_percent = (savings / full_cost) * 100 if full_cost > 0 else 0

        formatted_savings = format_cost(savings)
        print(f"{model.value}: {formatted_savings} ({savings_percent:.1f}% savings)")

    print()
    print("Note: Cache reads are significantly cheaper than regular input tokens.")
    print("Note: Cache writes are slightly more expensive than regular input tokens.")
