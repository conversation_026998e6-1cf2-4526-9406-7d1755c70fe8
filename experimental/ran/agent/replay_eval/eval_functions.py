import json
from typing import Call<PERSON>, <PERSON><PERSON>
from base.third_party_clients.third_party_model_client import ToolDefinition
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.utils import (
    extract_text,
    extract_tool_calls,
    validate_tool_input_with_schema,
    count_tokens,
)


def contain_keywords(keywords: list[str]):
    def _contain_keywords(response: ModelResponse) -> Tuple[bool, str]:
        text = extract_text(response)
        for keyword in keywords:
            if keyword not in text:
                return False, f"Missing keyword: {keyword}"
        return True, ""

    return _contain_keywords


def not_contain_keywords(keywords: list[str]):
    def _not_contain_keywords(response: ModelResponse) -> Tuple[bool, str]:
        text = extract_text(response)
        for keyword in keywords:
            if keyword in text:
                return False, f"Found keyword: {keyword}"
        return True, ""

    return _not_contain_keywords


def contains_one_of_tool_calls(tool_names: list[str]):
    def _contains_one_of_tool_calls(response: ModelResponse) -> <PERSON>ple[bool, str]:
        tool_calls = extract_tool_calls(response)
        for tool_call in tool_calls:
            if tool_call.name in tool_names:
                return True, ""
        return False, f"Missing tool calls: {tool_names}"

    return _contains_one_of_tool_calls


def calls_tool_with_valid_input(tool_name: str):
    def _calls_tool_with_valid_input(
        response: ModelResponse,
        tool_definitions: list[ToolDefinition],
    ) -> Tuple[bool, str]:
        tool_calls = extract_tool_calls(response)
        if len(tool_calls) == 0:
            return False, "No tool calls found"
        if len(tool_calls) > 1:
            return False, "Too many tool calls found"
        tool_call = tool_calls[0]
        if tool_call.name != tool_name:
            return False, f"Expected tool call: {tool_name}, found: {tool_call.name}"
        tool_input = tool_call.input
        tool_definition = next(
            tool_definition
            for tool_definition in tool_definitions
            if tool_definition.name == tool_name
        )
        return validate_tool_input_with_schema(
            tool_input=tool_input, schema=json.loads(tool_definition.input_schema_json)
        )

    return _calls_tool_with_valid_input


def num_output_tokens_less_than(max_tokens: int):
    def _num_output_tokens_less_than(response: ModelResponse) -> Tuple[bool, str]:
        num_tokens = count_tokens(response)
        if num_tokens > max_tokens:
            return (
                False,
                f"Number of output tokens ({num_tokens}) is greater than {max_tokens}",
            )
        return True, ""

    return _num_output_tokens_less_than


def eval_chain(*eval_funcs: Callable[[ModelResponse], Tuple[bool, str]]):
    def _chain(response: ModelResponse) -> Tuple[bool, str]:
        for eval_func in eval_funcs:
            is_correct, message = eval_func(response)
            if not is_correct:
                return False, message
        return True, ""

    return _chain
