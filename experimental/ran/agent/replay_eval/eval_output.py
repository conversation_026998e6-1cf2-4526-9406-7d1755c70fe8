from dataclasses_json import DataClassJsonMixin  # type: ignore
from dataclasses import dataclass, field
from typing import Dict, Optional

from base.third_party_clients.third_party_model_client import (
    ToolDefinition,
    EndOfStream,
)
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelConfig
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.utils import (
    extract_system_prompt,
)


@dataclass
class Attempt(DataClassJsonMixin):
    response: ModelResponse | None
    is_correct: bool
    explanation: str = ""
    stats: dict[str, int | float] = field(default_factory=dict)
    end_of_stream: EndOfStream | None = None


@dataclass
class EvalResult(DataClassJsonMixin):
    sample: EvalSample
    attempts: list[Attempt]

    sample_tool_definitions: list[ToolDefinition] = field(default_factory=list)
    model_tool_definitions: list[ToolDefinition] = field(default_factory=list)

    num_correct_attempts: int = field(init=False)

    def __post_init__(self):
        self.num_correct_attempts = sum(
            1 for attempt in self.attempts if attempt.is_correct
        )


@dataclass
class EvalSummary(DataClassJsonMixin):
    model_config: ModelConfig
    dataset_path: str
    # sample unique name to eval result
    eval_results: dict[str, EvalResult]
    stats: dict[str, int | float] = field(default_factory=dict)


@dataclass
class ComparisonSummary(DataClassJsonMixin):
    ref_summary: EvalSummary
    new_model_summaries: dict[str, EvalSummary]
    ref_system_prompt: Optional[str] = None
    model_system_prompts: Dict[str, str] = field(default_factory=dict)
    ref_supervisor_message: Optional[str] = None
    model_supervisor_messages: Dict[str, str] = field(default_factory=dict)

    def __post_init__(self):
        """Extract system prompts and supervisor messages from model configs after initialization."""
        # Extract reference model system prompt
        self.ref_system_prompt = extract_system_prompt(self.ref_summary.model_config)

        # Extract reference model supervisor message
        self.ref_supervisor_message = self.ref_summary.model_config.supervisor_message

        # Extract system prompts and supervisor messages for all new models
        for model_name, summary in self.new_model_summaries.items():
            self.model_system_prompts[model_name] = extract_system_prompt(
                summary.model_config
            )
            # Handle None supervisor messages
            if summary.model_config.supervisor_message is not None:
                self.model_supervisor_messages[model_name] = (
                    summary.model_config.supervisor_message
                )
