#!/usr/bin/env python3

import argparse
import json
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.prompt_format.common import (
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
)
from experimental.ran.agent.replay_eval.launch_process_eval_functions import (
    extract_focus_lines_from_response,
    extract_focus_lines_with_summary_from_response,
    calculate_enhanced_metrics,
    expand_line_ranges_to_set,
)
from experimental.ran.agent.replay_eval.launch_process_sample_extractor import (
    extract_launch_process_samples_from_request_ids,
)
from experimental.ran.agent.replay_eval.model_config import ModelConfig
from experimental.ran.agent.replay_eval.utils import load_model_config
from experimental.ran.utils.pickle_cache import pickle_cache
from research.tools.chat_replay.replay_utils import (
    run_model_v2,
    parse_request_message_nodes,
    parse_response_message_nodes,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

CACHE_DIRECTORY = "/mnt/efs/augment/user/ran/launch_process_focus_eval_cache"
DETAILED_OUTPUT_DIRECTORY = (
    "/mnt/efs/augment/user/ran/launch_process_focus_eval_detailed_outputs"
)


def resolve_namespace_to_tenant(namespace: str) -> str:
    """Resolve a namespace to a specific tenant name.

    Args:
        namespace: The namespace string (e.g., "i0", "i1", or a specific tenant name)

    Returns:
        A specific tenant name that can be used with get_tenant()

    Raises:
        ValueError: If the namespace cannot be resolved to a valid tenant
    """
    # If it's already a specific tenant name, return it as-is
    if namespace in [
        "dogfood",
        "dogfood-shard",
        "aitutor-pareto",
        "aitutor-turing",
        "aitutor-mercor",
    ]:
        return namespace

    # Check if it's already a specific vanguard tenant
    if namespace.startswith(("i0-vanguard", "i1-vanguard")):
        return namespace

    # Map namespace to default tenant
    namespace_to_tenant_mapping = {
        "i0": "i0-vanguard0",  # Default to vanguard0 for i0 namespace
        "i1": "i1-vanguard0",  # Default to vanguard0 for i1 namespace
    }

    if namespace in namespace_to_tenant_mapping:
        resolved_tenant = namespace_to_tenant_mapping[namespace]
        logger.info(f"Resolved namespace '{namespace}' to tenant '{resolved_tenant}'")
        return resolved_tenant

    # If we can't resolve it, return as-is and let get_tenant() handle the error
    return namespace


def extract_text_chunks_from_line_ranges(
    text: str, line_ranges: List[Tuple[int, int]]
) -> List[str]:
    """Extract text chunks from the given line ranges.

    Args:
        text: The original text to extract from
        line_ranges: List of (start_line, end_line) tuples (1-indexed, inclusive)

    Returns:
        List of text chunks corresponding to each line range
    """
    if not line_ranges:
        return []

    lines = text.split("\n")
    chunks = []

    for start_line, end_line in line_ranges:
        # Ensure start_line <= end_line
        if start_line > end_line:
            start_line, end_line = end_line, start_line

        # Convert to 0-indexed and ensure bounds
        start_idx = max(0, start_line - 1)
        end_idx = min(len(lines), end_line)

        if start_idx < len(lines):
            chunk_lines = lines[start_idx:end_idx]
            chunk_text = "\n".join(chunk_lines)
            chunks.append(chunk_text)
        else:
            chunks.append("")  # Empty chunk if line range is out of bounds

    return chunks


@dataclass
class DetailedEvaluationResult:
    """Comprehensive evaluation result for a single sample."""

    # Sample identification
    request_id: str
    model_name: str
    evaluation_timestamp: str

    # Original input data
    original_tool_output: str
    numbered_tool_output: str
    context_history: str
    tool_input: dict

    # Model input
    analysis_prompt: str
    system_prompt: Optional[str]

    # Model response
    response_nodes: List[ChatResultNode]
    response_text: str
    extracted_focus_lines: List[Tuple[int, int]]

    # Evaluation results
    success: bool
    baseline_focus_lines: Optional[List[Tuple[int, int]]] = None
    metrics: Optional[Dict[str, float]] = None

    # Text chunks from focus lines
    extracted_focus_chunks: List[str] = field(default_factory=list)
    baseline_focus_chunks: List[str] = field(default_factory=list)

    # Additional metadata
    extracted_summary: Optional[str] = None
    attempt_number: int = 1
    error_message: Optional[str] = None


@dataclass
class DetailedEvaluationSummary:
    """Summary of all detailed evaluations for a model."""

    model_name: str
    evaluation_timestamp: str
    total_samples: int
    successful_evaluations: int
    average_overlap: float
    average_hit_rate: float
    baseline_compression_rate: float = 0.0
    evaluated_compression_rate: float = 0.0
    average_total_lines: float = 0.0

    # Map from request_id to detailed result
    detailed_results: Dict[str, DetailedEvaluationResult] = field(default_factory=dict)


# Request IDs from staging/vanguard environment conversations with LaunchProcess tool calls
STAGING_LAUNCH_PROCESS_REQUEST_IDS = [
    # i1 min 50 lines
    "bcdde099-6a66-4e69-a23d-5a391db6be5d",
    "202589e0-19d7-4a46-a597-33f8f0fc9f87",
    "1dc78328-f152-4b73-95fa-94d187b83fd6",
    "a6b0f0e2-f31a-4d83-8a0f-7776a9a219bb",
    "6832370a-4cb3-4acb-962c-cd7da4734e29",
    "e99d32b2-436b-4b0e-9231-b579dddf90ed",
    "23b36afe-8c5f-4470-807e-8f4c4d6f630c",
    "33eefc40-9c16-4069-b01b-6a5e9162b0d5",
    "2803d12d-5aaa-48b7-b38c-8ddd3366acaf",
    "df0aed12-5735-4ae5-b75d-efbf9ca47b2a",
    "3a4fe7d2-d71d-433f-94d1-14a14dc2128d",
    "11021e44-7628-457f-9728-c313201d8124",
    "76a92916-ad57-4ff9-9958-287245aae209",
    "6b2ef6d8-649b-4117-9d7d-43097937305b",
    "8eb11787-47a5-4f55-9e69-0c0907a90030",
    "56d57c1c-2238-4214-95ab-ac76644130a5",
    "3f63a8df-2336-4664-96b4-d1d3a2ba1570",
    "91bf31f5-b61d-41bf-99c2-b81b4029523e",
    "dd4b70e5-b763-4b37-b0f3-06b4321311fc",
    "25e0efc2-8615-4b55-9d9f-1675d835ea7d",
    "943e29d1-888a-4eb1-a001-4d15166af62a",
    "bdb2ba3c-be2c-46d4-a1ea-97218e182324",
    "6d90b1a1-3fe0-4f46-b85c-12847a5ef7b0",
    "1534518f-5b63-419e-a365-6ba76654440b",
    "17e77a1b-4c0e-416a-8fdc-135e5f5724c8",
    "50478d62-7397-4032-88c0-6c05d4f7ebd5",
    "c0f99c54-6c86-452c-bd5d-ad3f4af959c3",
    "a03c7d89-62b2-4d4f-a61b-5508336bd17e",
    "8dc7aa17-d56a-4f3f-ad5c-5add6409003d",
    "6ab0bc33-5b4a-4330-8cff-926a9cd677d1",
    "a48cd67c-13e5-4524-ab50-790092fc0809",
    "73b3a37b-f83c-4baa-ba33-03284a7ce6c8",
    "2fedcd6d-5afb-407f-98c8-e0838459e53e",
    "60798ea7-c9bb-4951-af5a-27446180203f",
    "c2db78c5-9fab-4af9-9cd5-a17a00eab217",
    "a1cac274-ba72-4b9f-a119-84091d71f988",
    "ad4e3986-2ad7-41bf-bd35-653f49e58c16",
    "11a92d8a-6bcc-4206-93fa-f5639a65dee6",
    "0e198729-7f2e-4a05-88b0-c5134051cdda",
    "0708ac8f-fbb0-43ca-b994-39d95827adab",
    "971b5b26-f55b-412b-9efb-81cfd62fb695",
    "98daf2ee-0da1-488b-aced-9a6cf26feb09",
    "7840047e-1313-4ae2-9999-460a6ada6ad0",
    "bbea5c8e-4d8b-4b4b-ac65-bef6758c64e7",
    "49ebd943-449c-4286-8e90-32eb23ce1107",
    "fb1b21d3-5c3d-428b-947e-3497b033557f",
    "35744ab3-3883-4c8b-b76b-0dfc8389efd3",
    "0abe31af-09df-4746-b433-8407ac42f1fb",
    "b2990e23-d842-451c-a78e-b6b1cfe6590b",
    "d444d6e2-685c-4519-b68d-51055e3f1d0f",
    "082b169e-307c-4063-b163-04f4d57606f9",
    "5cd534a5-02b0-4ce3-87b0-81a4064ff5ac",
    "997808e3-ad6e-4cc0-b248-95c45762d845",
    "9465a690-dec9-4148-9a01-887fb45c3544",
    "4c76f19a-3c8f-443d-a909-261399260dcf",
    "37247a99-8a48-41d5-b48d-107e1c29e6b2",
    "cdc9bfe4-d5c9-42dd-853b-90fb39612f0d",
    "51425937-fb26-4439-af12-3af606dc15e2",
    # i0 min 50 lines
    # "29871fca-8fea-4f4a-a26e-bb7afb3171cc",
    # "9c55659a-1b97-4b15-b1c8-f581c46a8ffe",
    # "08e94303-b91f-4a95-a12e-2e3498e58b5d",
    # "6bf5c718-dd40-443f-9e3b-e7c3dfdaec9e",
    # "b1132cdb-aa3c-4f0a-89be-780c5354463b",
    # "d82d39a6-0d63-4a3e-a96e-5d2046bf88ac",
    # "cbfa023f-bcc9-4c38-a97c-bb51720f7585",
    # "c61c48ee-37a6-4ce4-9d92-1b09e231fd80",
    # "09801daf-f1a8-43be-86e4-e5ba0004524a",
    # "23862333-ed11-47d5-adc5-84d77590beee",
    # "ae88b4bd-9e1b-421d-aebf-266c1de05faa",
    # "61ad55da-d4e7-4bb0-a778-03e90e2c33f9",
    # "4b9d0c9e-a117-4aa1-ba14-ee7e2c3690e5",
    # "5e9113fd-efee-440e-a739-3840737e59e6",
    # "5b421e74-817a-4d04-9868-bf1259a50588",
    # "8f890f01-9221-4f52-9b8f-f7c16b2a8138",
    # "dc8a8f95-9e44-48bf-ac3c-91356890d0b3",
    # "ec1fd7a1-e7aa-4738-a0df-8b489ef958f1",
    # "1fad6ca9-9528-4fe3-aae2-7be73ea6887f",
    # "37c4bd7d-cd97-4fb3-ae2c-7f1b12d9e6a1",
    # "3b7c5e60-9e40-4781-ac3d-24678793ca63",
    # "5fa3e4a9-29e3-4a57-b290-3a3d615b2c6a",
    # "accbcb43-8900-4b04-807e-12cbee3fe950",
    # "e4b403ca-5a84-43f7-84a5-72e8dc8688e4",
    # "a375219a-d61f-4a5f-a3fc-cc4c91cfbc1e",
    # "b14c6fd8-989b-4832-bd96-06cd55bf1ec9",
    # "bdcdf283-59b4-4bae-ba07-816285e49894",
    # "e6377e28-4b32-44e6-bd44-4251745d1be2",
    # "64a4966d-1741-40b1-8e87-65c948080ad8",
    # "a36e9631-9519-4e02-b416-69ee70cf7d60",
    # "49f84147-d4b2-4e33-b225-eefd28d95d16",
    # "6b56c249-c801-46f2-ad2d-683b13f64381",
    # "2ca23b0d-a611-4c58-bee7-955886d8623d",
    # "fa5d2646-66e1-40c5-b79e-8e390e95171e",
    # Staging min 50 lines
    # "2cf008c8-9409-45c9-ac36-5998ed1e62c9",
    # "e01320f5-23c2-4508-84a6-77972c41d9e3",
    # "bc4c5dae-ca89-4211-943c-392e62bee78d",
    # "b6783050-93e1-438a-8949-dec18993c3bc",
    # "317931eb-4249-490e-a148-20bd24f7d799",
    # "a0748960-4a8a-43c7-9c46-21d876196449",
    # "7224a8ad-05cc-4d9c-9ce1-8973fcc3ad55",
    # "824cedda-7f0e-4afc-bdfb-cff51e3bd42d",
    # "10c863ce-3657-4ec9-aa7f-fbee46aadd4e",
    # "6b405f18-96c9-4998-8c4f-1921fe4837b2",
    # "744a51a6-758e-4daf-a10d-87920f404b5f",
    # "ec804957-feff-4a79-9bfd-d1d9738ae4a6",
    # "deb9e562-e2b8-4178-8f3e-2decf66bfaf6",
    # "ac57ba6e-df3a-4378-838e-af5cfb1d75bb",
    # "6fbd8a2d-ad88-4448-b600-826470e5e726",
    # "1b064a2d-ca52-409c-8944-8ee61da242d4",
    # "1b082bae-8bc2-4c39-a78e-32deba831404",
    # "780bf60b-7f6c-4940-be79-fcb42077f0bb",
    # "6af01739-0389-41f6-a3bd-07149d228374",
    # "1bfaf9e3-29b0-4387-b212-233e04619810",
    # "528019be-26fc-432f-a54b-21051924fe6a",
    # Staging including shorter samples
    # "734e9cc8-8b42-4d86-ae78-5270f9f836ad",
    # "56e4dcbb-9276-426c-83fa-9fc7fc21adb3",
    # "c0f6a6cb-7677-4c06-8fd0-fa066c3fd92e",
    # "70125c61-4a13-4cd5-b326-b0dae6ef9f46",
    # "9f3626b7-961a-4fc3-94e4-8a989caa7d75",
    # "66a10060-2caf-479e-89cc-f6662a228f9a",
    # "9bc6e2ba-7cce-47b5-ae47-644b75a7bc0b",
    # "98e5b394-2356-4423-846b-9245b6194cfc",
    # "b58c6618-9a32-4084-9db2-7a559b502151",
    # "a1bc2387-5e65-48e8-9b6f-8704f3591e3f",
    # "bc4c5dae-ca89-4211-943c-392e62bee78d",
    # "b6783050-93e1-438a-8949-dec18993c3bc",
    # "317931eb-4249-490e-a148-20bd24f7d799",
    # "a0748960-4a8a-43c7-9c46-21d876196449",
    # "b6c156ce-48d8-432c-a7e0-0e26da90a50d",
    # "6bd48c13-b251-4b34-8e2c-072f1a55e5b8",
    # "7224a8ad-05cc-4d9c-9ce1-8973fcc3ad55",
    # "0e5e57a7-ddc7-4360-a848-0f903a8a02e6",
    # "c97acf7c-446e-4474-a906-9dbe5fa4a7df",
    # "2b207113-389c-441b-9174-c6b2f5f88b2e",
    # "9b5a2dd0-1696-4dd1-9b4c-6686dc893b69",
    # "3c1dcfac-7346-44f5-9f2d-31719618eadc",
    # "58e782e9-9569-4c0d-913a-2e7f039330cf",
    # "6d7c62a2-1b34-4ac6-990f-d8ee559e02d2",
    # "9e53eeb1-8861-4f0f-81dd-d285d5ae4761",
    # "f68db6ad-98e6-45c3-aa9b-21ab676a5a09",
    # "63a1c9a3-ced7-4a8a-b880-c4eea98b64cc",
    # "25868135-f53e-4819-8331-92dd6c94f17e",
    # "1f29c424-4ee2-4348-ba39-f2bb7054d05d",
    # "7f846842-e3c3-4f0b-b56f-688f02cafae4",
    # "8c80ed9d-860e-468b-9449-82c0d147356a",
    # "e2b93d9c-a47d-492e-8299-8439ae6a0893",
    # "5b2eaa46-0c49-4a2f-804c-ea1898283f8b",
    # "89f9d9d3-d88d-4207-8437-ac87226785d1",
    # "563699b6-ca0f-45fc-9914-64452096ce16",
    # "3f56c56a-6058-491d-affb-472987cda142",
    # "40fad87e-e993-4b00-8200-1b527e62bd98",
    # "ac605658-86c2-46f9-b3e9-4f671d11decf",
    # "c0866529-3b66-4571-86c8-db5ea38a5cd8",
    # "73f2366e-d50c-4401-bb65-0b3e3c8ed2bb",
    # "c557681d-7d49-481b-861d-0c2d9dfffa4d",
    # "8efc93c9-f356-4b34-b463-a8068691b01d",
    # "a07a7f44-2d5f-4d20-b593-d6086655684b",
]


def create_focus_analysis_message_with_tool_context(
    numbered_output: str, tool_input: dict, context_history: str = ""
) -> str:
    """Create a chat message with tool use request and result for analysis.

    Args:
        numbered_output: Line-numbered tool output
        tool_input: The original tool input parameters
        context_history: Optional minimal context from the original conversation

    Returns:
        String message for the analysis request
    """
    # Format the tool input in a readable way
    tool_input_str = ""
    if tool_input:
        import json

        try:
            tool_input_str = json.dumps(tool_input, indent=2)
        except (TypeError, ValueError):
            tool_input_str = str(tool_input)

    # Extract minimal context - just the last user-assistant exchange
    minimal_context = ""
    has_context = False
    if context_history:
        minimal_context = extract_minimal_context(context_history)
        if minimal_context:
            has_context = True
            minimal_context = f"CONTEXT (last exchange):\n{minimal_context}\n\n"

    # If we have context, don't duplicate the tool call info since it's likely in the assistant's message
    if has_context:
        user_message = f"""The assistant executed a terminal command and received the output below. Please analyze this terminal/command output and identify only the most critical and relevant parts.

{minimal_context}TERMINAL OUTPUT:
{numbered_output}

Use the focus_lines tool to indicate only the few most critical lines of terminal output that are essential for understanding the command result. Be highly selective and focus specifically on:
- Error messages, warnings, or failures
- Key results or success indicators
- Important status information or summaries
- Unexpected behavior or notable output

Ignore routine/verbose output, standard headers, or repetitive information. After calling the tool, provide a single paragraph summary explaining why these specific terminal output lines are the most important."""
    else:
        # No context available, so include the tool call details
        user_message = f"""The assistant executed a terminal command and received the output below. Please analyze this terminal/command output and identify only the most critical and relevant parts.

COMMAND EXECUTED:
Tool: launch-process
Input: {tool_input_str}

TERMINAL OUTPUT:
{numbered_output}

Use the focus_lines tool to indicate only the few most critical lines of terminal output that are essential for understanding the command result. Be highly selective and focus specifically on:
- Error messages, warnings, or failures
- Key results or success indicators
- Important status information or summaries
- Unexpected behavior or notable output

Ignore routine/verbose output, standard headers, or repetitive information. After calling the tool, provide a single paragraph summary explaining why these specific terminal output lines are the most important."""

    return user_message


def parse_conversation_history(context_history: str) -> List[Exchange]:
    """Parse conversation history string into Exchange objects.

    Args:
        context_history: String containing conversation history in format:
            "User: message1\nAssistant: response1\nUser: message2\nAssistant: response2..."

    Returns:
        List of Exchange objects representing the conversation history
    """
    if not context_history:
        return []

    exchanges = []
    lines = context_history.strip().split("\n")

    current_user_msg = ""
    current_assistant_msg = ""

    for line in lines:
        line = line.strip()
        if line.startswith("User: "):
            # If we have a previous exchange, save it
            if current_user_msg and current_assistant_msg:
                exchanges.append(
                    Exchange(
                        request_message=current_user_msg,
                        response_text=current_assistant_msg,
                    )
                )
            # Start new exchange
            current_user_msg = line[6:]  # Remove "User: " prefix
            current_assistant_msg = ""
        elif line.startswith("Assistant: "):
            current_assistant_msg = line[11:]  # Remove "Assistant: " prefix
        elif current_user_msg and not current_assistant_msg:
            # Continuation of user message
            current_user_msg += "\n" + line
        elif current_assistant_msg:
            # Continuation of assistant message
            current_assistant_msg += "\n" + line

    # Add the last exchange if we have both parts
    if current_user_msg and current_assistant_msg:
        exchanges.append(
            Exchange(
                request_message=current_user_msg,
                response_text=current_assistant_msg,
            )
        )

    return exchanges


def extract_minimal_context(context_history: str) -> str:
    """Extract minimal context from conversation history.

    Returns just the last user message and assistant response before the tool call.

    Args:
        context_history: Full conversation history

    Returns:
        Minimal context string with last exchange
    """
    if not context_history:
        return ""

    # Split into lines and find the last User: and Assistant: messages
    lines = context_history.strip().split("\n")

    # Find the last user message and assistant response
    last_user_msg = ""
    last_assistant_msg = ""

    # Work backwards to find the most recent exchange
    for line in reversed(lines):
        line = line.strip()
        if line.startswith("User: ") and not last_user_msg:
            last_user_msg = line[6:]  # Remove "User: " prefix
        elif line.startswith("Assistant: ") and not last_assistant_msg:
            last_assistant_msg = line[11:]  # Remove "Assistant: " prefix

        # Stop once we have both
        if last_user_msg and last_assistant_msg:
            break

    # Format the minimal context
    context_parts = []
    if last_user_msg:
        # Truncate very long messages
        if len(last_user_msg) > 200:
            last_user_msg = last_user_msg[:200] + "..."
        context_parts.append(f"User: {last_user_msg}")

    if last_assistant_msg:
        # Truncate very long messages
        if len(last_assistant_msg) > 200:
            last_assistant_msg = last_assistant_msg[:200] + "..."
        context_parts.append(f"Assistant: {last_assistant_msg}")

    return "\n".join(context_parts) if context_parts else ""


def create_focus_analysis_message(
    numbered_output: str, context_history: str = ""
) -> str:
    """Create a chat message asking the model to analyze LaunchProcess output.

    Args:
        numbered_output: Line-numbered tool output
        context_history: Optional context from the original conversation

    Returns:
        String message for the analysis request
    """
    context_section = (
        f"Context from original conversation:\n{context_history}\n\n"
        if context_history
        else ""
    )

    user_message = f"""Please analyze the following terminal command output and identify only the most critical and relevant parts. The output has been formatted with line numbers for easy reference.

{context_section}TERMINAL OUTPUT:
{numbered_output}

Use the focus_lines tool to indicate only the few most critical lines of terminal output that are essential for understanding the command result. Be highly selective and focus specifically on:
- Error messages, warnings, or failures
- Key results or success indicators
- Important status information or summaries
- Unexpected behavior or notable output

Ignore routine/verbose output, standard headers, or repetitive information. After calling the tool, provide a single paragraph summary explaining why these specific terminal output lines are the most important."""

    return user_message


def evaluate_model_on_tool_output_detailed(
    model_config: ModelConfig,
    request_id: str,
    numbered_output: str,
    context_history: str = "",
    tool_input: Optional[dict] = None,
    original_tool_output: str = "",
    max_attempts: int = 3,
) -> DetailedEvaluationResult:
    """Evaluate a model on LaunchProcess tool output analysis with detailed results.

    Args:
        model_config: Model configuration to use
        request_id: Request ID for caching purposes
        numbered_output: Line-numbered tool output
        context_history: Full conversation history
        tool_input: Original tool input parameters
        original_tool_output: Original tool output (without line numbers)
        max_attempts: Maximum number of attempts

    Returns:
        DetailedEvaluationResult with comprehensive evaluation data
    """
    evaluation_timestamp = datetime.now().isoformat()

    # Parse the full conversation history into Exchange objects
    chat_history = parse_conversation_history(context_history)

    # Create the current message asking for focus line analysis
    current_message = f"""The assistant executed a terminal command and received the output below. Please analyze this terminal/command output and identify only the most critical and relevant parts.

COMMAND EXECUTED:
Tool: launch-process
Input: {json.dumps(tool_input, indent=2) if tool_input else "N/A"}

TERMINAL OUTPUT:
{numbered_output}

Use the focus_lines tool to indicate only the few most critical lines of terminal output that are essential for understanding the command result. Be highly selective and focus specifically on:
- Error messages, warnings, or failures
- Key results or success indicators
- Important status information or summaries
- Unexpected behavior or notable output

Ignore routine/verbose output, standard headers, or repetitive information. After calling the tool, provide a single paragraph summary explaining why these specific terminal output lines are the most important."""

    # Add supervisor message if configured
    if model_config.supervisor_message:
        current_message += model_config.supervisor_message

    # Create StructuredChatPromptOutput with full chat history
    prompt_output = StructuredChatPromptOutput(
        system_prompt=model_config.system_prompt,
        chat_history=chat_history,
        message=current_message,
        retrieved_chunks_in_prompt=[],
        retrieval_as_tool=False,
    )

    # Get tool definitions
    tool_definitions = model_config.get_tool_definitions([])

    # Initialize variables for detailed result
    response_nodes = []
    response_text = ""
    focus_lines = []
    summary = None
    error_message = None
    success = False

    for attempt in range(max_attempts):
        try:
            logger.info(
                f"Attempt {attempt + 1}/{max_attempts} for {model_config.name} on {request_id}"
            )
            logger.info(
                f"Using model: {model_config.anthropic_model}, client_type: {model_config.client_type}"
            )

            # Make the model call using run_model_v2
            result = run_model_v2(
                prompt_output=prompt_output,
                base_model_version=model_config.anthropic_model,
                client_type=model_config.client_type,  # Use the model config's client type
                tool_definitions=tool_definitions,
                yield_final_parameters=True,
            )

            response_nodes = result.response_nodes

            # Extract focus lines and summary
            focus_lines_data = extract_focus_lines_with_summary_from_response(
                response_nodes
            )
            focus_lines = focus_lines_data["line_ranges"]
            summary = focus_lines_data["summary"]

            # Extract response text from RAW_RESPONSE nodes only
            response_text = ""
            for node in response_nodes:
                if (
                    hasattr(node, "content")
                    and hasattr(node, "type")
                    and node.type == ChatResultNodeType.RAW_RESPONSE
                ):
                    response_text += node.content

            if focus_lines:
                logger.info("Successfully extracted focus lines")
                success = True
                break
            else:
                logger.warning(f"No focus lines found in attempt {attempt + 1}")
                error_message = f"No focus lines found in attempt {attempt + 1}"

        except Exception as e:
            error_message = f"Error in attempt {attempt + 1}: {e}"
            logger.error(error_message)
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(
                f"Model config: name={model_config.name}, model={model_config.anthropic_model}, client_type={model_config.client_type}"
            )
            import traceback

            logger.error(f"Full traceback: {traceback.format_exc()}")
            continue

    if not success:
        logger.error(f"Failed to get focus lines after {max_attempts} attempts")

    # Extract text chunks from focus lines
    extracted_focus_chunks = []
    if focus_lines and (original_tool_output or numbered_output):
        # Use original tool output if available, otherwise use numbered output
        source_text = original_tool_output if original_tool_output else numbered_output
        extracted_focus_chunks = extract_text_chunks_from_line_ranges(
            source_text, focus_lines
        )

    # Create detailed evaluation result
    return DetailedEvaluationResult(
        request_id=request_id,
        model_name=model_config.name,
        evaluation_timestamp=evaluation_timestamp,
        original_tool_output=original_tool_output or numbered_output,
        numbered_tool_output=numbered_output,
        context_history=context_history,
        tool_input=tool_input or {},
        analysis_prompt=current_message,
        system_prompt=model_config.system_prompt,
        response_nodes=response_nodes,
        response_text=response_text,
        extracted_focus_lines=focus_lines,
        extracted_focus_chunks=extracted_focus_chunks,
        success=success,
        extracted_summary=summary if success else None,
        attempt_number=max_attempts,
        error_message=error_message,
    )


@pickle_cache(CACHE_DIRECTORY)
def evaluate_model_on_tool_output(
    model_config: ModelConfig,
    request_id: str,
    numbered_output: str,
    context_history: str = "",
    max_attempts: int = 3,
    tool_input: Optional[dict] = None,
) -> Tuple[List[Tuple[int, int]], str, bool]:
    """Backward compatibility wrapper for evaluate_model_on_tool_output_detailed.

    Args:
        model_config: Model configuration to use
        request_id: Request ID for caching purposes
        numbered_output: Line-numbered tool output
        context_history: Optional context from original conversation (deprecated)
        max_attempts: Maximum attempts per sample
        tool_input: Original tool input parameters

    Returns:
        Tuple of (focus_line_ranges, response_text, success)
    """
    detailed_result = evaluate_model_on_tool_output_detailed(
        model_config=model_config,
        request_id=request_id,
        numbered_output=numbered_output,
        context_history=context_history,
        tool_input=tool_input,
        original_tool_output="",
        max_attempts=max_attempts,
    )

    return (
        detailed_result.extracted_focus_lines,
        detailed_result.response_text,
        detailed_result.success,
    )


def _generate_baseline_focus_lines_impl(
    baseline_model_config_path: str,
    request_ids: List[str],
    max_attempts: int = 1,
    min_lines: int = 0,
    namespace: str = "i0-vanguard0",
) -> Dict[str, Tuple[List[Tuple[int, int]], str]]:
    """Implementation of baseline focus lines generation.

    Args:
        baseline_model_config_path: Path to baseline model config
        request_ids: List of request IDs to process
        max_attempts: Maximum attempts per sample
        min_lines: Minimum number of lines in tool output to include in evaluation
        namespace: Namespace or tenant name to use for data access

    Returns:
        Dictionary mapping request_id to (focus_lines, response_text)
    """
    logger.info("Generating baseline focus lines...")

    # Resolve namespace to specific tenant name
    tenant_name = resolve_namespace_to_tenant(namespace)

    # Extract tool call data
    tool_call_data_list = extract_launch_process_samples_from_request_ids(
        request_ids, tenant_name=tenant_name
    )

    if not tool_call_data_list:
        logger.error("No LaunchProcess tool call data found!")
        return {}

    # Filter by minimum lines if specified
    if min_lines > 0:
        original_count = len(tool_call_data_list)
        tool_call_data_list = [
            data
            for data in tool_call_data_list
            if len(data.tool_output.split("\n")) >= min_lines
        ]
        filtered_count = len(tool_call_data_list)
        logger.info(
            f"Filtered samples by minimum lines ({min_lines}): "
            f"{original_count} -> {filtered_count} samples"
        )

    # Load baseline model config
    baseline_model_config = load_model_config(baseline_model_config_path)

    baseline_results = {}
    for tool_call_data in tool_call_data_list:
        focus_lines, response_text, success = evaluate_model_on_tool_output(
            baseline_model_config,
            tool_call_data.request_id,
            tool_call_data.numbered_output,
            tool_call_data.context_history,
            max_attempts,
            tool_call_data.tool_input,
        )

        if success:
            baseline_results[tool_call_data.request_id] = (focus_lines, response_text)
            logger.info(f"Baseline for {tool_call_data.request_id}: success")
        else:
            logger.warning(
                f"Failed to generate baseline for {tool_call_data.request_id}"
            )

    logger.info(f"Generated baseline for {len(baseline_results)} samples")
    return baseline_results


@pickle_cache(CACHE_DIRECTORY)
def _cached_generate_baseline_focus_lines(
    baseline_model_config_path: str,
    request_ids: List[str],
    max_attempts: int = 1,
    min_lines: int = 0,
    namespace: str = "i0-vanguard0",
) -> Optional[Dict[str, Tuple[List[Tuple[int, int]], str]]]:
    """Cached wrapper for baseline focus lines generation.

    This wrapper only caches successful results (non-empty dictionaries).
    """
    result = _generate_baseline_focus_lines_impl(
        baseline_model_config_path, request_ids, max_attempts, min_lines, namespace
    )

    # Only return result for caching if it's non-empty (successful)
    if result:
        return result
    else:
        # Return None to prevent caching of empty results
        return None


def generate_baseline_focus_lines(
    baseline_model_config_path: str,
    request_ids: List[str],
    max_attempts: int = 1,
    force_regenerate: bool = False,
    min_lines: int = 0,
    namespace: str = "i0-vanguard0",
) -> Dict[str, Tuple[List[Tuple[int, int]], str]]:
    """Generate baseline focus lines using Sonnet 4.

    Args:
        baseline_model_config_path: Path to baseline model config
        request_ids: List of request IDs to process
        max_attempts: Maximum attempts per sample
        force_regenerate: If True, bypass cache and regenerate
        min_lines: Minimum number of lines in tool output to include in evaluation

    Returns:
        Dictionary mapping request_id to (focus_lines, response_text)
    """
    if force_regenerate:
        # Bypass cache and call implementation directly
        logger.info("Force regenerating baseline focus lines (bypassing cache)")
        return _generate_baseline_focus_lines_impl(
            baseline_model_config_path, request_ids, max_attempts, min_lines, namespace
        )

    # Try cached version first
    cached_result = _cached_generate_baseline_focus_lines(
        baseline_model_config_path, request_ids, max_attempts, min_lines, namespace
    )

    if cached_result is not None:
        return cached_result
    else:
        # Cache returned None (empty result), try again without cache
        logger.warning(
            "Cached baseline generation returned empty result, retrying without cache"
        )
        return _generate_baseline_focus_lines_impl(
            baseline_model_config_path, request_ids, max_attempts, min_lines, namespace
        )


def run_comparison_evaluation(
    baseline_results: Dict[str, Tuple[List[Tuple[int, int]], str]],
    model_config_paths: List[str],
    max_attempts: int = 3,
    namespace: str = "i0-vanguard0",
) -> Dict[str, Dict[str, Tuple[List[Tuple[int, int]], str, Dict[str, float]]]]:
    """Run comparison evaluation against baseline.

    Args:
        baseline_results: Baseline results from Sonnet 4
        model_config_paths: List of model config paths to evaluate
        max_attempts: Maximum attempts per sample
        namespace: Namespace or tenant name to use for data access

    Returns:
        Dictionary mapping model_name -> request_id -> (focus_lines, response_text, metrics)
    """
    logger.info("Running comparison evaluation...")

    # Resolve namespace to specific tenant name
    tenant_name = resolve_namespace_to_tenant(namespace)

    # Extract tool call data
    tool_call_data_list = extract_launch_process_samples_from_request_ids(
        STAGING_LAUNCH_PROCESS_REQUEST_IDS, tenant_name=tenant_name
    )

    # Filter to only samples with baseline
    tool_call_data_list = [
        data for data in tool_call_data_list if data.request_id in baseline_results
    ]

    logger.info(f"Evaluating {len(tool_call_data_list)} samples with baseline")

    results = {}

    for model_config_path in model_config_paths:
        logger.info(f"Evaluating model config: {model_config_path}")
        model_config = load_model_config(model_config_path)
        model_results = {}

        for tool_call_data in tool_call_data_list:
            baseline_focus_lines, _ = baseline_results[tool_call_data.request_id]

            focus_lines, response_text, success = evaluate_model_on_tool_output(
                model_config,
                tool_call_data.request_id,
                tool_call_data.numbered_output,
                tool_call_data.context_history,
                max_attempts,
                tool_call_data.tool_input,
            )

            if success:
                # Calculate enhanced metrics against baseline
                baseline_lines = expand_line_ranges_to_set(baseline_focus_lines)
                evaluated_lines = expand_line_ranges_to_set(focus_lines)

                # Get total lines from the original tool output
                total_lines = len(tool_call_data.tool_output.split("\n"))

                metrics = calculate_enhanced_metrics(
                    baseline_lines, evaluated_lines, total_lines
                )

                model_results[tool_call_data.request_id] = (
                    focus_lines,
                    response_text,
                    metrics,
                )
                logger.info(
                    f"{model_config.name} on {tool_call_data.request_id}: "
                    f"Overlap={metrics['overlap_percent']:.1f}%, Hit={metrics['hit_percent']:.1f}%, "
                    f"Baseline={metrics['baseline_lines_count']}/{metrics['total_lines']} "
                    f"({metrics['baseline_focus_percent']:.1f}%), "
                    f"Evaluated={metrics['evaluated_lines_count']}/{metrics['total_lines']} "
                    f"({metrics['evaluated_focus_percent']:.1f}%)"
                )
            else:
                logger.warning(
                    f"Failed evaluation for {model_config.name} on {tool_call_data.request_id}"
                )

        results[model_config.name] = model_results

    return results


def run_comparison_evaluation_detailed(
    baseline_results: Dict[str, Tuple[List[Tuple[int, int]], str]],
    model_config_paths: List[str],
    max_attempts: int = 3,
    min_lines: int = 0,
    namespace: str = "i0",
) -> Dict[str, DetailedEvaluationSummary]:
    """Run comparison evaluation against baseline with detailed results.

    Args:
        baseline_results: Baseline results from Sonnet 4
        model_config_paths: List of model config paths to evaluate
        max_attempts: Maximum attempts per sample
        min_lines: Minimum number of lines in tool output to include in evaluation
        namespace: Namespace or tenant name to use for data access

    Returns:
        Dictionary mapping model_name -> DetailedEvaluationSummary
    """
    logger.info("Running detailed comparison evaluation...")

    # Resolve namespace to specific tenant name
    tenant_name = resolve_namespace_to_tenant(namespace)

    # Extract tool call data
    tool_call_data_list = extract_launch_process_samples_from_request_ids(
        STAGING_LAUNCH_PROCESS_REQUEST_IDS, tenant_name=tenant_name
    )

    # Filter to only samples with baseline
    tool_call_data_list = [
        data for data in tool_call_data_list if data.request_id in baseline_results
    ]

    # Filter by minimum lines if specified
    if min_lines > 0:
        original_count = len(tool_call_data_list)
        tool_call_data_list = [
            data
            for data in tool_call_data_list
            if len(data.tool_output.split("\n")) >= min_lines
        ]
        filtered_count = len(tool_call_data_list)
        logger.info(
            f"Filtered evaluation samples by minimum lines ({min_lines}): "
            f"{original_count} -> {filtered_count} samples"
        )

    logger.info(f"Evaluating {len(tool_call_data_list)} samples with baseline")

    detailed_summaries = {}

    for model_config_path in model_config_paths:
        logger.info(f"Evaluating model config: {model_config_path}")
        model_config = load_model_config(model_config_path)

        evaluation_timestamp = datetime.now().isoformat()
        detailed_results = {}
        successful_evaluations = 0
        total_overlap = 0.0
        total_hit = 0.0
        total_baseline_lines_focused = 0
        total_evaluated_lines_focused = 0
        total_lines_provided = 0

        for tool_call_data in tool_call_data_list:
            baseline_focus_lines, _ = baseline_results[tool_call_data.request_id]

            detailed_result = evaluate_model_on_tool_output_detailed(
                model_config=model_config,
                request_id=tool_call_data.request_id,
                numbered_output=tool_call_data.numbered_output,
                context_history=tool_call_data.context_history,
                tool_input=tool_call_data.tool_input,
                original_tool_output=tool_call_data.tool_output,
                max_attempts=max_attempts,
            )

            if detailed_result.success:
                # Calculate enhanced metrics against baseline
                baseline_lines = expand_line_ranges_to_set(baseline_focus_lines)
                evaluated_lines = expand_line_ranges_to_set(
                    detailed_result.extracted_focus_lines
                )

                # Get total lines from the original tool output
                total_lines = len(detailed_result.original_tool_output.split("\n"))

                metrics = calculate_enhanced_metrics(
                    baseline_lines, evaluated_lines, total_lines
                )

                # Update detailed result with baseline and metrics
                detailed_result.baseline_focus_lines = baseline_focus_lines
                detailed_result.metrics = metrics

                # Extract baseline focus chunks
                if baseline_focus_lines:
                    detailed_result.baseline_focus_chunks = (
                        extract_text_chunks_from_line_ranges(
                            detailed_result.original_tool_output, baseline_focus_lines
                        )
                    )

                successful_evaluations += 1
                total_overlap += metrics["overlap_percent"]
                total_hit += metrics["hit_percent"]
                total_baseline_lines_focused += metrics["baseline_lines_count"]
                total_evaluated_lines_focused += metrics["evaluated_lines_count"]
                total_lines_provided += metrics["total_lines"]

                logger.info(
                    f"{model_config.name} on {tool_call_data.request_id}: "
                    f"Overlap={metrics['overlap_percent']:.1f}%, Hit={metrics['hit_percent']:.1f}%, "
                    f"Baseline={metrics['baseline_lines_count']}/{metrics['total_lines']} "
                    f"({metrics['baseline_focus_percent']:.1f}%), "
                    f"Evaluated={metrics['evaluated_lines_count']}/{metrics['total_lines']} "
                    f"({metrics['evaluated_focus_percent']:.1f}%)"
                )
            else:
                logger.warning(
                    f"Failed evaluation for {model_config.name} on {tool_call_data.request_id}"
                )

            detailed_results[tool_call_data.request_id] = detailed_result

        # Calculate averages and compression rates
        avg_overlap = (
            total_overlap / successful_evaluations
            if successful_evaluations > 0
            else 0.0
        )
        avg_hit = (
            total_hit / successful_evaluations if successful_evaluations > 0 else 0.0
        )
        baseline_compression_rate = (
            (total_baseline_lines_focused / total_lines_provided) * 100
            if total_lines_provided > 0
            else 0.0
        )
        evaluated_compression_rate = (
            (total_evaluated_lines_focused / total_lines_provided) * 100
            if total_lines_provided > 0
            else 0.0
        )
        avg_total_lines = (
            total_lines_provided / successful_evaluations
            if successful_evaluations > 0
            else 0.0
        )

        # Create detailed summary
        summary = DetailedEvaluationSummary(
            model_name=model_config.name,
            evaluation_timestamp=evaluation_timestamp,
            total_samples=len(tool_call_data_list),
            successful_evaluations=successful_evaluations,
            average_overlap=avg_overlap,
            average_hit_rate=avg_hit,
            baseline_compression_rate=baseline_compression_rate,
            evaluated_compression_rate=evaluated_compression_rate,
            average_total_lines=avg_total_lines,
            detailed_results=detailed_results,
        )

        detailed_summaries[model_config.name] = summary

    return detailed_summaries


def print_detailed_evaluation_summary(
    baseline_results: Dict[str, Tuple[List[Tuple[int, int]], str]],
    detailed_summaries: Dict[str, DetailedEvaluationSummary],
):
    """Print a detailed summary of the evaluation results including summaries."""
    print("\n===== LaunchProcess Focus Lines Evaluation Results =====")
    print(f"Baseline samples: {len(baseline_results)}")

    for model_name, summary in detailed_summaries.items():
        print(f"\nModel: {model_name}")
        print(f"Successful evaluations: {summary.successful_evaluations}")

        if summary.successful_evaluations > 0:
            print(f"Average Overlap: {summary.average_overlap:.2f}%")
            print(f"Average Hit Rate: {summary.average_hit_rate:.2f}%")
            print(
                f"Baseline Compression Rate: {summary.baseline_compression_rate:.2f}%"
            )
            print(
                f"Evaluated Compression Rate: {summary.evaluated_compression_rate:.2f}%"
            )

            # Print per-sample results with summaries
            print("Per-sample results:")
            for request_id, detailed_result in summary.detailed_results.items():
                if detailed_result.success and detailed_result.metrics:
                    metrics = detailed_result.metrics
                    baseline_count = metrics.get("baseline_lines_count", 0)
                    evaluated_count = metrics.get("evaluated_lines_count", 0)
                    total_lines = metrics.get("total_lines", 0)
                    baseline_focus_pct = metrics.get("baseline_focus_percent", 0)
                    evaluated_focus_pct = metrics.get("evaluated_focus_percent", 0)

                    print(
                        f"  {request_id}: "
                        f"Overlap={metrics['overlap_percent']:.1f}%, "
                        f"Hit={metrics['hit_percent']:.1f}%, "
                        f"Baseline={baseline_count}/{total_lines} ({baseline_focus_pct:.1f}%), "
                        f"Evaluated={evaluated_count}/{total_lines} ({evaluated_focus_pct:.1f}%)"
                    )

                    # Add summary on a new line with line break
                    if detailed_result.extracted_summary:
                        print(f"    Summary: {detailed_result.extracted_summary}")
                    else:
                        print("    Summary: No summary provided")

                    # Display the actual focused lines from the evaluated model
                    if (
                        detailed_result.extracted_focus_chunks
                        and detailed_result.extracted_focus_lines
                    ):
                        print("    --- Evaluated extracted lines ---")
                        for chunk, line_range in zip(
                            detailed_result.extracted_focus_chunks,
                            detailed_result.extracted_focus_lines,
                        ):
                            start_line, _ = line_range
                            # Split chunk into lines and display each with proper line breaks
                            chunk_lines = chunk.split("\n")
                            for j, line in enumerate(chunk_lines):
                                line_num = start_line + j
                                print(f"      {line_num:4d}: {line}")
                    else:
                        print(
                            "    --- Evaluated extracted lines ---: No focused lines available"
                        )

                    # Display the baseline focused lines for comparison
                    if (
                        detailed_result.baseline_focus_chunks
                        and detailed_result.baseline_focus_lines
                    ):
                        print("    --- Baseline extracted lines ---")
                        for chunk, line_range in zip(
                            detailed_result.baseline_focus_chunks,
                            detailed_result.baseline_focus_lines,
                        ):
                            start_line, _ = line_range
                            # Split chunk into lines and display each with proper line breaks
                            chunk_lines = chunk.split("\n")
                            for j, line in enumerate(chunk_lines):
                                line_num = start_line + j
                                print(f"      {line_num:4d}: {line}")
                    else:
                        print(
                            "    --- Baseline extracted lines ---: No baseline focused lines available"
                        )

                    # Add full baseline response text containing chain-of-thought analysis
                    if detailed_result.response_text:
                        print("    Full Model Response:")
                        # Indent each line of the response text for better readability
                        for line in detailed_result.response_text.split("\n"):
                            print(f"      {line}")
                        print()  # Add blank line after response
                    else:
                        print("    Full Model Response: No response text available\n")

                    # Add sample divider
                    print("\n***************\n")


def print_evaluation_summary(
    baseline_results: Dict[str, Tuple[List[Tuple[int, int]], str]],
    comparison_results: Dict[
        str, Dict[str, Tuple[List[Tuple[int, int]], str, Dict[str, float]]]
    ],
):
    """Print a summary of the evaluation results (legacy function for backward compatibility)."""
    print("\n===== LaunchProcess Focus Lines Evaluation Results =====")
    print(f"Baseline samples: {len(baseline_results)}")

    for model_name, model_results in comparison_results.items():
        print(f"\nModel: {model_name}")
        print(f"Successful evaluations: {len(model_results)}")

        if model_results:
            # Calculate average metrics
            total_overlap = sum(
                metrics["overlap_percent"] for _, _, metrics in model_results.values()
            )
            total_hit = sum(
                metrics["hit_percent"] for _, _, metrics in model_results.values()
            )
            count = len(model_results)

            avg_overlap = total_overlap / count
            avg_hit = total_hit / count

            print(
                f"Average Overlap: {avg_overlap:.2f}% ({total_overlap:.2f} / {count} samples)"
            )
            print(
                f"Average Hit Rate: {avg_hit:.2f}% ({total_hit:.2f} / {count} samples)"
            )

            # Calculate compression rate metrics
            total_baseline_lines_focused = sum(
                metrics.get("baseline_lines_count", 0)
                for _, _, metrics in model_results.values()
            )
            total_evaluated_lines_focused = sum(
                metrics.get("evaluated_lines_count", 0)
                for _, _, metrics in model_results.values()
            )
            total_lines_provided = sum(
                metrics.get("total_lines", 0)
                for _, _, metrics in model_results.values()
            )

            baseline_compression_rate = (
                (total_baseline_lines_focused / total_lines_provided) * 100
                if total_lines_provided > 0
                else 0.0
            )
            evaluated_compression_rate = (
                (total_evaluated_lines_focused / total_lines_provided) * 100
                if total_lines_provided > 0
                else 0.0
            )

            print(
                f"Baseline Compression Rate: {baseline_compression_rate:.2f}% ({total_baseline_lines_focused} / {total_lines_provided} lines)"
            )
            print(
                f"Evaluated Compression Rate: {evaluated_compression_rate:.2f}% ({total_evaluated_lines_focused} / {total_lines_provided} lines)"
            )

            # Print per-sample results
            print("Per-sample results:")
            for request_id, (_, _, metrics) in model_results.items():
                baseline_count = metrics.get("baseline_lines_count", 0)
                evaluated_count = metrics.get("evaluated_lines_count", 0)
                total_lines = metrics.get("total_lines", 0)
                baseline_focus_pct = metrics.get("baseline_focus_percent", 0)
                evaluated_focus_pct = metrics.get("evaluated_focus_percent", 0)

                print(
                    f"  {request_id}: "
                    f"Overlap={metrics['overlap_percent']:.1f}%, "
                    f"Hit={metrics['hit_percent']:.1f}%, "
                    f"Baseline={baseline_count}/{total_lines} ({baseline_focus_pct:.1f}%), "
                    f"Evaluated={evaluated_count}/{total_lines} ({evaluated_focus_pct:.1f}%)"
                )


def save_detailed_evaluation_result(
    detailed_result: DetailedEvaluationResult, output_dir: Path
) -> Path:
    """Save a detailed evaluation result to a JSON file.

    Args:
        detailed_result: The detailed evaluation result to save
        output_dir: Directory to save the file in

    Returns:
        Path to the saved file
    """
    # Create filename with model name and request ID
    safe_model_name = detailed_result.model_name.replace("/", "_").replace(":", "_")
    filename = f"{safe_model_name}_{detailed_result.request_id}.json"
    file_path = output_dir / filename

    # Convert to dictionary for JSON serialization
    result_dict = {
        "request_id": detailed_result.request_id,
        "model_name": detailed_result.model_name,
        "evaluation_timestamp": detailed_result.evaluation_timestamp,
        "original_tool_output": detailed_result.original_tool_output,
        "numbered_tool_output": detailed_result.numbered_tool_output,
        "context_history": detailed_result.context_history,
        "tool_input": detailed_result.tool_input,
        "analysis_prompt": detailed_result.analysis_prompt,
        "system_prompt": detailed_result.system_prompt,
        "response_text": detailed_result.response_text,
        "extracted_focus_lines": detailed_result.extracted_focus_lines,
        "extracted_focus_chunks": detailed_result.extracted_focus_chunks,
        "extracted_summary": detailed_result.extracted_summary,
        "success": detailed_result.success,
        "baseline_focus_lines": detailed_result.baseline_focus_lines,
        "baseline_focus_chunks": detailed_result.baseline_focus_chunks,
        "metrics": detailed_result.metrics,
        "attempt_number": detailed_result.attempt_number,
        "error_message": detailed_result.error_message,
        # Convert response_nodes to a serializable format
        "response_nodes": [
            {
                "type": str(node.type) if hasattr(node, "type") else "unknown",
                "content": getattr(node, "content", ""),
                "tool_use": {
                    "name": getattr(node.tool_use, "name", None)
                    if hasattr(node, "tool_use") and node.tool_use
                    else None,
                    "input": getattr(node.tool_use, "input", None)
                    if hasattr(node, "tool_use") and node.tool_use
                    else None,
                    "tool_use_id": getattr(node.tool_use, "tool_use_id", None)
                    if hasattr(node, "tool_use") and node.tool_use
                    else None,
                }
                if hasattr(node, "tool_use") and node.tool_use
                else None,
            }
            for node in detailed_result.response_nodes
        ],
    }

    # Save to JSON file
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(result_dict, f, indent=2, ensure_ascii=False)

    logger.info(f"Saved detailed evaluation result to {file_path}")
    return file_path


def save_detailed_evaluation_summary(
    summary: DetailedEvaluationSummary, output_dir: Path
) -> Path:
    """Save a detailed evaluation summary to a JSON file.

    Args:
        summary: The detailed evaluation summary to save
        output_dir: Directory to save the file in

    Returns:
        Path to the saved file
    """
    # Create filename with model name and timestamp
    safe_model_name = summary.model_name.replace("/", "_").replace(":", "_")
    timestamp = summary.evaluation_timestamp.replace(":", "_").replace(".", "_")
    filename = f"{safe_model_name}_summary_{timestamp}.json"
    file_path = output_dir / filename

    # Convert to dictionary for JSON serialization
    summary_dict = {
        "model_name": summary.model_name,
        "evaluation_timestamp": summary.evaluation_timestamp,
        "total_samples": summary.total_samples,
        "successful_evaluations": summary.successful_evaluations,
        "average_overlap": summary.average_overlap,
        "average_hit_rate": summary.average_hit_rate,
        "baseline_compression_rate": summary.baseline_compression_rate,
        "evaluated_compression_rate": summary.evaluated_compression_rate,
        "average_total_lines": summary.average_total_lines,
        "detailed_results_files": [
            f"{safe_model_name}_{request_id}.json"
            for request_id in summary.detailed_results.keys()
        ],
    }

    # Save to JSON file
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(summary_dict, f, indent=2, ensure_ascii=False)

    logger.info(f"Saved detailed evaluation summary to {file_path}")
    return file_path


def main():
    parser = argparse.ArgumentParser(
        description="Run LaunchProcess focus lines evaluation"
    )
    parser.add_argument(
        "--baseline-model-config",
        type=str,
        default="launch_process/focus_lines_baseline.py",
        help="Path to baseline model config (default: Sonnet 4 baseline)",
    )
    parser.add_argument(
        "--model-configs",
        type=str,
        nargs="+",
        default=[
            "experimental/ran/agent/replay_eval/model_configs/launch_process/focus_lines_gemini_flash.py"
        ],
        help="List of model config paths to evaluate",
    )
    parser.add_argument(
        "--max-attempts",
        type=int,
        default=3,
        help="Maximum attempts per sample (default: 3)",
    )
    parser.add_argument(
        "--baseline-attempts",
        type=int,
        default=1,
        help="Maximum attempts for baseline generation (default: 1)",
    )
    parser.add_argument(
        "--regenerate-baseline",
        action="store_true",
        help="Force regeneration of baseline (ignore cache)",
    )
    parser.add_argument(
        "--min-lines",
        type=int,
        default=20,
        help="Minimum number of lines in launch-process output to include (default: 20)",
    )
    parser.add_argument(
        "--namespace",
        type=str,
        default="i0",
        help="Kubernetes namespace to query (default: i0)",
    )

    args = parser.parse_args()

    # Ensure cache directory exists
    Path(CACHE_DIRECTORY).mkdir(parents=True, exist_ok=True)

    # Ensure detailed output directory exists if needed
    Path(DETAILED_OUTPUT_DIRECTORY).mkdir(parents=True, exist_ok=True)

    # Generate or load baseline focus lines
    baseline_results = generate_baseline_focus_lines(
        args.baseline_model_config,
        STAGING_LAUNCH_PROCESS_REQUEST_IDS,
        args.baseline_attempts,
        force_regenerate=args.regenerate_baseline,
        min_lines=args.min_lines,
        namespace=args.namespace,
    )

    if not baseline_results:
        logger.error("No baseline focus lines available!")
        return

    logger.info(f"Using baseline with {len(baseline_results)} samples")

    # Run detailed comparison evaluation
    detailed_summaries = run_comparison_evaluation_detailed(
        baseline_results,
        args.model_configs,
        args.max_attempts,
        args.min_lines,
        args.namespace,
    )

    # Save detailed results to files
    output_dir = Path(DETAILED_OUTPUT_DIRECTORY)
    for model_name, summary in detailed_summaries.items():
        logger.info(f"Saving detailed results for {model_name}...")

        # Save individual detailed results
        for request_id, detailed_result in summary.detailed_results.items():
            save_detailed_evaluation_result(detailed_result, output_dir)

        # Save summary
        save_detailed_evaluation_summary(summary, output_dir)

    # Convert detailed summaries to comparison results format for printing
    comparison_results = {}
    for model_name, summary in detailed_summaries.items():
        model_results = {}
        for request_id, detailed_result in summary.detailed_results.items():
            if detailed_result.success and detailed_result.metrics:
                model_results[request_id] = (
                    detailed_result.extracted_focus_lines,
                    detailed_result.response_text,
                    detailed_result.metrics,
                )
        comparison_results[model_name] = model_results

    logger.info(f"Detailed evaluation results saved to {DETAILED_OUTPUT_DIRECTORY}")

    # Print detailed summary with summaries
    print_detailed_evaluation_summary(baseline_results, detailed_summaries)


if __name__ == "__main__":
    main()
