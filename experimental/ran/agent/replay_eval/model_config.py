import copy
import dataclasses
import json
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Tuple
import jinja2


from dataclasses_json import (  # type: ignore
    DataClassJsonMixin,  # type: ignore
    config,
)

from base.prompt_format.common import (
    Chat<PERSON><PERSON>quest<PERSON><PERSON>,
    ChatRequestNodeType,
    ChatRequestText,
    ChatResultNode,
)
from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from base.prompt_format_chat.lib.string_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Val<PERSON>
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.prompt_format_chat.structured_binks_agent_prompt_formatter import (
    StructuredBinksAgentPromptFormatter,
)
from base.prompt_format_chat.prompt_formatter import StructuredChatPromptFormatter
from base.third_party_clients.third_party_model_client import ToolDefinition
from base.prompt_format_chat.lib.system_prompts import (
    AgentSystemPromptInfo,
    get_agent_system_prompt_formatter_v2,
)

DEFAULT_PROD_MODEL_NAME = "claude-sonnet-4-0-200k-v3-agent"

ModelResponse = list[ChatResultNode]


class SupervisorMode(Enum):
    """Mode for how supervisor messages are applied.

    MESSAGE: Add supervisor message to the beginning of all messages (current and history)
    USER_MESSAGE: Add supervisor message to the beginning of user messages only
    EVERY_K: Add supervisor message to every k-th message, starting from k messages ago
    LATEST_MESSAGE: Add supervisor message only to the latest message, not to any history messages
    PREFILL: Add supervisor message to the message prefill instead of to any messages
    """

    MESSAGE = "message"  # Add supervisor message to the beginning of all messages
    USER_MESSAGE = (
        "user_message"  # Add supervisor message to the beginning of user messages only
    )
    EVERY_K = "every_k"  # Add supervisor message every k messages
    LATEST_MESSAGE = (
        "latest_message"  # Add supervisor message only to the latest message
    )
    PREFILL = "prefill"  # Add supervisor message to the message prefill


@dataclass
class ModelConfig(DataClassJsonMixin):
    name: str
    anthropic_model: (
        str  # Model name/version for inference (kept for backward compatibility)
    )
    prompt_formatter: StructuredChatPromptFormatter | None = field(
        default=None, metadata=config(encoder=lambda x: None, exclude=lambda x: True)
    )
    eval_func: Callable[[ModelResponse], Tuple[bool, str]] | None = field(
        default=None, metadata=config(encoder=lambda x: None, exclude=lambda x: True)
    )
    add_system_prompt_to_prefill: bool = False
    additional_prefill: str | None = ""
    system_prompt: str | None = None
    # message to prepend to messages based on supervisor_mode
    supervisor_message: str | None = None
    # mode for how supervisor messages are applied
    supervisor_mode: SupervisorMode = SupervisorMode.LATEST_MESSAGE
    # for EVERY_K mode, apply to every k-th message
    supervisor_every_k: int = 1
    # client type for inference (anthropic, vertex_ai, etc.)
    client_type: str = "anthropic"

    override_tool_map: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    """Dictionary mapping tool names to their descriptions and schemas.

    Each entry is a dictionary with optional 'description' and 'schema' keys.
    Example: {'str-replace-editor': {'description': 'Custom editing tool...', 'schema': {...}}}
    """

    _preprocess_chat_prompt_input_funcs: list[
        Callable[[ChatPromptInput], ChatPromptInput]
    ] = field(
        default_factory=list,
        metadata=config(encoder=lambda x: None, exclude=lambda x: True),
    )

    def get_tool_definitions(
        self, request_tool_definitions: list[ToolDefinition]
    ) -> list[ToolDefinition]:
        tool_definitions = copy.deepcopy(request_tool_definitions)
        for tool_definition in tool_definitions:
            if tool_definition.name in self.override_tool_map:
                tool_info = self.override_tool_map[tool_definition.name]
                if "description" in tool_info:
                    tool_definition.description = tool_info["description"]
                if "schema" in tool_info:
                    tool_definition.input_schema_json = json.dumps(tool_info["schema"])
        existing_tool_names = set([tool.name for tool in tool_definitions])
        for name, tool_info in self.override_tool_map.items():
            if name not in existing_tool_names:
                tool_definitions.append(
                    ToolDefinition(
                        name=name,
                        description=tool_info.get("description", ""),
                        input_schema_json=json.dumps(tool_info.get("schema", {})),
                    )
                )
        return tool_definitions

    def get_additional_prefill(self) -> str:
        if self.supervisor_mode == SupervisorMode.PREFILL and self.supervisor_message:
            return self.supervisor_message + (self.additional_prefill or "")
        else:
            return self.additional_prefill or ""

    def override_system_prompt(
        self, system_prompt: str, model_name="Claude 4.0 Sonnet", creator="Anthropic"
    ) -> None:
        self.system_prompt = system_prompt
        # Only update the prompt formatter's system prompt if it's a StructuredBinksAgentPromptFormatter
        if isinstance(self.prompt_formatter, StructuredBinksAgentPromptFormatter):
            from base.prompt_format_chat.lib.system_prompts import (
                get_agent_system_prompt_formatter_v2,
            )

            # Create a new formatter with the custom system prompt
            self.prompt_formatter.system_prompt_formatter = (
                get_agent_system_prompt_formatter_v2(
                    token_counter=self.prompt_formatter.token_counter,
                    model_name=model_name,
                    creator=creator,
                )
            )
            # Override with custom template
            custom_template = jinja2.Template(
                system_prompt, lstrip_blocks=True, trim_blocks=True
            )
            self.prompt_formatter.system_prompt_formatter = JinjaFormatter(
                custom_template,
                token_counter=self.prompt_formatter.token_counter,
                default_values=AgentSystemPromptInfo(
                    model_name=model_name,
                    creator=creator,
                    formatted_custom_guidelines="",
                    memories="",
                    tools=[],
                    tasklist="",
                ),
            )
        # For other formatter types (like GeminiBinksChatPromptFormatter),
        # we just store the system_prompt and let the evaluation system handle it

    def _is_user_message(self, message: str | list[ChatRequestNode]) -> bool:
        """Determine if a message contains user-generated text content.

        For string messages, always returns True (assuming all string messages are from users).
        For structured messages (list of nodes), returns True if any node is a TEXT node.

        In the context of supervisor messages, we only want to add them to messages that contain
        actual user text content, not just tool results or other non-text content.

        Args:
            message: The message to check (string or list of ChatRequestNode).

        Returns:
            True if the message contains user-generated text content, False otherwise.
        """
        if isinstance(message, str):
            return True
        elif isinstance(message, list):
            # Check if any node is a TEXT node or an image node
            return any(node.type == ChatRequestNodeType.TEXT for node in message)

    def _should_add_supervisor_message(
        self, is_current_message: bool, message: Any, position_from_end: int = 0
    ) -> bool:
        """Determine if a supervisor message should be added based on the supervisor mode.

        This helper method centralizes the decision logic for all supervisor modes.

        Args:
            is_current_message: Whether this is the current message (True) or a history message (False).
            message: The message to check (string or list of ChatRequestNode).
            position_from_end: The position of the message from the end of the history (0 is most recent).

        Returns:
            True if a supervisor message should be added, False otherwise.
        """
        if not self.supervisor_message:
            return False

        if self.supervisor_mode == SupervisorMode.MESSAGE:
            # Add to all messages
            return True
        elif self.supervisor_mode == SupervisorMode.USER_MESSAGE:
            # Add only to user messages
            return self._is_user_message(message)
        elif self.supervisor_mode == SupervisorMode.EVERY_K:
            # Add to every k-th message, starting from k messages ago
            # Skip the latest message if it's not exactly k messages ago
            return (
                position_from_end > 0
                and position_from_end % self.supervisor_every_k == 0
            )
        elif self.supervisor_mode == SupervisorMode.LATEST_MESSAGE:
            # Add only to the current message, not to any history messages
            return is_current_message
        elif self.supervisor_mode == SupervisorMode.PREFILL:
            # Don't add to any messages when using prefill mode
            return False

        # Default case (shouldn't reach here)
        return False

    def _add_supervisor_message_to_message(self, message: Any) -> Any:
        """Add supervisor message to a message (either string or list of nodes).

        This helper method handles both string messages and structured messages (list of nodes).
        For string messages, it simply concatenates the supervisor message at the beginning.
        For structured messages, it finds the first text node and prepends the supervisor message
        to its content. If no text node exists, it creates a new text node with the supervisor
        message and adds it to the beginning of the list.

        Args:
            message: The message to add the supervisor message to (string or list of ChatRequestNode).

        Returns:
            The message with the supervisor message added, preserving the original message type.
        """
        if not self.supervisor_message:
            return message

        if isinstance(message, str):
            return message + self.supervisor_message
        elif isinstance(message, list):
            supervisor_message_node = ChatRequestNode(
                id=1,  # We'll use id=1 for all supervisor messages
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content=self.supervisor_message),
                tool_result_node=None,
                image_node=None,
            )
            return message + [supervisor_message_node]
        else:
            return message

    def _process_chat_history(self, chat_history) -> list:
        """Process chat history based on the supervisor mode.

        This method iterates through the chat history and applies the supervisor message
        to each exchange based on the selected supervisor mode using the unified logic
        in _should_add_supervisor_message.

        Args:
            chat_history: The chat history to process (list of Exchange objects).

        Returns:
            The processed chat history with supervisor messages added according to the selected mode.
        """
        if not self.supervisor_message:
            return chat_history

        processed_history = []
        for i, exchange in enumerate(chat_history):
            # Create a copy of the exchange to modify
            new_exchange = copy.deepcopy(exchange)

            # Calculate position from the end (0 is the most recent)
            position_from_end = len(chat_history) - 1 - i

            # Use the unified logic to determine if we should add supervisor message
            # For history messages, is_current_message is always False
            # In chat history, we're only processing user messages (request_message)
            should_add = self._should_add_supervisor_message(
                is_current_message=False,
                message=new_exchange.request_message,
                position_from_end=position_from_end,
            )

            if should_add:
                # Create a new exchange with the modified request_message
                new_exchange = dataclasses.replace(
                    new_exchange,
                    request_message=self._add_supervisor_message_to_message(
                        new_exchange.request_message
                    ),
                )

            processed_history.append(new_exchange)

        return processed_history

    def preprocess_chat_prompt_input(
        self, chat_prompt_input: ChatPromptInput
    ) -> ChatPromptInput:
        """Preprocess the chat prompt input based on the supervisor mode.

        This method applies the supervisor message to the chat prompt input based on the
        selected supervisor mode:

        - MESSAGE: Adds the supervisor message to the current message and all messages in the chat history
        - USER_MESSAGE: Adds the supervisor message to the current message and all user messages in the chat history
        - EVERY_K: Adds the supervisor message to the current message and every k-th message in the chat history,
          starting from k messages ago
        - LATEST_MESSAGE: Adds the supervisor message only to the latest message, not to any history messages
        - PREFILL: Adds the supervisor message to the message prefill instead of to any messages

        Args:
            chat_prompt_input: The chat prompt input to preprocess.

        Returns:
            The preprocessed chat prompt input with supervisor messages added according to the selected mode.
        """
        chat_prompt_input = copy.deepcopy(chat_prompt_input)

        # Apply any custom preprocessors first
        for func in self._preprocess_chat_prompt_input_funcs:
            chat_prompt_input = func(chat_prompt_input)

        if self.supervisor_message:
            # Handle prefill mode separately
            if self.supervisor_mode == SupervisorMode.PREFILL:
                # supervisor_message will be added to prefill in get_additional_prefill
                pass
            else:
                # For all other modes, process as before
                # Determine if we should add supervisor message to the current message
                # using the unified logic
                should_add_to_current = self._should_add_supervisor_message(
                    is_current_message=True,
                    message=chat_prompt_input.message,
                    position_from_end=0,  # Current message is at position 0
                )

                # Process the current message if needed
                new_message = chat_prompt_input.message
                if should_add_to_current:
                    new_message = self._add_supervisor_message_to_message(
                        chat_prompt_input.message
                    )

                # Process the chat history based on the supervisor mode
                new_chat_history = self._process_chat_history(
                    chat_prompt_input.chat_history
                )

                # Create a new chat prompt input with the processed message and history
                chat_prompt_input = dataclasses.replace(
                    chat_prompt_input,
                    message=new_message,
                    chat_history=new_chat_history,
                )

        return chat_prompt_input

    def eval_response(self, response: ModelResponse | None) -> Tuple[bool, str]:
        if response is None:
            return False, "Response is None"
        if self.eval_func is None:
            return True, ""
        return self.eval_func(response)

    def add_chat_prompt_input_preprocessor(
        self, func: Callable[[ChatPromptInput], ChatPromptInput]
    ) -> None:
        self._preprocess_chat_prompt_input_funcs.append(func)


def from_prod_model_name(model_name: str = DEFAULT_PROD_MODEL_NAME) -> ModelConfig:
    # based on chatanol3_third_party_chat_deploy.jsonnet
    if model_name == "claude-sonnet-3-7-200k-v2-agent":
        token_apportionment = ChatTokenApportionment(
            prefix_len=1024 * 2,
            suffix_len=1024 * 2,
            path_len=256,
            message_len=0,
            selected_code_len=-1,
            chat_history_len=0,
            retrieval_len_per_each_user_guided_file=0,
            retrieval_len_for_user_guided=0,
            retrieval_len=0,
            max_prompt_len=1024 * 200,
            tool_results_len=1024 * 120,
            token_budget_to_trigger_truncation=1024 * 120,
        )
        prompt_formatter = get_structured_chat_prompt_formatter_by_name(
            "agent-binks-claude-v2",
            token_apportionment,
        )
        assert isinstance(prompt_formatter, StructuredBinksAgentPromptFormatter)

        return ModelConfig(
            name="claude-sonnet-3-7-200k-v2-agent",
            anthropic_model="sonnet3.7",
            prompt_formatter=prompt_formatter,
            client_type="anthropic",
        )
    elif model_name == "claude-sonnet-3-7-150k-v2-agent":
        token_apportionment = ChatTokenApportionment(
            prefix_len=1024 * 2,
            suffix_len=1024 * 2,
            path_len=256,
            message_len=0,
            selected_code_len=-1,
            chat_history_len=0,
            retrieval_len_per_each_user_guided_file=0,
            retrieval_len_for_user_guided=0,
            retrieval_len=0,
            max_prompt_len=1024 * 150,
            tool_results_len=1024 * 90,
            token_budget_to_trigger_truncation=1024 * 90,
        )
        prompt_formatter = get_structured_chat_prompt_formatter_by_name(
            "agent-binks-claude-v2",
            token_apportionment,
        )
        assert isinstance(prompt_formatter, StructuredBinksAgentPromptFormatter)

        return ModelConfig(
            name="claude-sonnet-3-7-150k-v2-agent",
            anthropic_model="sonnet3.7",
            prompt_formatter=prompt_formatter,
            client_type="anthropic",
        )
    elif model_name == "claude-sonnet-3-7-200k-v3-agent":
        token_apportionment = ChatTokenApportionment(
            prefix_len=1024 * 2,
            suffix_len=1024 * 2,
            path_len=256,
            message_len=0,
            selected_code_len=8192,
            chat_history_len=0,
            retrieval_len_per_each_user_guided_file=0,
            retrieval_len_for_user_guided=0,
            retrieval_len=0,
            max_prompt_len=1024 * 200,
            tool_results_len=1024 * 120,
            token_budget_to_trigger_truncation=1024 * 120,
        )
        prompt_formatter = get_structured_chat_prompt_formatter_by_name(
            "agent-binks-claude-v3",
            token_apportionment,
        )
        assert isinstance(prompt_formatter, StructuredBinksAgentPromptFormatter)

        return ModelConfig(
            name="claude-sonnet-3-7-200k-v3-agent",
            anthropic_model="sonnet3.7",
            prompt_formatter=prompt_formatter,
            client_type="anthropic",
        )
    elif model_name == "claude-sonnet-4-0-200k-v3-agent":
        token_apportionment = ChatTokenApportionment(
            prefix_len=1024 * 2,
            suffix_len=1024 * 2,
            path_len=256,
            message_len=0,
            selected_code_len=8192,
            chat_history_len=0,
            retrieval_len_per_each_user_guided_file=0,
            retrieval_len_for_user_guided=0,
            retrieval_len=0,
            max_prompt_len=1024 * 200,
            tool_results_len=1024 * 120,
            token_budget_to_trigger_truncation=1024 * 120,
        )
        prompt_formatter = get_structured_chat_prompt_formatter_by_name(
            "agent-binks-claude-v3",
            token_apportionment,
        )
        assert isinstance(prompt_formatter, StructuredBinksAgentPromptFormatter)

        return ModelConfig(
            name="claude-sonnet-4-0-200k-v3-agent",
            anthropic_model="sonnet4.0",
            prompt_formatter=prompt_formatter,
            client_type="anthropic",
        )
    elif model_name == "gemini-2-5-flash":
        token_apportionment = ChatTokenApportionment(
            prefix_len=1024 * 2,
            suffix_len=1024 * 2,
            path_len=256,
            message_len=-1,  # Deprecated field: Not used by the structured-binks-gemini prompt formatter
            selected_code_len=-1,  # Deprecated field: Not used by the structured-binks-gemini prompt formatter
            chat_history_len=1024 * 4,
            retrieval_len_per_each_user_guided_file=2000,
            retrieval_len_for_user_guided=3000,
            retrieval_len=-1,  # Fill the rest of the input prompt with retrievals
            max_prompt_len=1024 * 12,  # 12k tokens for the prompt
            tool_results_len=1024 * 8,  # 8k for tool results
            token_budget_to_trigger_truncation=1024 * 8,
        )
        prompt_formatter = get_structured_chat_prompt_formatter_by_name(
            "binks-gemini-2.0-flash-exp",
            token_apportionment,
        )
        # Gemini models use GeminiBinksChatPromptFormatter which inherits from StructuredChatPromptFormatter
        # No assertion needed since we're now supporting different formatter types

        return ModelConfig(
            name="gemini-2-5-flash",
            anthropic_model="gemini2.5-flash",
            prompt_formatter=prompt_formatter,
            client_type="google_genai",
        )

    else:
        raise ValueError(f"Unknown model name: {model_name}")
