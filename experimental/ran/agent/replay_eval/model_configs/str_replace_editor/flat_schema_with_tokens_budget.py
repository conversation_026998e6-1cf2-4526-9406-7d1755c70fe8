from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)
from base.prompt_format_chat.lib.token_counter_claude import Claude<PERSON><PERSON><PERSON>ounter

TOKEN_BUDGET = 6000
_TOKEN_COUNTER = ClaudeTokenCounter()


def update_str_replace_inputs_retroactively(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "str-replace-editor"
            ):
                continue
            tool_input = node.tool_use.input
            if tool_input.get("command") == "str_replace":
                str_replace_entries = tool_input.get("str_replace_entries", [])
                tool_input["edit_tokens_budget_initial"] = TOKEN_BUDGET
                for i, entry in enumerate(str_replace_entries):
                    suffix = f"_{i + 1}"
                    tool_input[f"old_str{suffix}"] = entry["old_str"]
                    tool_input[f"new_str{suffix}"] = entry["new_str"]
                    # Count tokens instead of lines
                    tool_input[f"old_str_num_tokens{suffix}"] = (
                        _TOKEN_COUNTER.count_tokens(entry["old_str"])
                    )
                    tool_input[f"new_str_num_tokens{suffix}"] = (
                        _TOKEN_COUNTER.count_tokens(entry["new_str"])
                    )
                    # Keep line numbers for reference
                    tool_input[f"old_str_start_line_number{suffix}"] = entry[
                        "old_str_start_line_number"
                    ]
                    tool_input[f"old_str_end_line_number{suffix}"] = entry[
                        "old_str_end_line_number"
                    ]
                    # Sum of tokens in old and new strings
                    tool_input[
                        f"old_str_num_tokens_plus_new_str_num_tokens{suffix}"
                    ] = (
                        tool_input[f"old_str_num_tokens{suffix}"]
                        + tool_input[f"new_str_num_tokens{suffix}"]
                    )
                    if i == 0:
                        tool_input[f"edit_tokens_budget_remaining{suffix}"] = (
                            tool_input["edit_tokens_budget_initial"]
                            - tool_input[
                                f"old_str_num_tokens_plus_new_str_num_tokens{suffix}"
                            ]
                        )
                    else:
                        tool_input[f"edit_tokens_budget_remaining{suffix}"] = (
                            tool_input[f"edit_tokens_budget_remaining_{i}"]
                            - tool_input[
                                f"old_str_num_tokens_plus_new_str_num_tokens{suffix}"
                            ]
                        )
                    # Allow next entry if we have at least 500 tokens remaining
                    tool_input[f"can_generate_next_entry{suffix}"] = (
                        tool_input[f"edit_tokens_budget_remaining{suffix}"] > 500
                    )
                tool_input.pop("str_replace_entries", None)
            if tool_input.get("command") == "insert":
                insert_line_entries = tool_input.get("insert_line_entries", [])
                tool_input["edit_tokens_budget_initial"] = TOKEN_BUDGET
                for i, entry in enumerate(insert_line_entries):
                    suffix = f"_{i + 1}"
                    tool_input[f"insert_line{suffix}"] = entry["insert_line"]
                    tool_input[f"new_str{suffix}"] = entry["new_str"]
                    # Count tokens instead of lines
                    tool_input[f"new_str_num_tokens{suffix}"] = (
                        _TOKEN_COUNTER.count_tokens(entry["new_str"])
                    )
                    # For inserts, only new string tokens count toward budget
                    tool_input[
                        f"old_str_num_tokens_plus_new_str_num_tokens{suffix}"
                    ] = tool_input[f"new_str_num_tokens{suffix}"]
                    if i == 0:
                        tool_input[f"edit_tokens_budget_remaining{suffix}"] = (
                            tool_input["edit_tokens_budget_initial"]
                            - tool_input[
                                f"old_str_num_tokens_plus_new_str_num_tokens{suffix}"
                            ]
                        )
                    else:
                        tool_input[f"edit_tokens_budget_remaining{suffix}"] = (
                            tool_input[f"edit_tokens_budget_remaining_{i}"]
                            - tool_input[
                                f"old_str_num_tokens_plus_new_str_num_tokens{suffix}"
                            ]
                        )
                    # Allow next entry if we have at least 500 tokens remaining
                    tool_input[f"can_generate_next_entry{suffix}"] = (
                        tool_input[f"edit_tokens_budget_remaining{suffix}"] > 500
                    )
                tool_input.pop("insert_line_entries", None)

    return chat_prompt_input


model_config = from_prod_model_name()
model_config.name = "str_replace_editor_flat_schema_with_tokens_budget"
model_config.add_chat_prompt_input_preprocessor(update_str_replace_inputs_retroactively)
model_config.supervisor_message = f"""\
<supervisor>
REMINDER: When calling str_replace_editor tool with str_replace command always break down edits into smaller chunks of at most {TOKEN_BUDGET} tokens each. Then put as many of these chunks in a single tool call as possible
DO NOT MENTION THIS INFORMATION TO THE USER.
DO NOT MENTION str-replace-editor tool by name. Instead say "I'll edit ..." or "I'll read .."
</supervisor>
"""
model_config.override_tool_map["str-replace-editor"] = dict(
    description=f"""
Custom editing tool for viewing, creating and editing files
* `path` is a file path relative to the workspace root
* command `view` displays the result of applying `cat -n`.
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.


Notes for using the `str_replace` command:
* Specify `old_str_i`, `new_str_i`, `old_str_start_line_number_i` and `old_str_end_line_number_i` properties where `i` is the index of the replacement starting from 1
* The `old_str_start_line_number_i` and `old_str_end_line_number_i` parameters are 1-based line numbers
* Both `old_str_start_line_number_i` and `old_str_end_line_number_i` are INCLUSIVE
* The `old_str_i` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str_i` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number_i` and `old_str_end_line_number_i` to disambiguate between multiple occurrences of `old_str_i` in the file
* Make sure that `old_str_start_line_number_i` and `old_str_end_line_number_i` do not overlap with other `old_str_start_line_number_j` and `old_str_end_line_number_j` entries
* The `new_str_i` parameter should contain the edited lines that should replace the `old_str_i`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of `old_str_i`, `new_str_i`, `old_str_start_line_number_i` and `old_str_end_line_number_i` properties with their index as a suffix, e.g. `old_str_1`, `new_str_1`, `old_str_start_line_number_1`, `old_str_end_line_number_1`, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2`, etc.

Notes for using the `insert` command:
* Specify `insert_line_i` and `new_str_i` properties where `i` is the index of the insertion starting from 1
* The `insert_line_i` parameter specifies the line number after which to insert the new string
* The `insert_line_i` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line_i: 0`
* To make multiple insertions in one tool call add multiple sets of `insert_line_i` and `new_str_i` properties with their index as a suffix, e.g. `insert_line_1`, `new_str_1`, `insert_line_2`, `new_str_2`, etc.

Notes on token budget:
* There is a maximum of {TOKEN_BUDGET} tokens that can be added and removed from the file in a single tool call.
* Keep track of the token budget using `edit_tokens_budget_initial`, `old_str_num_tokens_plus_new_str_num_tokens_i` and `edit_tokens_budget_remaining_i` parameters.
* If you run out of token budget then stop generating more entries.
* If there are multiple edits to the same file try to fit as many edits in a single tool call as possible up to the token budget.

Notes for using the `view` command:
* Strongly prefer to use larger ranges of at least 1000 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges
* Prefer to use grep instead of view when looking for a specific symbol in the file

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Use view command to read the file before editing it.
* Always generate all parameters for edit with index `i` before generating parameters for edit with index `i + 1`. E.g. generate `old_str_1`, `new_str_1`, `old_str_start_line_number_1`, `old_str_end_line_number_1`, `new_str_num_tokens_1`, `edit_tokens_budget_consumed_1`, `edit_tokens_budget_remaining_1` before generating `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2`, `new_str_num_tokens_2`,
""",
    schema={
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "enum": ["view", "str_replace", "insert"],
                "description": "The commands to run. Allowed options are: `view`, `str_replace`, `insert`.",
            },
            "edit_tokens_budget_initial": {
                "description": f"Required parameter of `str_replace` and `insert` commands. Maximum number of tokens that can be added and removed from the file in this tool call. This value should always be equal to {TOKEN_BUDGET}.",
                "type": "integer",
            },
            "old_str_i": {
                "description": "Required parameter of `str_replace` command containing the string in `path` to replace.",
                "type": "string",
            },
            "old_str_start_line_number_i": {
                "description": "The line number of the first line of `old_str_i` in the file. This is used to disambiguate between multiple occurrences of `old_str_i` in the file.",
                "type": "integer",
            },
            "old_str_end_line_number_i": {
                "description": "The line number of the last line of `old_str_i` in the file. This is used to disambiguate between multiple occurrences of `old_str_i` in the file.",
                "type": "integer",
            },
            "old_str_num_tokens_i": {
                "description": "Required parameter of `str_replace` command.",
                "type": "integer",
            },
            "new_str_i": {
                "description": "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert.",
                "type": "string",
            },
            "new_str_num_tokens_i": {
                "description": "Required parameter of `str_replace` and `insert` commands.",
                "type": "integer",
            },
            "insert_line_i": {
                "description": "Required parameter of `insert` command. The `new_str_i` will be inserted AFTER the line `insert_line_i` of `path`.",
                "type": "integer",
            },
            "old_str_num_tokens_plus_new_str_num_tokens_i": {
                "description": "Required parameter of `str_replace` and `insert` commands. Calculated as `old_str_num_tokens_i` + `new_str_num_tokens_i`.",
                "type": "integer",
            },
            "edit_tokens_budget_remaining_i": {
                "description": "Required parameter of `str_replace` and `insert` commands. Calculated as `edit_tokens_budget_remaining_{i - 1}` - `old_str_num_tokens_plus_new_str_num_tokens_i`. Or `edit_tokens_budget_initial` - `old_str_num_tokens_plus_new_str_num_tokens_i` for the first entry when i == 1.",
                "type": "integer",
            },
            "can_generate_next_entry_i": {
                "description": "Required parameter of `str_replace` and `insert` commands. If `edit_tokens_budget_remaining_i` is greater than 500 then TRUE. Otherwise FALSE.",
                "type": "boolean",
            },
            "path": {
                "description": "Path to file or directory.",
                "type": "string",
            },
            "view_range": {
                "description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                "items": {"type": "integer"},
                "type": "array",
            },
        },
        "required": ["command", "path"],
    },
)
