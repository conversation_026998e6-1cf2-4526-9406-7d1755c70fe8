from base.prompt_format.common import ChatRequestNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)


def update_error_message(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    assert isinstance(chat_prompt_input.message, list)
    nodes = chat_prompt_input.message
    for node in nodes:
        if (
            node.type != ChatRequestNodeType.TOOL_RESULT
            or node.tool_result_node is None
        ):
            continue
        tool_result_str = node.tool_result_node.content
        if "includes is not a function" not in tool_result_str:
            continue
        node.tool_result_node.content = "Invalid parameter `old_str` for `str_replace` command. It must be a string not an object. Do not forget to wrap any code in old_str in quotes."

    return chat_prompt_input


model_config = from_prod_model_name()
model_config.name = "str_replace_editor_old_str_object_better_error"
model_config.add_chat_prompt_input_preprocessor(update_error_message)
