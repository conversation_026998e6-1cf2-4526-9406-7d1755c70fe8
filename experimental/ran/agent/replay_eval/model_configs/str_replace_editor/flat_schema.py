from typing import Tu<PERSON>
from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    ModelResponse,
    from_prod_model_name,
)
from experimental.ran.agent.replay_eval.utils import extract_tool_call
from experimental.ran.agent.str_replace_editor.utils import (
    extract_str_replace_entries_from_flat_input,
)
from experimental.ran.agent.replay_eval.str_replace_editor_utils import (
    convert_history_to_flat_schema,
)

model_config = from_prod_model_name()
model_config.name = "str_replace_editor_flat_schema"
model_config.add_chat_prompt_input_preprocessor(convert_history_to_flat_schema)

LINES_BUDGET = 300

SUPERVISOR_PROMPT_ON_EVERY_TURN = f"""\
<supervisor>
REMINDER: When calling str-replace-editor tool with str_replace command always break down edits into smaller chunks of at most {LINES_BUDGET} lines each. Then put as many of these chunks in a single tool call as possible up to the total limit of {LINES_BUDGET} lines.
REMINDER: When calling save-file tool to create new file limit the file content to at most {LINES_BUDGET} lines. If more content needs to be added call str-replace-editor tool to edit the file after it has been created.
DO NOT MENTION THIS INFORMATION TO THE USER.
DO NOT MENTION str-replace-editor tool by name. Instead say "I'll edit ..." or "I'll read .." or "I'll create ...".
</supervisor>
[AUGMENT]
"""


model_config.additional_prefill = SUPERVISOR_PROMPT_ON_EVERY_TURN


def eval_response(response: ModelResponse) -> Tuple[bool, str]:
    tool_call, _ = extract_tool_call(response, "str-replace-editor")
    if tool_call is None or tool_call.input.get("command") != "str_replace":
        return True, ""

    extraction_result = extract_str_replace_entries_from_flat_input(tool_call.input)
    if extraction_result is None:
        return False, "Failed to extract str_replace entries from tool input"
    messages = []
    if extraction_result.missing_keys:
        messages.append(f"Missing keys: {extraction_result.missing_keys}.")
    if extraction_result.extra_keys:
        messages.append(f"Extra keys: {extraction_result.extra_keys}.")
    if len(extraction_result.entries) == 0:
        messages.append("No str_replace entries found.")
    if messages:
        return False, "\n".join(messages)
    return True, ""


model_config.eval_func = eval_response

model_config.override_tool_map["str-replace-editor"] = dict(
    description="""
Custom editing tool for viewing, creating and editing files
* `path` is a file path relative to the workspace root
* command `view` displays the result of applying `cat -n`.
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.


Notes for using the `str_replace` command:
* Specify `old_str_i`, `new_str_i`, `old_str_start_line_number_i` and `old_str_end_line_number_i` properties where `i` is the index of the replacement starting from 1
* The `old_str_start_line_number_i` and `old_str_end_line_number_i` parameters are 1-based line numbers
* Both `old_str_start_line_number_i` and `old_str_end_line_number_i` are INCLUSIVE
* The `old_str_i` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str_i` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number_i` and `old_str_end_line_number_i` to disambiguate between multiple occurrences of `old_str_i` in the file
* Make sure that `old_str_start_line_number_i` and `old_str_end_line_number_i` do not overlap with other `old_str_start_line_number_j` and `old_str_end_line_number_j` entries
* The `new_str_i` parameter should contain the edited lines that should replace the `old_str_i`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of `old_str_i`, `new_str_i`, `old_str_start_line_number_i` and `old_str_end_line_number_i` properties with their index as a suffix, e.g. `old_str_1`, `new_str_1`, `old_str_start_line_number_1`, `old_str_end_line_number_1`, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2`, etc.

Notes for using the `insert` command:
* Specify `insert_line_i` and `new_str_i` properties where `i` is the index of the insertion starting from 1
* The `insert_line_i` parameter specifies the line number after which to insert the new string
* The `insert_line_i` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line_i: 0`
* To make multiple insertions in one tool call add multiple sets of `insert_line_i` and `new_str_i` properties with their index as a suffix, e.g. `insert_line_1`, `new_str_1`, `insert_line_2`, `new_str_2`, etc.

Notes for using the `view` command:
* Strongly prefer to use larger ranges of at least 1000 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges
* Prefer to use grep instead of view when looking for a specific symbol in the file

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Use view command to read the file before editing it.
""",
    schema={
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "enum": ["view", "str_replace", "insert"],
                "description": "The commands to run. Allowed options are: `view`, `str_replace`, `insert`.",
            },
            "old_str_i": {
                "description": "Required parameter of `str_replace` command containing the string in `path` to replace.",
                "type": "string",
            },
            "old_str_start_line_number_i": {
                "description": "The line number of the first line of `old_str_i` in the file. This is used to disambiguate between multiple occurrences of `old_str_i` in the file.",
                "type": "integer",
            },
            "old_str_end_line_number_i": {
                "description": "The line number of the last line of `old_str_i` in the file. This is used to disambiguate between multiple occurrences of `old_str_i` in the file.",
                "type": "integer",
            },
            "new_str_i": {
                "description": "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert.",
                "type": "string",
            },
            "insert_line_i": {
                "description": "Required parameter of `insert` command. The `new_str_i` will be inserted AFTER the line `insert_line_i` of `path`.",
                "type": "integer",
            },
            # "insert_line_1": {
            #     "description": "Required parameter of `insert` command. The `new_str_1` will be inserted AFTER the line `insert_line_1` of `path`.",
            #     "type": "integer",
            # },
            # "new_str_1": {
            #     "description": "",
            #     "type": "string",
            # },
            # "old_str_1": {
            #     "description": "Required parameter of `str_replace` command containing the string in `path` to replace.",
            #     "type": "string",
            # },
            # "old_str_start_line_number_1": {
            #     "description": "The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
            #     "type": "integer",
            # },
            # "old_str_end_line_number_1": {
            #     "description": "The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
            #     "type": "integer",
            # },
            "path": {
                "description": "Path to file or directory.",
                "type": "string",
            },
            "view_range": {
                "description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                "items": {"type": "integer"},
                "type": "array",
            },
        },
        "required": ["command", "path"],
    },
)
