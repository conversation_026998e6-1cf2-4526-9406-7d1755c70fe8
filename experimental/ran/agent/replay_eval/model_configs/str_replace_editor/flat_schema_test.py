from unittest.mock import MagicMock

from base.prompt_format.common import ChatResultNodeType
from experimental.ran.agent.replay_eval.model_configs.str_replace_editor.flat_schema import (
    update_str_replace_inputs_retroactively,
)


def test_update_str_replace_inputs_retroactively_empty_history():
    """Test with empty chat history."""
    # Create a mock ChatPromptInput with empty chat_history
    chat_input = MagicMock()
    chat_input.chat_history = []

    result = update_str_replace_inputs_retroactively(chat_input)

    # Should return the same object without changes
    assert result == chat_input


def test_update_str_replace_inputs_retroactively_no_tool_use():
    """Test with chat history but no tool use nodes."""
    # Create a mock node with RAW_RESPONSE type (not a tool use)
    mock_node = MagicMock()
    mock_node.type = ChatResultNodeType.RAW_RESPONSE
    mock_node.tool_use = None

    # Create a mock exchange with the node
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    result = update_str_replace_inputs_retroactively(chat_input)

    # Should return the same object without changes
    assert result == chat_input
    # The node should not be modified since it's not a tool use
    assert mock_node.tool_use is None


def test_update_str_replace_inputs_retroactively_different_tool():
    """Test with a tool use node but not str-replace-editor."""
    # Create a mock tool use with a different tool name
    mock_tool_use = MagicMock()
    mock_tool_use.name = "another-tool"
    mock_tool_use.input = {"param": "value"}

    # Create a mock node with TOOL_USE type
    mock_node = MagicMock()
    mock_node.type = ChatResultNodeType.TOOL_USE
    mock_node.tool_use = mock_tool_use

    # Create a mock exchange with the node
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    result = update_str_replace_inputs_retroactively(chat_input)

    # Should return the same object without changes to the tool input
    assert result == chat_input
    # The tool input should not be modified since it's not str-replace-editor
    assert mock_tool_use.input == {"param": "value"}


def test_update_str_replace_inputs_retroactively_str_replace():
    """Test with str_replace command."""
    # Create a tool use with str_replace_entries
    str_replace_entries = [
        {
            "old_str": "original text",
            "new_str": "new text",
            "old_str_start_line_number": 10,
            "old_str_end_line_number": 12,
        }
    ]

    # Create a mock tool use for str-replace-editor with str_replace command
    mock_tool_use = MagicMock()
    mock_tool_use.name = "str-replace-editor"
    mock_tool_use.input = {
        "command": "str_replace",
        "path": "test.py",
        "str_replace_entries": str_replace_entries,
    }

    # Create a mock node with TOOL_USE type
    mock_node = MagicMock()
    mock_node.type = ChatResultNodeType.TOOL_USE
    mock_node.tool_use = mock_tool_use

    # Create a mock exchange with the node
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    # Call the function
    update_str_replace_inputs_retroactively(chat_input)

    # Check that str_replace_entries was transformed to flat properties
    assert "str_replace_entries" not in mock_tool_use.input
    assert mock_tool_use.input["old_str"] == "original text"
    assert mock_tool_use.input["new_str"] == "new text"
    assert mock_tool_use.input["old_str_start_line_number"] == 10
    assert mock_tool_use.input["old_str_end_line_number"] == 12
    assert mock_tool_use.input["command"] == "str_replace"
    assert mock_tool_use.input["path"] == "test.py"


def test_update_str_replace_inputs_retroactively_multiple_str_replace_entries():
    """Test with multiple str_replace entries."""
    # Create a tool use with multiple str_replace_entries
    str_replace_entries = [
        {
            "old_str": "first original",
            "new_str": "first new",
            "old_str_start_line_number": 5,
            "old_str_end_line_number": 7,
        },
        {
            "old_str": "second original",
            "new_str": "second new",
            "old_str_start_line_number": 10,
            "old_str_end_line_number": 12,
        },
    ]

    # Create a mock tool use for str-replace-editor with str_replace command and multiple entries
    mock_tool_use = MagicMock()
    mock_tool_use.name = "str-replace-editor"
    mock_tool_use.input = {
        "command": "str_replace",
        "path": "test.py",
        "str_replace_entries": str_replace_entries,
    }

    # Create a mock node with TOOL_USE type
    mock_node = MagicMock()
    mock_node.type = ChatResultNodeType.TOOL_USE
    mock_node.tool_use = mock_tool_use

    # Create a mock exchange with the node
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    # Call the function
    update_str_replace_inputs_retroactively(chat_input)

    # Check that str_replace_entries was transformed to flat properties with suffixes
    assert "str_replace_entries" not in mock_tool_use.input

    # First entry (no suffix)
    assert mock_tool_use.input["old_str"] == "first original"
    assert mock_tool_use.input["new_str"] == "first new"
    assert mock_tool_use.input["old_str_start_line_number"] == 5
    assert mock_tool_use.input["old_str_end_line_number"] == 7

    # Second entry (with _1 suffix)
    assert mock_tool_use.input["old_str_1"] == "second original"
    assert mock_tool_use.input["new_str_1"] == "second new"
    assert mock_tool_use.input["old_str_start_line_number_1"] == 10
    assert mock_tool_use.input["old_str_end_line_number_1"] == 12


def test_update_str_replace_inputs_retroactively_insert():
    """Test with insert command."""
    # Create a tool use with insert_line_entries
    insert_line_entries = [{"insert_line": 5, "new_str": "inserted text"}]

    # Create a mock tool use for str-replace-editor with insert command
    mock_tool_use = MagicMock()
    mock_tool_use.name = "str-replace-editor"
    mock_tool_use.input = {
        "command": "insert",
        "path": "test.py",
        "insert_line_entries": insert_line_entries,
    }

    # Create a mock node with TOOL_USE type
    mock_node = MagicMock()
    mock_node.type = ChatResultNodeType.TOOL_USE
    mock_node.tool_use = mock_tool_use

    # Create a mock exchange with the node
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    # Call the function
    update_str_replace_inputs_retroactively(chat_input)

    # Check that insert_line_entries was transformed to flat properties
    assert "insert_line_entries" not in mock_tool_use.input
    assert mock_tool_use.input["insert_line"] == 5
    assert mock_tool_use.input["new_str"] == "inserted text"
    assert mock_tool_use.input["command"] == "insert"
    assert mock_tool_use.input["path"] == "test.py"


def test_update_str_replace_inputs_retroactively_multiple_insert_entries():
    """Test with multiple insert entries."""
    # Create a tool use with multiple insert_line_entries
    insert_line_entries = [
        {"insert_line": 5, "new_str": "first inserted text"},
        {"insert_line": 10, "new_str": "second inserted text"},
    ]

    # Create a mock tool use for str-replace-editor with insert command and multiple entries
    mock_tool_use = MagicMock()
    mock_tool_use.name = "str-replace-editor"
    mock_tool_use.input = {
        "command": "insert",
        "path": "test.py",
        "insert_line_entries": insert_line_entries,
    }

    # Create a mock node with TOOL_USE type
    mock_node = MagicMock()
    mock_node.type = ChatResultNodeType.TOOL_USE
    mock_node.tool_use = mock_tool_use

    # Create a mock exchange with the node
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    # Call the function
    update_str_replace_inputs_retroactively(chat_input)

    # Check that insert_line_entries was transformed to flat properties with suffixes
    assert "insert_line_entries" not in mock_tool_use.input

    # First entry (no suffix)
    assert mock_tool_use.input["insert_line"] == 5
    assert mock_tool_use.input["new_str"] == "first inserted text"

    # Second entry (with _1 suffix)
    assert mock_tool_use.input["insert_line_1"] == 10
    assert mock_tool_use.input["new_str_1"] == "second inserted text"


def test_update_str_replace_inputs_retroactively_multiple_tool_uses():
    """Test with multiple tool uses in the chat history."""
    # Create mock tool uses for two different exchanges

    # First tool use - str_replace
    mock_tool_use1 = MagicMock()
    mock_tool_use1.name = "str-replace-editor"
    mock_tool_use1.input = {
        "command": "str_replace",
        "path": "test1.py",
        "str_replace_entries": [
            {
                "old_str": "original in file 1",
                "new_str": "new in file 1",
                "old_str_start_line_number": 5,
                "old_str_end_line_number": 7,
            }
        ],
    }

    # First node
    mock_node1 = MagicMock()
    mock_node1.type = ChatResultNodeType.TOOL_USE
    mock_node1.tool_use = mock_tool_use1

    # First exchange
    mock_exchange1 = MagicMock()
    mock_exchange1.response_nodes = [mock_node1]

    # Second tool use - insert
    mock_tool_use2 = MagicMock()
    mock_tool_use2.name = "str-replace-editor"
    mock_tool_use2.input = {
        "command": "insert",
        "path": "test2.py",
        "insert_line_entries": [{"insert_line": 10, "new_str": "inserted in file 2"}],
    }

    # Second node
    mock_node2 = MagicMock()
    mock_node2.type = ChatResultNodeType.TOOL_USE
    mock_node2.tool_use = mock_tool_use2

    # Second exchange
    mock_exchange2 = MagicMock()
    mock_exchange2.response_nodes = [mock_node2]

    # Create a mock ChatPromptInput with both exchanges
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange1, mock_exchange2]

    # Call the function
    update_str_replace_inputs_retroactively(chat_input)

    # Check first exchange (str_replace)
    assert "str_replace_entries" not in mock_tool_use1.input
    assert mock_tool_use1.input["old_str"] == "original in file 1"
    assert mock_tool_use1.input["new_str"] == "new in file 1"
    assert mock_tool_use1.input["old_str_start_line_number"] == 5
    assert mock_tool_use1.input["old_str_end_line_number"] == 7

    # Check second exchange (insert)
    assert "insert_line_entries" not in mock_tool_use2.input
    assert mock_tool_use2.input["insert_line"] == 10
    assert mock_tool_use2.input["new_str"] == "inserted in file 2"


def test_update_str_replace_inputs_retroactively_mixed_node_types():
    """Test with a mix of tool use and other node types."""
    # Create a mix of different node types in a single exchange

    # First node - RAW_RESPONSE
    mock_node1 = MagicMock()
    mock_node1.type = ChatResultNodeType.RAW_RESPONSE
    mock_node1.content = "Some text response"
    mock_node1.tool_use = None

    # Second node - TOOL_USE with str-replace-editor
    mock_tool_use = MagicMock()
    mock_tool_use.name = "str-replace-editor"
    mock_tool_use.input = {
        "command": "str_replace",
        "path": "test.py",
        "str_replace_entries": [
            {
                "old_str": "original text",
                "new_str": "new text",
                "old_str_start_line_number": 10,
                "old_str_end_line_number": 12,
            }
        ],
    }

    mock_node2 = MagicMock()
    mock_node2.type = ChatResultNodeType.TOOL_USE
    mock_node2.tool_use = mock_tool_use

    # Third node - SUGGESTED_QUESTIONS
    mock_node3 = MagicMock()
    mock_node3.type = ChatResultNodeType.SUGGESTED_QUESTIONS
    mock_node3.content = "Suggested question"
    mock_node3.tool_use = None

    # Create a mock exchange with all three nodes
    mock_exchange = MagicMock()
    mock_exchange.response_nodes = [mock_node1, mock_node2, mock_node3]

    # Create a mock ChatPromptInput with the exchange
    chat_input = MagicMock()
    chat_input.chat_history = [mock_exchange]

    # Call the function
    update_str_replace_inputs_retroactively(chat_input)

    # Check that only the tool use node was modified
    # First node should remain unchanged
    assert mock_node1.type == ChatResultNodeType.RAW_RESPONSE
    assert mock_node1.content == "Some text response"
    assert mock_node1.tool_use is None

    # Second node (tool use) should be modified
    assert "str_replace_entries" not in mock_tool_use.input
    assert mock_tool_use.input["old_str"] == "original text"
    assert mock_tool_use.input["new_str"] == "new text"

    # Third node should remain unchanged
    assert mock_node3.type == ChatResultNodeType.SUGGESTED_QUESTIONS
    assert mock_node3.content == "Suggested question"
    assert mock_node3.tool_use is None
