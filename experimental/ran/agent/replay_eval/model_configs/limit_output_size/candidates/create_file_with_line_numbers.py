from typing import <PERSON><PERSON>
from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    ModelResponse,
    from_prod_model_name,
)
from experimental.ran.agent.replay_eval.utils import extract_tool_call
from experimental.ran.agent.str_replace_editor.utils import (
    extract_str_replace_entries_from_flat_input,
    add_line_numbers,
    split_into_chunks_by_lines,
)

CHUNK_SIZE = 50

model_config = from_prod_model_name()
model_config.name = "create_file_tool_with_line_numbers"
model_config.override_tool_map["save-file"] = dict(
    description=f"""\
Save a new file.
Use this tool to write new files with the attached content.
The content should be in a line-numbered format (like output from `cat -n`), with each line starting with line number(padded to 6 digits) and tab\n
Example:
```
     1\tdef my_function():
     2\t    print("Hello World")
     3\t
     4\tdef my_other_function():
     5\t    print("Hello World Again")
```

Split content into chunks of {CHUNK_SIZE} lines each.
Put each chunk in a separate tool input attribute. `file_content_with_line_numbers_chunk_1`, `file_content_with_line_numbers_chunk_2`, etc.

It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_content_with_line_numbers_chunk_1": {
                "type": "string",
                "description": f"This is the first chunk of {CHUNK_SIZE} lines.",
            },
            "file_content_with_line_numbers_chunk_2": {
                "type": "string",
                "description": f"This is the second chunk of {CHUNK_SIZE} lines.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["file_path", "file_content_with_line_numbers_chunk_1"],
    },
)


def update_save_file_inputs_retroactively(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "save-file"
            ):
                continue
            tool_input = node.tool_use.input
            if tool_input and tool_input.get("file_content"):
                file_content = tool_input["file_content"]
                file_content_with_line_numbers = add_line_numbers(file_content)
                chunks = split_into_chunks_by_lines(
                    file_content_with_line_numbers, CHUNK_SIZE
                )
                for i, chunk in enumerate(chunks, start=1):
                    tool_input[f"file_content_with_line_numbers_chunk_{i}"] = chunk
                del tool_input["file_content"]
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(update_save_file_inputs_retroactively)
