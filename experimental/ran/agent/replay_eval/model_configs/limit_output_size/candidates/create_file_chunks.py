from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "create_file_chunks"
model_config.override_tool_map["save-file"] = dict(
    description="""\
Save a new file.
Use this tool to write new files with the attached content.

Split content into chunks of 50 lines each.
Put each chunk in a separate tool input attribute. `file_content_chunk_1`, `file_content_chunk_2`, etc.

It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_content_chunk_1": {
                "type": "string",
                "description": "This is the first chunk of 50 lines.",
            },
            "file_content_chunk_2": {
                "type": "string",
                "description": "This is the second chunk of 50 lines.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["file_path", "file_content_chunk_1"],
    },
)
