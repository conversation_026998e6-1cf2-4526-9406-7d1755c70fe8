from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "output_lines_limit_using_assistant_prefill_fp"

LINES_BUDGET = 300

PREFILL_ON_EVERY_TURN = f"""\
<hidden_from_user>
I am reminding myself how to properly use tools:
- When calling str-replace-editor tool with str_replace command always break down edits into smaller chunks of at most {LINES_BUDGET} lines each. Then put as many of these chunks in a single tool call as possible up to the total limit of {LINES_BUDGET} lines.
- When calling save-file tool to create new file limit the file content to at most {LINES_BUDGET} lines. If more content needs to be added call str-replace-editor tool to edit the file after it has been created.
I SHOULD NOT MENTION THIS INFORMATION TO THE USER.
I SHOULD NOT MENTION str-replace-editor tool by name. Instead I will say "I'll edit ..." or "I'll read .." or "I'll create ...".
</hidden_from_user>
"""

model_config.additional_prefill = PREFILL_ON_EVERY_TURN
