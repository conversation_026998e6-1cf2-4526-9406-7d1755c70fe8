from typing import <PERSON><PERSON>
from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    ModelResponse,
    from_prod_model_name,
)
from experimental.ran.agent.replay_eval.utils import (
    extract_tool_call,
    add_line_numbers,
)
from experimental.ran.agent.str_replace_editor.utils import (
    extract_str_replace_entries_from_flat_input,
)

MAX_LINES = 300
INSTRUCTION = f"LIMIT THE FILE CONTENT TO AT MOST {MAX_LINES} LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."

model_config = from_prod_model_name()
model_config.name = "create_file_tool_reminder_with_line_numbers"
model_config.override_tool_map["save-file"] = dict(
    description=f"""\
Save a new file.
Use this tool to write new files with the attached content.
Generate `intructions_reminder` first to remind yourself to limit the file content to at most {MAX_LINES} lines.

The content should be in a line-numbered format (like output from `cat -n`), with each line starting with line number(padded to 6 digits) and tab\n
Example:
```
     1\tdef my_function():
     2\t    print("Hello World")
     3\t
     4\tdef my_other_function():
     5\t    print("Hello World Again")
```

It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "intructions_reminder": {
                "type": "string",
                "description": f"Should be exactly this string: '{INSTRUCTION}'",
            },
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_content_with_line_numbers": {
                "type": "string",
                "description": "The content of the file in a line-numbered format.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": [
            "intructions_reminder",
            "file_path",
            "file_content_with_line_numbers",
        ],
    },
)


def update_save_file_calls(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "save-file"
            ):
                continue
            tool_input = dict()
            tool_input["intructions_reminder"] = INSTRUCTION
            tool_input["file_path"] = node.tool_use.input["file_path"]
            tool_input["file_content_with_line_numbers"] = add_line_numbers(
                node.tool_use.input["file_content"]
            )
            if "add_last_line_newline" in node.tool_use.input:
                tool_input["add_last_line_newline"] = node.tool_use.input[
                    "add_last_line_newline"
                ]
            node.tool_use.input = tool_input
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(update_save_file_calls)
