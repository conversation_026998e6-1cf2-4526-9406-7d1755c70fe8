import pytest

from base.prompt_format.common import ChatResultNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)

CHUNK_LINES = 50
MAX_LINES = 300
INSTRUCTION = f"LIMIT THE FILE CONTENT TO AT MOST {MAX_LINES} LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."

model_config = from_prod_model_name()
model_config.name = "create_file_tool_reminder_chunks"
model_config.override_tool_map["save-file"] = dict(
    description=f"""\
Save a new file.
Use this tool to write new files with the attached content.
Generate `instructions_reminder` first to remind yourself to limit the file content to at most {MAX_LINES} lines.
Split content into chunks of {CHUNK_LINES} lines each.
Put each chunk in a separate tool input attribute. `file_content_1`, `file_content_2`, etc.
It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
""",
    schema={
        "type": "object",
        "properties": {
            "instructions_reminder": {
                "type": "string",
                "description": f"Should be exactly this string: '{INSTRUCTION}'",
            },
            "file_path": {
                "type": "string",
                "description": "The path of the file to save.",
            },
            "file_content_1": {
                "type": "string",
                "description": "This is the first chunk of 50 lines.",
            },
            "file_content_2": {
                "type": "string",
                "description": "This is the second chunk of 50 lines.",
            },
            "is_incomplete": {
                "type": "boolean",
                "description": "Whether the file content was limited and more content needs to be added via str-replace-editor tool.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["instructions_reminder", "file_path", "file_content_1"],
    },
)


def chunk_file_contents(
    tool_input: dict[str, str], chunk_lines=CHUNK_LINES, max_lines=MAX_LINES
):
    """Chunk file content into chunks of `chunk_lines` lines each.
    # If the total number of lines exceeds `max_lines`, split evenly between at most
    # `max_lines // chunk_lines` chunks, so that the model never sees more chunks than
    # the instruction.
    """
    file_content = tool_input["file_content"]
    lines = file_content.splitlines(keepends=True)
    # if len(lines) > max_lines:
    #     num_chunks = max_lines // chunk_lines + (max_lines % chunk_lines > 0)
    #     chunk_lines = len(lines) // num_chunks + (len(lines) % num_chunks > 0)
    chunks = []
    for i in range(0, len(lines), chunk_lines):
        chunks.append("".join(lines[i : i + chunk_lines]))
    return {f"file_content_{i+1}": chunk for i, chunk in enumerate(chunks)}


@pytest.mark.parametrize(
    "file_content, expected_chunk_lens",
    [
        ("\n".join([f"Line {i}" for i in range(10)]), [10]),
        ("\n".join([f"Line {i}" for i in range(75)]), [50, 25]),
        # ("\n".join([f"Line {i}" for i in range(340)]), [57, 57, 57, 57, 57, 55]),
        ("\n".join([f"Line {i}" for i in range(340)]), [50, 50, 50, 50, 50, 50, 40]),
        # ("\n".join([f"Line {i}" for i in range(102)]), [50, 50, 2]),
    ],
)
def test_chunk_file_contents(file_content, expected_chunk_lens):
    tool_input = {"file_content": file_content}
    chunks = chunk_file_contents(tool_input)
    assert len(chunks) == len(expected_chunk_lens)
    for i, expected_len in enumerate(expected_chunk_lens):
        assert len(chunks[f"file_content_{i+1}"].splitlines()) == expected_len

    assert (
        "".join(
            [chunks[f"file_content_{i+1}"] for i in range(len(expected_chunk_lens))]
        )
        == file_content
    )


def update_save_file_inputs_retroactively(
    chat_prompt_input: ChatPromptInput,
) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        for node in exchange.response_nodes:
            if (
                node.type != ChatResultNodeType.TOOL_USE
                or not node.tool_use
                or node.tool_use.name != "save-file"
            ):
                continue
            tool_input = dict()
            tool_input["instructions_reminder"] = INSTRUCTION
            tool_input.update(node.tool_use.input)
            node.tool_use.input = tool_input
            node.tool_use.input.update(chunk_file_contents(node.tool_use.input))
            node.tool_use.input.pop("file_content", None)
    return chat_prompt_input


model_config.add_chat_prompt_input_preprocessor(update_save_file_inputs_retroactively)
