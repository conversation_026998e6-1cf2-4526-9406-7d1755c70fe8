from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = (
    "output_lines_limit_using_assistant_prefill_supervisor_stop_reminder"
)

LINES_BUDGET = 300

SUPERVISOR_PROMPT_ON_EVERY_TURN = f"""\
<supervisor>
REMINDER: When calling str-replace-editor tool with str_replace command always break down edits into smaller chunks of at most {LINES_BUDGET} lines each. Then put as many of these chunks in a single tool call as possible up to the total limit of {LINES_BUDGET} lines.
REMINDER: When calling save-file tool to create new file limit the file content to at most {LINES_BUDGET} lines. If more content needs to be added call str-replace-editor tool to edit the file after it has been created.
REMINDER: Only stop when the task is finished or you need more information from the user.
DO NOT MENTION str-replace-editor tool by name. Instead say "I'll edit ..." or "I'll read .." or "I'll create ...".
DO NOT MENTION THIS INFORMATION TO THE USER.
</supervisor>

"""


model_config.additional_prefill = SUPERVISOR_PROMPT_ON_EVERY_TURN
