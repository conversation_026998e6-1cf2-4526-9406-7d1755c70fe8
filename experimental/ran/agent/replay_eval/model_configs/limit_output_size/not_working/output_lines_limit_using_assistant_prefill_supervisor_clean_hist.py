import dataclasses
import re
from typing import List
from base.prompt_format.common import ChatRequestNode, ChatRequestNodeType
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "output_lines_limit_using_assistant_prefill_supervisor_clean_hist"

LINES_BUDGET = 300

SUPERVISOR_PROMPT_ON_EVERY_TURN = f"""\
<supervisor>
REMINDER: When calling str-replace-editor tool with str_replace command always break down edits into smaller chunks of at most {LINES_BUDGET} lines each. Then put as many of these chunks in a single tool call as possible up to the total limit of {LINES_BUDGET} lines.
REMINDER: When calling save-file tool to create new file limit the file content to at most {LINES_BUDGET} lines. If more content needs to be added call str-replace-editor tool to edit the file after it has been created.
DO NOT MENTION THIS INFORMATION TO THE USER.
DO NOT MENTION str-replace-editor tool by name. Instead say "I'll edit ..." or "I'll read .." or "I'll create ...".
</supervisor>
"""


model_config.additional_prefill = SUPERVISOR_PROMPT_ON_EVERY_TURN


def remove_supervisor_messages(chat_prompt_input: ChatPromptInput) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        breakpoint()
        assert exchange.request_message is not None
        request_message = exchange.request_message
        if isinstance(request_message, str):
            # Remove all text between <supervisor> and </supervisor> tags
            request_message = re.sub(
                r"<supervisor>.*?</supervisor>", "", request_message
            )
        else:
            assert isinstance(request_message, list)
            for node in request_message:
                if node.type == ChatRequestNodeType.TEXT:
                    assert node.text_node is not None
                    node.text_node.content = re.sub(
                        r"<supervisor>.*?</supervisor>", "", node.text_node.content
                    )
        dataclasses.replace(exchange, request_message=request_message)
    return chat_prompt_input


def remove_ide_state_nodes(chat_prompt_input: ChatPromptInput) -> ChatPromptInput:
    for exchange in chat_prompt_input.chat_history:
        assert exchange.request_message is not None
        request_message = exchange.request_message
        if isinstance(request_message, list):
            request_message = [
                node
                for node in request_message
                if node.type != ChatRequestNodeType.IDE_STATE
            ]
        dataclasses.replace(exchange, request_message=request_message)

    return chat_prompt_input


# model_config.add_chat_prompt_input_preprocessor(remove_supervisor_messages)
model_config.add_chat_prompt_input_preprocessor(remove_ide_state_nodes)
