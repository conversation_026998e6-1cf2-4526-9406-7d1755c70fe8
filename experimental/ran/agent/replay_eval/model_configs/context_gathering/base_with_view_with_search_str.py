from experimental.ran.agent.replay_eval.model_config import (
    from_prod_model_name,
)

model_config = from_prod_model_name()
model_config.name = "base_with_view_with_search_str"
model_config.override_tool_map["view"] = dict(
    description="""\
Custom tool for viewing files
* `path` is a file path relative to the workspace root
* The tool displays the result of applying `cat -n` to the file
* If the output is long, it will be truncated and marked with `<response clipped>`

Notes for using the tool:
* Strongly prefer to use larger ranges of at least 500 lines when scanning through files. One call with large range is much more efficient than many calls with small ranges
* Use the `view_range` parameter to specify a range of lines to view, e.g. [501, 1000] will show lines from 501 to 1000
* Indices are 1-based and inclusive
* Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file
* Use the `search_str` parameter to search for a specific string in the file. If provided, the file will be shown with the lines containing the search string highlighted. The search is case-sensitive and uses exact matching.
* Examples of when to use `search_str`:
  * You want to find all references to a specific symbol in the file
  * You want to find definitions of a symbol in the file. E.g. search_str = "def foo("
""",
    schema={
        "type": "object",
        "properties": {
            "path": {
                "description": "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
                "type": "string",
            },
            "view_range": {
                "description": "Optional parameter when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                "type": "array",
                "items": {"type": "integer"},
            },
            "search_str": {
                "description": "Optional parameter to search for a specific string in the file. If provided, the file will be shown with the lines containing the search string highlighted. The search is case-sensitive and uses exact matching.",
                "type": "string",
            },
        },
        "required": ["path"],
    },
)
