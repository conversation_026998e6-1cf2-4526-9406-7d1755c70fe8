#!/usr/bin/env python3

from experimental.ran.agent.replay_eval.model_config import (
    ModelConfig,
    from_prod_model_name,
)

# Create base Sonnet 4 configuration
model_config = from_prod_model_name("claude-sonnet-4-0-200k-v3-agent")

# Override the name to indicate this is for focus lines baseline
model_config.name = "claude-sonnet-4-0-focus-lines-baseline"

# Add the focus_lines tool
focus_lines_tool = {
    "description": (
        "Identify the most important and interesting parts of a tool call result. "
        "Use this tool to highlight the few most critical blocks of code or output "
        "that are most relevant to understanding the result."
    ),
    "schema": {
        "type": "object",
        "properties": {
            "line_ranges": {
                "type": "array",
                "description": "List of line ranges representing the most important parts",
                "items": {
                    "type": "array",
                    "description": "A line range as [start_line, end_line] (inclusive)",
                    "items": {"type": "integer"},
                    "minItems": 2,
                    "maxItems": 2,
                },
            },
            "summary": {
                "type": "string",
                "description": "Brief, single-sentence explanation of what the focused code chunk represents or indicates (e.g., 'The tests passed', 'Build compilation failed with syntax errors', 'Service responded successfully with expected data')",
            },
        },
        "required": ["line_ranges"],
    },
}

model_config.override_tool_map["focus_lines"] = focus_lines_tool

focus_lines_supervisor_message = """Your goal is to perform comprehensive analysis of the tool call result and identify the most critical lines for understanding command execution results. This baseline configuration prioritizes result quality over latency or token efficiency.

ANALYSIS APPROACH: Before calling the focus_lines tool, perform detailed line-by-line analysis using structured thinking to ensure comprehensive coverage of all important content.

Analyze each line in the terminal output using the following structured approach:

<line_analysis>
  <line_number>
    [integer] - line number in the terminal response
  </line_number>
  <line_content>
    [exact text] - copy the line content exactly as it appears
  </line_content>
  <is_success_indicator>
    [boolean] - true if this line indicates whether the command succeeded or failed
  </is_success_indicator>
  <is_error_details>
    [boolean] - true if this line contains specific error information (test names, stack traces, error messages)
  </is_error_details>
  <is_duplicate_content>
    [boolean] - true if this line duplicates or nearly duplicates information from other lines
  </is_duplicate_content>
  <code_reference>
    [string] - if this line references specific code, cite the relevant code location or context
  </code_reference>
  <focus_reasoning>
    [string] - explain why this line should be included in the focused output
  </focus_reasoning>
  <ignore_reasoning>
    [string] - explain why this line could be safely excluded from the focused output
  </ignore_reasoning>
</line_analysis>

After analyzing all lines, use the focus_lines tool to select only the most essential lines for understanding the command execution outcome.

When calling the focus_lines tool:
1. Provide line_ranges for the most critical sections
2. Include a summary parameter with a brief, single-sentence explanation of what the focused code chunk represents or indicates

Examples of good summaries:
- "The tests passed successfully"
- "Build compilation failed with syntax errors"
- "Service responded with expected data"
- "Database connection error occurred"
- "Performance metrics show significant improvement"

COMPREHENSIVE FOCUS CRITERIA (prioritize quality over compression):
- Error messages or warnings (highest priority - include full context)
- Key output or results (final outcomes and intermediate steps if they provide insight)
- Important status information (completion status, counts, summaries, progress indicators)
- Critical code sections (especially those containing errors, warnings, or unexpected behavior)
- Unexpected or notable behavior (anything that deviates from normal execution)
- Test failure details (specific test names, assertion failures, stack traces)
- Build/compilation errors (specific file locations, error types, suggested fixes)
- Performance metrics or timing information (if relevant to understanding execution)
- Configuration or environment issues (missing dependencies, version conflicts)

BASELINE QUALITY STANDARDS:
- Include sufficient context around errors to understand root causes
- Preserve important sequential relationships between related lines
- Include summary information that helps understand overall execution flow
- Maintain enough detail to enable effective debugging or troubleshooting
- Err on the side of inclusion rather than exclusion when in doubt

After calling the tool, provide a detailed summary explaining why these sections are the most important and how they contribute to understanding the command execution outcome."""

# Set supervisor message to add instructions to the current message
model_config.supervisor_message = focus_lines_supervisor_message
