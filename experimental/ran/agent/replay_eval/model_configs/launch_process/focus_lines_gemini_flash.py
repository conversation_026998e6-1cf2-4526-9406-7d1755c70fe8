#!/usr/bin/env python3

from experimental.ran.agent.replay_eval.model_config import (
    ModelConfig,
    from_prod_model_name,
)

# Create base Gemini Flash configuration
model_config = from_prod_model_name("gemini-2-5-flash")

# Override the name to indicate this is for focus lines evaluation
model_config.name = "gemini-2-5-flash-focus-lines"

# Add the focus_lines tool
focus_lines_tool = {
    "description": (
        "Identify the most important and interesting parts of a tool call result. "
        "Use this tool to highlight the few most critical blocks of code or output "
        "that are most relevant to understanding the result."
    ),
    "schema": {
        "type": "object",
        "properties": {
            "line_ranges": {
                "type": "array",
                "description": "List of line ranges representing the most important parts",
                "items": {
                    "type": "array",
                    "description": "A line range as [start_line, end_line] (inclusive)",
                    "items": {"type": "integer"},
                    "minItems": 2,
                    "maxItems": 2,
                },
            },
            "summary": {
                "type": "string",
                "description": "Brief, single-sentence explanation of what the focused code chunk represents or indicates (e.g., 'The tests passed', 'Build compilation failed with syntax errors', 'Service responded successfully with expected data')",
            },
        },
        "required": ["line_ranges"],
    },
}

model_config.override_tool_map["focus_lines"] = focus_lines_tool

focus_lines_supervisor_message = """Your sole goal is to analyze the tool call result and identify the most interesting and important parts of the response. Call the focus_lines tool to indicate the few most critical blocks of code or output, and provide a brief summary explaining what the focused sections mean with regards to the task at hand.

COMPRESSION TARGET: Attempt to focus on 20 important lines or less - be extremely selective and avoid redundant content

When calling the focus_lines tool:
1. Provide line_ranges for the most critical sections
2. Include a summary parameter with a brief, single-sentence explanation of the response

Examples of good summaries:
- "All tests passed successfully"
- "Build compilation failed with syntax errors in the file X"
- "Response includes a list of 37 config files"
- "Service responded with expected data"
- "Database connection error occurred"
- "Performance metrics show significant improvement"

Focus on:
- Error messages (highest priority)
- Test failure details useful to triage and fix the issue (failed test names, assertion failures, stack traces)
- Suggestions of what could fix the failures, e.g. "3 errors are fixable with the --fix option"
- Key output or results (final outcomes, not intermediate steps)
- Important status information (completion status, counts, summaries)
- Critical code sections (only if they contain errors or unexpected behavior)
- Unexpected or notable behavior

AVOID focusing on:
- Large blocks of generated code (SQL queries, compiled output, etc.) - only include if they contain errors
- Repetitive or boilerplate code sections
- Successful test results or routine output
- Verbose data structures or configuration dumps
- Long lists of similar items
- Warnings that have no effect on the final outcome, especially when running tests or builds and they succeeded

For compilation/generation outputs: Focus only on the summary lines (e.g., "Found X models", "Compilation completed") and any error messages, NOT the generated code itself.

Select only the most essential parts that provide actionable insights or indicate problems - prioritize brevity and relevance over completeness. After calling the tool, provide a single paragraph summary explaining why these sections are the most important."""

# Set supervisor message to add instructions to the current message
model_config.supervisor_message = focus_lines_supervisor_message
