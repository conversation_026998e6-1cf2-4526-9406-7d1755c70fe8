# Replay Evaluation Framework

## Overview

The Replay Evaluation Framework is a system designed to evaluate AI agent performance by replaying historical user requests and measuring the agent's ability to respond correctly. This framework allows for systematic testing of different model configurations, prompt formats, and tool behaviors to identify improvements or regressions in agent performance.

The framework works by:
1. Loading historical user requests from a database
2. Running these requests through different model configurations
3. Evaluating the correctness of the responses using custom evaluation criteria
4. Generating HTML reports with detailed results and statistics

## Caching

Calls to <PERSON> are cached based on all inputs and attempt index.
Following changes would invalidate the cache:
- Changing the model configuration
- Changing the prompt formatter
- Changing the tool definitions
- Changing the eval samples

Changing the number of attempts would only run new attempts and not affect the cached results.

Since Claude responses are non deterministic if you want to rerun calls to <PERSON> you can use the `--no-cache` flag.

## Usage Examples

### Single Model Evaluation

```bash
python experimental/vpas/agent/replay_eval/run_replay_eval.py \
  --ref-model-config claude-sonnet-3-7-200k-v2-agent.py \
  --num-attempts 5
```

### Multiple Model Comparison

```bash
python experimental/vpas/agent/replay_eval/run_replay_eval.py \
  --ref-model-config claude-sonnet-3-7-200k-v2-agent.py \
  --model-configs str_replace_editor/prompt_larger_views.py planning/less_planning_prompt.py \
  --num-attempts 5
```

### Running on a subset of samples

```bash
python experimental/vpas/agent/replay_eval/run_replay_eval.py \
  --ref-model-config claude-sonnet-3-7-200k-v2-agent.py \
  --model-configs str_replace_editor/prompt_larger_views.py planning/less_planning_prompt.py \
  --samples str_replace_editor/many_small_views_issue.py \
  --num-attempts 5
```

### Running on specific request IDs

```bash
python experimental/vpas/agent/replay_eval/run_replay_eval.py \
  --ref-model-config claude-sonnet-3-7-200k-v2-agent.py \
  --request-ids req_01234567890abcdef req_fedcba0987654321 \
  --num-attempts 5
```

## Adding New Samples

To add a new evaluation sample:

1. Create a new Python file in the appropriate subdirectory of `eval_samples/`
2. Define a function that evaluates a model response and returns a tuple of (is_correct, explanation)
3. Create a list of `EvalSample` objects with request IDs, names, and the evaluation function
4. Export this list as a variable named `samples`

## Adding New Model Configurations

To add a new model configuration:

1. Create a new Python file in the `model_configs/` directory
2. Define a `ModelConfig` object with the appropriate model name, prompt formatter, and tool definitions
3. Export this object as a variable named `model_config`


## Key Classes and Their Relationships

### Core Classes

1. **EvalSample** (`eval_sample.py`)
   - Base class for all evaluation samples
   - Defines the interface for evaluating if a model response is correct
   - Contains request ID, name, and a function to evaluate correctness
   - Provides `eval_response()` method that returns both correctness and explanation
   - Used by: `EvalHarness`, `EvalResult`

2. **ModelConfig** (`model_config.py`)
   - Represents a model configuration including model name, prompt formatter, and tool definitions
   - Can override tool definitions for specific tests
   - Used by: `EvalHarness`, `EvalSummary`

3. **EvalHarness** (`run_replay_eval.py`)
   - Main class that runs evaluations on samples
   - Takes a model configuration and runs it against samples
   - Produces `EvalResult` objects for each sample
   - Uses: `ModelConfig`, `EvalSample`

### Result Classes (`eval_output.py`)

4. **Attempt**
   - Represents a single attempt at responding to a request
   - Contains the model response, whether it was correct, and an explanation if incorrect
   - Used by: `EvalResult`

5. **EvalResult**
   - Contains results for a single sample across multiple attempts
   - Tracks how many attempts were correct
   - Used by: `EvalSummary`

6. **EvalSummary**
   - Aggregates results across all samples for a model configuration
   - Contains overall statistics like success rate

7. **ComparisonSummary**
   - Compares results between different model configurations
   - Contains a reference model summary and multiple new model summaries
   - Used for A/B testing different models or configurations
   - Used by: `save_comparison_html_report`

### HTML Report Generation (`html_report/html_report_generator.py`)

8. **HTML Report Functions**
   - `generate_html_report`: Creates an HTML report from an `EvalSummary`
   - `generate_comparison_html_report`: Creates an HTML report comparing multiple models against a reference model
   - `save_comparison_html_report`: Saves the comparison HTML report and associated JavaScript to disk
   - `format_tool_use`, `format_response_node`: Helper functions for formatting HTML

## How It Works

1. **Sample Definition**:
   - Create Python files in the `eval_samples` directory that define samples
   - Each sample includes a request ID (from a historical request), a name, and a function to evaluate correctness
   - Samples are grouped by the behavior they're testing (e.g., retrieval, str_replace_editor)

2. **Model Configuration**:
   - Define model configurations in the `model_configs` directory
   - Configurations specify the model name, prompt formatter, and any tool definition overrides

3. **Running Evaluations**:
   - Use `run_replay_eval.py` to run evaluations on a set of samples with one or more model configurations
   - The script loads the samples, a reference model configuration, and one or more test model configurations
   - It runs the evaluation on all models and generates a comparison HTML report

4. **Viewing Results**:
   - A brief summary of the evaluation results is printed to the terminal
   - HTML reports are generated with detailed information about each sample and attempt
   - Reports include overall statistics and allow for expanding/collapsing details
   - For incorrect attempts, explanations are displayed to help understand why they failed
   - Comparison reports show improvements or regressions compared to a reference model
   - Reports use a tabbed interface to switch between different models for each sample
   - Reports are saved to a web server directory for easy access

# Launch Process - Usage example

### Find samples
```sh
python experimental/ran/agent/replay_eval/launch_process_sample_extractor.py --last-days 14 --min-lines 20 --limit 50
```

Copy request ids from `launch_process_samples.json` into STAGING_LAUNCH_PROCESS_REQUEST_IDS (in `experimental/ran/agent/replay_eval/launch_process_focus_runner.py`)

### Eval - gemini flash vs baseline
```sh
kubectl config use-context gke_augment-research-gsc_us-central1_gcp-us1
python -m experimental.ran.agent.replay_eval.launch_process_focus_runner   --baseline-model-config launch_process/focus_lines_baseline.py   --model-configs launch_process/focus_lines_gemini_flash.py   --max-attempts 3   --baseline-attempts 1
```

Detailed results will be stored in `/mnt/efs/augment/user/ran/launch_process_focus_eval_detailed_outputs`
