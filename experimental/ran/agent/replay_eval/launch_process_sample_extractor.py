#!/usr/bin/env python3

import logging
import random
from typing import List, Optional, Tuple

from experimental.ran.agent.analytics.conversation import Conversation
from experimental.ran.agent.analytics.launch_process_analysis import (
    find_launch_process_requests,
)
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.launch_process_eval_functions import (
    add_line_numbers_to_text,
    evaluate_focus_lines_against_baseline,
    generate_focus_lines_stats,
)
from experimental.ran.utils.ri_utils import (
    get_chat_host_request_factory,
    get_chat_host_response_factory,
)

logger = logging.getLogger(__name__)


def get_all_tenants_for_namespace(namespace: str) -> List[str]:
    """Get all tenant names for a given namespace.

    Args:
        namespace: The namespace string (e.g., "i0", "i1")

    Returns:
        List of tenant names that belong to the namespace

    Raises:
        ValueError: If the namespace is not recognized
    """
    from base.datasets.tenants import DATASET_TENANTS

    # If it's already a specific tenant name, return it as a single-item list
    if namespace in DATASET_TENANTS:
        return [namespace]

    # Map namespace to all its tenants
    namespace_tenant_mapping = {
        "i0": [
            name for name in DATASET_TENANTS.keys() if name.startswith("i0-vanguard")
        ],
        "i1": [
            name for name in DATASET_TENANTS.keys() if name.startswith("i1-vanguard")
        ],
        "dogfood": ["dogfood", "dogfood-shard"],
        "aitutor": ["aitutor-pareto", "aitutor-turing", "aitutor-mercor"],
    }

    if namespace in namespace_tenant_mapping:
        tenants = namespace_tenant_mapping[namespace]
        logger.info(
            f"Found {len(tenants)} tenants for namespace '{namespace}': {tenants}"
        )
        return tenants

    # If we can't resolve it, treat it as a single tenant
    if namespace in [
        "dogfood",
        "dogfood-shard",
        "aitutor-pareto",
        "aitutor-turing",
        "aitutor-mercor",
    ] or namespace.startswith(("i0-vanguard", "i1-vanguard")):
        return [namespace]

    raise ValueError(
        f"Unknown namespace: {namespace}. Available namespaces: i0, i1, dogfood, aitutor, or specific tenant names"
    )


def resolve_namespace_to_tenant(namespace: str) -> str:
    """Resolve a namespace to a specific tenant name.

    Args:
        namespace: The namespace string (e.g., "i0", "i1", or a specific tenant name)

    Returns:
        A specific tenant name that can be used with get_tenant()

    Raises:
        ValueError: If the namespace cannot be resolved to a valid tenant
    """
    # If it's already a specific tenant name, return it as-is
    if namespace in [
        "dogfood",
        "dogfood-shard",
        "aitutor-pareto",
        "aitutor-turing",
        "aitutor-mercor",
    ]:
        return namespace

    # Check if it's already a specific vanguard tenant
    if namespace.startswith(("i0-vanguard", "i1-vanguard")):
        return namespace

    # Map namespace to default tenant
    namespace_to_tenant_mapping = {
        "i0": "i0-vanguard0",  # Default to vanguard0 for i0 namespace
        "i1": "i1-vanguard0",  # Default to vanguard0 for i1 namespace
    }

    if namespace in namespace_to_tenant_mapping:
        resolved_tenant = namespace_to_tenant_mapping[namespace]
        logger.info(f"Resolved namespace '{namespace}' to tenant '{resolved_tenant}'")
        return resolved_tenant

    # If we can't resolve it, return as-is and let get_tenant() handle the error
    return namespace


class LaunchProcessToolCallData:
    """Container for LaunchProcess tool call data and context."""

    def __init__(
        self,
        request_id: str,
        tool_output: str,
        numbered_output: str,
        context_history: str,
        tool_input: dict,
        tenant_name: str = "i0-vanguard0",
    ):
        self.request_id = request_id
        self.tool_output = tool_output
        self.numbered_output = numbered_output
        self.context_history = context_history
        self.tool_input = tool_input
        self.tenant_name = tenant_name


def extract_launch_process_tool_calls_from_conversations(
    conversations: List[Conversation],
    min_output_lines: int = 0,
) -> List[LaunchProcessToolCallData]:
    """Extract LaunchProcess tool call data from conversations.

    Args:
        conversations: List of conversations to extract from
        min_output_lines: Minimum number of lines in tool output to include

    Returns:
        List of LaunchProcessToolCallData objects
    """
    tool_call_data = []

    for conversation in conversations:
        for round_idx, round in enumerate(conversation.agent_rounds):
            for turn_idx, turn in enumerate(round.agent_turns):
                if (
                    turn.tool_call
                    and turn.tool_call.tool_use
                    and turn.tool_call.tool_use.name
                    in ["launch-process", "LaunchProcess", "launch_process"]
                    and turn.tool_call.tool_result
                    and turn.tool_call.tool_result.content
                ):
                    # Extract tool output
                    tool_output = turn.tool_call.tool_result.content

                    # Filter by minimum output lines
                    if min_output_lines > 0:
                        output_lines = (
                            tool_output.count("\n") + 1 if tool_output.strip() else 0
                        )
                        if output_lines < min_output_lines:
                            logger.debug(
                                f"Skipping LaunchProcess tool call from {turn.request_id}: "
                                f"output has {output_lines} lines, minimum required is {min_output_lines}"
                            )
                            continue

                    # Create line-numbered version
                    numbered_output = add_line_numbers_to_text(tool_output)

                    # Build context history (all previous rounds + current user message)
                    context_parts = []

                    # Add previous rounds
                    for prev_round in conversation.agent_rounds[:round_idx]:
                        context_parts.append(f"User: {prev_round.user_message}")
                        for prev_turn in prev_round.agent_turns:
                            context_parts.append(f"Assistant: {prev_turn.message}")

                    # Add current round's user message
                    context_parts.append(f"User: {round.user_message}")

                    # Add current round's agent turns up to this tool call
                    for prev_turn in round.agent_turns[: turn_idx + 1]:
                        context_parts.append(f"Assistant: {prev_turn.message}")

                    context_history = "\n\n".join(context_parts)

                    tool_call_data.append(
                        LaunchProcessToolCallData(
                            request_id=turn.request_id,
                            tool_output=tool_output,
                            numbered_output=numbered_output,
                            context_history=context_history,
                            tool_input=turn.tool_call.tool_use.input,
                            tenant_name=getattr(
                                conversation, "tenant_name", "i0-vanguard0"
                            ),
                        )
                    )

                    logger.info(
                        f"Extracted LaunchProcess tool call from {turn.request_id} "
                        f"(output has {tool_output.count(chr(10)) + 1 if tool_output.strip() else 0} lines)"
                    )

    return tool_call_data


def create_eval_sample_from_tool_call_data(
    tool_call_data: LaunchProcessToolCallData,
    baseline_line_ranges: Optional[List[Tuple[int, int]]] = None,
    sample_name_suffix: str = "",
) -> EvalSample:
    """Create an EvalSample from LaunchProcess tool call data.

    Args:
        tool_call_data: The tool call data to create sample from
        baseline_line_ranges: Optional baseline line ranges for evaluation
        sample_name_suffix: Optional suffix for sample name

    Returns:
        EvalSample configured for LaunchProcess focus evaluation
    """

    def eval_func(response, tool_definitions, request_id):
        if baseline_line_ranges is None:
            # If no baseline, just check that focus_lines tool was called
            from experimental.ran.agent.replay_eval.launch_process_eval_functions import (
                extract_focus_lines_from_response,
            )

            focus_lines = extract_focus_lines_from_response(response)
            if focus_lines:
                return (
                    True,
                    f"Successfully called focus_lines with {len(focus_lines)} ranges",
                )
            else:
                return False, "No focus_lines tool call found"
        else:
            # Evaluate against baseline
            return evaluate_focus_lines_against_baseline(response, baseline_line_ranges)

    def stats_func(response, tool_definitions, request_id):
        if baseline_line_ranges is None:
            return {}
        return generate_focus_lines_stats(response, baseline_line_ranges)

    sample_name = f"launch_process_focus_{tool_call_data.request_id}"
    if sample_name_suffix:
        sample_name += f"_{sample_name_suffix}"

    return EvalSample(
        request_id=tool_call_data.request_id,
        name=sample_name,
        eval_response_func=eval_func,
        gen_stats_func=stats_func,
        tenant_name=tool_call_data.tenant_name,
        category="launch_process_focus",
    )


def create_synthetic_request_for_tool_output(
    tool_call_data: LaunchProcessToolCallData,
) -> str:
    """Create a synthetic request ID for evaluating tool output focus.

    This creates a new conversation context where the user asks the model to analyze
    the LaunchProcess tool output and identify the most important parts.

    Args:
        tool_call_data: The tool call data containing the output to analyze

    Returns:
        A synthetic request ID that can be used for evaluation
    """
    # For now, we'll use the original request ID and modify the evaluation
    # to inject the tool output context. In a full implementation, we might
    # create actual synthetic requests in the database.
    return tool_call_data.request_id


def extract_launch_process_samples_from_request_ids(
    request_ids: List[str],
    tenant_name: str = "i0-vanguard0",
    min_output_lines: int = 0,
) -> List[LaunchProcessToolCallData]:
    """Extract LaunchProcess tool call data from specific request IDs.

    Args:
        request_ids: List of request IDs to process
        tenant_name: Tenant name for data access
        min_output_lines: Minimum number of lines in tool output to include

    Returns:
        List of LaunchProcessToolCallData objects
    """
    tool_call_data = []

    _get_chat_host_request = get_chat_host_request_factory(tenant_name)
    _get_chat_host_response = get_chat_host_response_factory(tenant_name)

    for request_id in request_ids:
        try:
            logger.info(f"Processing request ID: {request_id}")

            # Get the chat host request and response
            chat_request = _get_chat_host_request(request_id)
            chat_response = _get_chat_host_response(request_id)

            if not chat_request or not chat_response:
                logger.warning(
                    f"Could not find chat request or response for {request_id}"
                )
                continue

            # Convert to conversation
            conversation = Conversation.from_chat_request(
                request_id=request_id,
                chat_request=chat_request,
                chat_response=chat_response,
                get_chat_host_request_func=_get_chat_host_request,
            )

            # Extract LaunchProcess tool calls
            conversation_tool_calls = (
                extract_launch_process_tool_calls_from_conversations(
                    [conversation], min_output_lines
                )
            )
            tool_call_data.extend(conversation_tool_calls)

        except Exception as e:
            logger.error(
                f"Error processing request ID {request_id}: {e}", exc_info=True
            )
            continue

    logger.info(
        f"Extracted {len(tool_call_data)} LaunchProcess tool calls from {len(request_ids)} request IDs"
    )
    return tool_call_data


def main():
    """Main function to run the sample extractor from command line."""
    import argparse
    import datetime
    import json
    from base.datasets.tenants import get_tenant
    from experimental.ran.agent.analytics.big_query_utils import (
        get_agent_conv_last_request_ids,
    )

    parser = argparse.ArgumentParser(
        description="Extract LaunchProcess tool call samples from specified namespace. "
        "Supports multi-tenant namespaces (e.g., 'i0' processes all i0-vanguard* tenants)."
    )
    parser.add_argument(
        "--last-days",
        type=int,
        default=14,
        help="Number of days to look back (default: 14)",
    )
    parser.add_argument(
        "--min-lines",
        type=int,
        default=20,
        help="Minimum number of lines in launch-process output (default: 20)",
    )
    parser.add_argument(
        "--namespace",
        type=str,
        default="i0",
        help="Namespace to query. Can be a namespace (e.g., 'i0' for all i0-vanguard* tenants) "
        "or a specific tenant name (e.g., 'i0-vanguard0'). Default: 'i0'",
    )
    parser.add_argument(
        "--limit",
        type=int,
        help="Maximum number of request IDs to process",
    )
    parser.add_argument(
        "--output-file",
        type=str,
        default="launch_process_samples.json",
        help="Output file for request IDs (default: launch_process_samples.json)",
    )
    parser.add_argument(
        "--detailed-output-file",
        type=str,
        default="launch_process_samples_detailed.json",
        help="Output file for detailed sample data (default: launch_process_samples_detailed.json)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Calculate date range
    to_date = datetime.datetime.now()
    from_date = to_date - datetime.timedelta(days=args.last_days)

    logger.info(f"Extracting LaunchProcess samples from {from_date} to {to_date}")
    logger.info(f"Namespace: {args.namespace}")
    logger.info(f"Minimum output lines: {args.min_lines}")

    # Get all tenants for the namespace
    tenant_names = get_all_tenants_for_namespace(args.namespace)
    logger.info(f"Processing {len(tenant_names)} tenants: {tenant_names}")

    # Collect request IDs from all tenants
    all_request_ids = []
    tenant_request_mapping = {}  # Track which tenant each request ID came from

    for tenant_name in tenant_names:
        logger.info(f"Fetching conversation request IDs for tenant: {tenant_name}")
        try:
            tenant_request_ids = get_agent_conv_last_request_ids(
                tenant=get_tenant(tenant_name),
                from_datetime=from_date,
                to_datetime=to_date,
            )
            logger.info(
                f"Found {len(tenant_request_ids)} request IDs for {tenant_name}"
            )

            # Track tenant for each request ID
            for request_id in tenant_request_ids:
                tenant_request_mapping[request_id] = tenant_name

            all_request_ids.extend(tenant_request_ids)
        except Exception as e:
            logger.error(f"Error fetching request IDs for tenant {tenant_name}: {e}")
            continue

    logger.info(
        f"Found {len(all_request_ids)} total conversation request IDs across all tenants"
    )

    # Randomize the request IDs to ensure diverse sampling across tenants and time
    random.shuffle(all_request_ids)

    # Apply limit after randomization
    if args.limit:
        all_request_ids = all_request_ids[: args.limit]
        logger.info(f"Limited to {args.limit} request IDs after randomization")

    # Extract LaunchProcess tool call data from all tenants
    logger.info("Extracting LaunchProcess tool call data...")
    all_tool_call_data = []

    # Group request IDs by tenant for efficient processing
    tenant_requests = {}
    for request_id in all_request_ids:
        tenant_name = tenant_request_mapping[request_id]
        if tenant_name not in tenant_requests:
            tenant_requests[tenant_name] = []
        tenant_requests[tenant_name].append(request_id)

    # Process each tenant's requests
    for tenant_name, request_ids in tenant_requests.items():
        logger.info(
            f"Processing {len(request_ids)} request IDs for tenant {tenant_name}"
        )
        try:
            tenant_tool_call_data = extract_launch_process_samples_from_request_ids(
                request_ids=request_ids,
                tenant_name=tenant_name,
                min_output_lines=args.min_lines,
            )
            all_tool_call_data.extend(tenant_tool_call_data)
            logger.info(
                f"Extracted {len(tenant_tool_call_data)} tool calls from {tenant_name}"
            )
        except Exception as e:
            logger.error(f"Error extracting tool calls for tenant {tenant_name}: {e}")
            continue

    tool_call_data = all_tool_call_data

    logger.info(f"Extracted {len(tool_call_data)} LaunchProcess tool calls")

    # Save detailed results to file
    detailed_output_data = []
    for data in tool_call_data:
        detailed_output_data.append(
            {
                "request_id": data.request_id,
                "tool_output": data.tool_output,
                "numbered_output": data.numbered_output,
                "context_history": data.context_history,
                "tool_input": data.tool_input,
                "tenant_name": data.tenant_name,
                "output_line_count": data.tool_output.count("\n") + 1
                if data.tool_output.strip()
                else 0,
            }
        )

    with open(args.detailed_output_file, "w") as f:
        json.dump(detailed_output_data, f, indent=2)

    logger.info(
        f"Saved {len(detailed_output_data)} detailed samples to {args.detailed_output_file}"
    )

    # Save request IDs only to main output file
    request_ids_output = {"request_ids": [data.request_id for data in tool_call_data]}

    with open(args.output_file, "w") as f:
        json.dump(request_ids_output, f, indent=2)

    logger.info(
        f"Saved {len(request_ids_output['request_ids'])} request IDs to {args.output_file}"
    )

    # Print summary statistics
    if detailed_output_data:
        line_counts = [sample["output_line_count"] for sample in detailed_output_data]
        logger.info("Output line count statistics:")
        logger.info(f"  Min: {min(line_counts)}")
        logger.info(f"  Max: {max(line_counts)}")
        logger.info(f"  Average: {sum(line_counts) / len(line_counts):.1f}")

        # Print tenant distribution statistics
        tenant_counts = {}
        for sample in detailed_output_data:
            tenant_name = sample["tenant_name"]
            tenant_counts[tenant_name] = tenant_counts.get(tenant_name, 0) + 1

        logger.info("Tenant distribution:")
        for tenant_name, count in sorted(tenant_counts.items()):
            percentage = (count / len(detailed_output_data)) * 100
            logger.info(f"  {tenant_name}: {count} samples ({percentage:.1f}%)")


if __name__ == "__main__":
    main()
