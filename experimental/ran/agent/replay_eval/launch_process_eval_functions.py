#!/usr/bin/env python3

import logging
from typing import Any, Dict, List, Set, Tuple

from experimental.ran.agent.replay_eval.model_config import ModelResponse

logger = logging.getLogger(__name__)


def extract_tool_call(response: ModelResponse, tool_name: str) -> Tuple[Any, str]:
    """Extract a specific tool call from a model response.

    Args:
        response: The model response to extract tool call from
        tool_name: Name of the tool to extract

    Returns:
        Tuple of (tool_call, error_message)
    """
    tool_calls = []

    for node in response:
        # ChatResultNodeType.TOOL_USE = 5
        if node.type == 5 and hasattr(node, "tool_use") and node.tool_use:
            tool_calls.append(node.tool_use)

    if len(tool_calls) == 0:
        return None, "No tool calls found"
    if len(tool_calls) > 1:
        return None, "Too many tool calls found"

    tool_call = tool_calls[0]
    if tool_call.name != tool_name:
        return None, f"Expected tool call: {tool_name}, found: {tool_call.name}"

    return tool_call, ""


def add_line_numbers_to_text(text: str) -> str:
    """Transform text into line-numbered format: <line_number><tab><line_text>"""
    lines = text.split("\n")
    numbered_lines = []
    for i, line in enumerate(lines, 1):
        numbered_lines.append(f"{i}\t{line}")
    return "\n".join(numbered_lines)


def extract_focus_lines_from_response(response: ModelResponse) -> List[Tuple[int, int]]:
    """Extract focus_lines tool call results from model response.

    Args:
        response: The model response to extract focus lines from

    Returns:
        List of (start_line, end_line) tuples representing focused line ranges
    """
    focus_lines_data = extract_focus_lines_with_summary_from_response(response)
    return focus_lines_data["line_ranges"]


def extract_focus_lines_with_summary_from_response(
    response: ModelResponse,
) -> Dict[str, Any]:
    """Extract focus_lines tool call results including summary from model response.

    Args:
        response: The model response to extract focus lines from

    Returns:
        Dictionary with keys:
        - "line_ranges": List of (start_line, end_line) tuples representing focused line ranges
        - "summary": Optional string summary of what the focused code represents
    """
    focus_lines_tool_call, error_msg = extract_tool_call(response, "focus_lines")
    if focus_lines_tool_call is None:
        logger.warning(f"No focus_lines tool call found: {error_msg}")
        return {"line_ranges": [], "summary": None}

    try:
        line_ranges = focus_lines_tool_call.input.get("line_ranges", [])
        if not isinstance(line_ranges, list):
            logger.warning(f"line_ranges is not a list: {line_ranges}")
            return {"line_ranges": [], "summary": None}

        result_ranges = []
        for range_item in line_ranges:
            if isinstance(range_item, list) and len(range_item) == 2:
                start_line, end_line = range_item
                if isinstance(start_line, int) and isinstance(end_line, int):
                    result_ranges.append((start_line, end_line))
                else:
                    logger.warning(f"Invalid line range format: {range_item}")
            else:
                logger.warning(f"Invalid line range format: {range_item}")

        # Extract summary if present
        summary = focus_lines_tool_call.input.get("summary")
        if summary is not None and not isinstance(summary, str):
            logger.warning(f"Summary is not a string: {summary}")
            summary = None

        return {"line_ranges": result_ranges, "summary": summary}
    except Exception as e:
        logger.error(f"Error extracting focus lines: {e}")
        return {"line_ranges": [], "summary": None}


def expand_line_ranges_to_set(line_ranges: List[Tuple[int, int]]) -> Set[int]:
    """Convert list of line ranges to set of individual line numbers.

    Args:
        line_ranges: List of (start_line, end_line) tuples

    Returns:
        Set of individual line numbers covered by the ranges
    """
    lines = set()
    for start_line, end_line in line_ranges:
        # Ensure start_line <= end_line
        if start_line > end_line:
            start_line, end_line = end_line, start_line

        # Add all lines in the range (inclusive)
        for line_num in range(start_line, end_line + 1):
            lines.add(line_num)

    return lines


def calculate_overlap_metrics(
    baseline_lines: Set[int], evaluated_lines: Set[int]
) -> Tuple[float, float]:
    """Calculate overlap and hit percentages between baseline and evaluated line sets.

    Args:
        baseline_lines: Set of line numbers from baseline (Sonnet 4)
        evaluated_lines: Set of line numbers from evaluated model

    Returns:
        Tuple of (overlap_percent, hit_percent)
        - overlap_percent: (overlapping lines) / (total unique lines) * 100
        - hit_percent: (baseline lines hit by evaluated) / (total baseline lines) * 100
    """
    if not baseline_lines and not evaluated_lines:
        return 100.0, 100.0  # Perfect match when both are empty

    if not baseline_lines:
        return (
            0.0,
            100.0 if not evaluated_lines else 0.0,
        )  # No baseline to compare against

    if not evaluated_lines:
        return 0.0, 0.0  # Evaluated model selected nothing

    # Calculate overlapping lines
    overlapping_lines = baseline_lines.intersection(evaluated_lines)

    # Calculate total unique lines
    total_unique_lines = baseline_lines.union(evaluated_lines)

    # Calculate metrics
    overlap_percent = (len(overlapping_lines) / len(total_unique_lines)) * 100
    hit_percent = (len(overlapping_lines) / len(baseline_lines)) * 100

    return overlap_percent, hit_percent


def calculate_enhanced_metrics(
    baseline_lines: Set[int], evaluated_lines: Set[int], total_lines: int
) -> Dict[str, float]:
    """Calculate enhanced metrics including line counts and focus percentages.

    Args:
        baseline_lines: Set of line numbers from baseline (Sonnet 4)
        evaluated_lines: Set of line numbers from evaluated model
        total_lines: Total number of lines in original response

    Returns:
        Dictionary with comprehensive evaluation metrics
    """
    # Calculate basic overlap metrics
    overlap_percent, hit_percent = calculate_overlap_metrics(
        baseline_lines, evaluated_lines
    )

    # Calculate overlapping lines
    overlapping_lines = baseline_lines.intersection(evaluated_lines)

    # Calculate focus percentages
    baseline_focus_percent = (
        (len(baseline_lines) / total_lines) * 100 if total_lines > 0 else 0.0
    )
    evaluated_focus_percent = (
        (len(evaluated_lines) / total_lines) * 100 if total_lines > 0 else 0.0
    )

    return {
        "overlap_percent": overlap_percent,
        "hit_percent": hit_percent,
        "baseline_lines_count": len(baseline_lines),
        "evaluated_lines_count": len(evaluated_lines),
        "overlapping_lines_count": len(overlapping_lines),
        "total_lines": total_lines,
        "baseline_focus_percent": baseline_focus_percent,
        "evaluated_focus_percent": evaluated_focus_percent,
    }


def evaluate_focus_lines_against_baseline(
    response: ModelResponse, baseline_line_ranges: List[Tuple[int, int]]
) -> Tuple[bool, str]:
    """Evaluate a model's focus_lines response against a baseline.

    Args:
        response: The model response containing focus_lines tool call
        baseline_line_ranges: The baseline line ranges to compare against

    Returns:
        Tuple of (is_correct, explanation)
        - is_correct: Always True for this evaluation (we measure metrics, not correctness)
        - explanation: Detailed explanation with metrics
    """
    # Extract focus lines and summary from response
    focus_lines_data = extract_focus_lines_with_summary_from_response(response)
    evaluated_line_ranges = focus_lines_data["line_ranges"]
    summary = focus_lines_data["summary"]

    # Convert to line sets
    baseline_lines = expand_line_ranges_to_set(baseline_line_ranges)
    evaluated_lines = expand_line_ranges_to_set(evaluated_line_ranges)

    # Calculate metrics
    overlap_percent, hit_percent = calculate_overlap_metrics(
        baseline_lines, evaluated_lines
    )

    # Create detailed explanation
    explanation = (
        f"Focus Lines Evaluation:\n"
        f"Baseline ranges: {baseline_line_ranges} (covers {len(baseline_lines)} lines)\n"
        f"Evaluated ranges: {evaluated_line_ranges} (covers {len(evaluated_lines)} lines)\n"
        f"Summary: {summary if summary else 'No summary provided'}\n"
        f"Overlap: {len(baseline_lines.intersection(evaluated_lines))} lines\n"
        f"Overlap Percent: {overlap_percent:.2f}%\n"
        f"Hit Percent: {hit_percent:.2f}%"
    )

    # Always return True since we're measuring metrics, not binary correctness
    return True, explanation


def generate_focus_lines_stats(
    response: ModelResponse, baseline_line_ranges: List[Tuple[int, int]]
) -> Dict[str, Any]:
    """Generate statistics for focus_lines evaluation.

    Args:
        response: The model response containing focus_lines tool call
        baseline_line_ranges: The baseline line ranges to compare against

    Returns:
        Dictionary with evaluation statistics including summary
    """
    # Extract focus lines and summary from response
    focus_lines_data = extract_focus_lines_with_summary_from_response(response)
    evaluated_line_ranges = focus_lines_data["line_ranges"]
    summary = focus_lines_data["summary"]

    # Convert to line sets
    baseline_lines = expand_line_ranges_to_set(baseline_line_ranges)
    evaluated_lines = expand_line_ranges_to_set(evaluated_line_ranges)

    # Calculate metrics
    overlap_percent, hit_percent = calculate_overlap_metrics(
        baseline_lines, evaluated_lines
    )

    return {
        "baseline_lines_count": len(baseline_lines),
        "evaluated_lines_count": len(evaluated_lines),
        "overlapping_lines_count": len(baseline_lines.intersection(evaluated_lines)),
        "overlap_percent": overlap_percent,
        "hit_percent": hit_percent,
        "total_unique_lines": len(baseline_lines.union(evaluated_lines)),
        "summary": summary,
        "has_summary": summary is not None,
    }


def extract_text_content(response: ModelResponse) -> str:
    """Extract text content from model response (excluding tool calls).

    Args:
        response: The model response to extract text from

    Returns:
        String containing the text content
    """
    text_content = ""
    for node in response:
        # ChatResultNodeType.RAW_RESPONSE = 0
        if node.type == 0:
            text_content += node.content
    return text_content
