import React from 'react';
import MessageContent from './MessageContent';

interface MessageProps {
  message: any;
  index: number;
}

const Message: React.FC<MessageProps> = ({ message, index }) => {
  return (
    <div className={`message message-${message.role}`}>
      <div className="message-header">
        <strong>{message.role.charAt(0).toUpperCase() + message.role.slice(1)}</strong>
        <span className="message-index">#{index + 1}</span>
      </div>
      <div className="message-body">
        <MessageContent content={message.content} />
      </div>
    </div>
  );
};

export default Message;
