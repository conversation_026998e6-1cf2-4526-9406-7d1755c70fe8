import { describe, it, expect } from 'vitest';
import * as statsUtils from '../statsUtils';
import { EvalSummary } from '../../../types';

// Get the original functions
const { extractAllStats, formatNumber } = statsUtils;

describe('statsUtils', () => {
  describe('extractAllStats', () => {
    it('should extract all stats', () => {
      const mockSummary = {
        model_config: {
          name: 'test_model',
          anthropic_model: 'test_anthropic_model',
          system_prompt: 'test_system_prompt',
          supervisor_message: 'test_supervisor_message'
        },
        dataset_path: 'test_dataset_path',
        eval_results: {},
        stats: {
          'test_avg': 10,
          'another_avg': 20.5,
          'not_an_avg_stat': 30,
          'third_avg': 40
        }
      } as EvalSummary;

      const result = extractAllStats(mockSummary);

      expect(Object.keys(result).length).toBe(4);
      expect(result).toHaveProperty('test_avg', 10);
      expect(result).toHaveProperty('another_avg', 20.5);
      expect(result).toHaveProperty('third_avg', 40);
      expect(result).toHaveProperty('not_an_avg_stat', 30);
    });

    it('should return empty object if no stats are available', () => {
      const mockSummary = {} as EvalSummary;
      const result = extractAllStats(mockSummary);
      expect(Object.keys(result).length).toBe(0);
    });

    it('should extract all stats regardless of naming', () => {
      const mockSummary = {
        model_config: {
          name: 'test_model',
          anthropic_model: 'test_anthropic_model',
          system_prompt: 'test_system_prompt',
          supervisor_message: 'test_supervisor_message'
        },
        dataset_path: 'test_dataset_path',
        eval_results: {},
        stats: {
          'not_avg': 10,
          'also_not_avg': 20
        }
      } as EvalSummary;

      const result = extractAllStats(mockSummary);

      // Check that all stats are included
      expect(Object.keys(result).length).toBe(2);
      expect(result).toHaveProperty('not_avg', 10);
      expect(result).toHaveProperty('also_not_avg', 20);
    });

    it('should handle non-number values', () => {
      const mockSummary = {
        stats: {
          'test_stat': 'not a number',
          'valid_stat': 42
        }
      } as unknown as EvalSummary;

      const result = extractAllStats(mockSummary as EvalSummary);
      expect(result).toHaveProperty('test_stat', 0);
      expect(result).toHaveProperty('valid_stat', 42);
    });
  });

  describe('formatNumber', () => {
    it('should format integers without decimal places', () => {
      expect(formatNumber(42)).toBe('42');
      expect(formatNumber(100)).toBe('100');
      expect(formatNumber(0)).toBe('0');
    });

    it('should format floats with 2 decimal places', () => {
      expect(formatNumber(3.14159)).toBe('3.14');
      expect(formatNumber(2.5)).toBe('2.50');
      expect(formatNumber(0.1)).toBe('0.10');
    });
  });
});
