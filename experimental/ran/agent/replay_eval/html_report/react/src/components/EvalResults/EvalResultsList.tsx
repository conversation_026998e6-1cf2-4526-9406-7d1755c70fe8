import { useMemo } from 'react';
import { usePassLogic } from '../../contexts/PassLogicContext';
import { EvalResult, ComparisonSummary } from '../../types/index';
import TabbedEvalResult from './TabbedEvalResult';
import './EvalResultsList.css';

interface EvalResultsListProps {
  comparisonSummary: ComparisonSummary;
  refModelName: string;
}

function EvalResultsList({ comparisonSummary, refModelName }: EvalResultsListProps) {
  const { passLogic } = usePassLogic();

  // Get reference model eval results
  const refEvalResults = useMemo(() => {
    return Object.values(comparisonSummary.ref_summary.eval_results);
  }, [comparisonSummary]);

  // Group eval results by category
  const resultsByCategory = useMemo(() => {
    const grouped: Record<string, EvalResult[]> = {};

    refEvalResults.forEach(evalResult => {
      const category = evalResult.sample.category || 'Uncategorized';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(evalResult);
    });

    return grouped;
  }, [refEvalResults]);

  // Sort categories alphabetically
  const sortedCategories = useMemo(() => {
    return Object.keys(resultsByCategory).sort();
  }, [resultsByCategory]);

  return (
    <div className="eval-results-list">
      <h2>Evaluation Results</h2>

      {sortedCategories.length === 0 ? (
        <p>No evaluation results available.</p>
      ) : (
        sortedCategories.map(category => (
          <div key={category} className="category-section">
            <h3 className="category-header">{category}</h3>
            <div className="category-results">
              {resultsByCategory[category].map((refEvalResult) => {
                // Get the corresponding eval results from all comparison models
                const modelEvalResults: Record<string, EvalResult> = {};

                // Extract model names from the comparison summary
                const modelNames = Object.keys(comparisonSummary.new_model_summaries);

                // Find matching eval results from each model
                modelNames.forEach(modelName => {
                  const modelSummary = comparisonSummary.new_model_summaries[modelName];
                  const sampleName = refEvalResult.sample.name;

                  // Find the matching eval result by sample name
                  const matchingEvalResult = Object.values(modelSummary.eval_results)
                    .find(evalResult => evalResult.sample.name === sampleName);

                  if (matchingEvalResult) {
                    modelEvalResults[modelName] = matchingEvalResult;
                  }
                });

                return (
                  <TabbedEvalResult
                    key={refEvalResult.sample.name}
                    refEvalResult={refEvalResult}
                    modelEvalResults={modelEvalResults}
                    passLogic={passLogic}
                    refModelName={refModelName}
                  />
                );
              })}
            </div>
          </div>
        ))
      )}
    </div>
  );
}

export default EvalResultsList;
