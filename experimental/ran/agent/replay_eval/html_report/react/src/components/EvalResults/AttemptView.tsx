
import { Attempt, ChatResultNodeType, ChatResultNode, StopReason } from '../../types/index';
import Details from '../common/Details';
import NodeRenderer from './NodeRenderer';
import './AttemptView.css';

interface AttemptViewProps {
  attempt: Attempt;
  index: number;
}

function AttemptView({ attempt, index }: AttemptViewProps) {
  // Render attempt data

  // Handle case where attempt might be undefined or null
  if (!attempt) {
    return <div className="attempt">Attempt data not available</div>;
  }

  // Helper function to format stop reason
  const formatStopReason = (stopReason: StopReason): string => {
    switch (stopReason) {
      case StopReason.REASON_UNSPECIFIED:
        return 'Reason Unspecified';
      case StopReason.END_TURN:
        return 'End Turn';
      case StopReason.MAX_TOKENS:
        return 'Max Tokens';
      case StopReason.TOOL_USE_REQUESTED:
        return 'Tool Use Requested';
      default:
        return 'Unknown';
    }
  };

  // Check if response only contains a single node of type FINAL_PARAMETERS
  const onlyFinalParams = attempt.response &&
    attempt.response.length === 1 &&
    attempt.response[0].type === ChatResultNodeType.FINAL_PARAMETERS;

  // Create a fake empty response node if needed
  const emptyResponseNode: ChatResultNode = {
    id: 999999, // Use a high number to avoid conflicts
    type: ChatResultNodeType.RAW_RESPONSE,
    content: "EMPTY RESPONSE",
    className: "empty-response-node" // Add a custom property for styling
  };

  // Combine the response nodes, adding the empty response node if needed
  const responseNodes = onlyFinalParams
    ? [emptyResponseNode, ...(attempt.response || [])]
    : attempt.response || [];
  return (
    <div className="attempt">
      <div className="attempt-header">
        <h5>Attempt {index + 1}</h5>
        <span className={attempt.is_correct ? 'correct' : 'incorrect'}>
          {attempt.is_correct ? 'CORRECT' : 'INCORRECT'}
        </span>
      </div>

      {attempt.explanation && (
        <Details summary="Explanation" defaultOpen={true}>
          <div className="attempt-explanation">
            <pre>{attempt.explanation}</pre>
          </div>
        </Details>
      )}

      <Details summary="Response" defaultOpen={false}>
        <div className="attempt-content">
          {responseNodes.length > 0 ? (
            <div className="response-nodes">
              {responseNodes.map((node, nodeIndex) => (
                <NodeRenderer key={nodeIndex} node={node} />
              ))}
            </div>
          ) : (
            <div className="no-response">No response data available</div>
          )}
        </div>
      </Details>

      {attempt.end_of_stream && (
        <Details summary="End of Stream Info" defaultOpen={false}>
          <div className="end-of-stream-info">
            <div className="end-of-stream-field">
              <strong>Stop Reason:</strong> {formatStopReason(attempt.end_of_stream.stop_reason)}
            </div>
            {attempt.end_of_stream.output_tokens !== null && attempt.end_of_stream.output_tokens !== undefined && (
              <div className="end-of-stream-field">
                <strong>Output Tokens:</strong> {attempt.end_of_stream.output_tokens}
              </div>
            )}
            {attempt.end_of_stream.prompt_cache_usage && (
              <div className="prompt-cache-usage">
                <strong>Prompt Cache Usage:</strong>
                <div className="cache-usage-details">
                  <div className="cache-field">
                    <span>Input Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.input_tokens}
                  </div>
                  <div className="cache-field">
                    <span>Cache Creation Input Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.cache_creation_input_tokens}
                  </div>
                  <div className="cache-field">
                    <span>Cache Read Input Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.cache_read_input_tokens}
                  </div>
                  {attempt.end_of_stream.prompt_cache_usage.text_input_tokens !== undefined && (
                    <div className="cache-field">
                      <span>Text Input Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.text_input_tokens}
                    </div>
                  )}
                  {attempt.end_of_stream.prompt_cache_usage.tool_input_tokens !== undefined && (
                    <div className="cache-field">
                      <span>Tool Input Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.tool_input_tokens}
                    </div>
                  )}
                  {attempt.end_of_stream.prompt_cache_usage.text_output_tokens !== undefined && (
                    <div className="cache-field">
                      <span>Text Output Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.text_output_tokens}
                    </div>
                  )}
                  {attempt.end_of_stream.prompt_cache_usage.tool_output_tokens !== undefined && (
                    <div className="cache-field">
                      <span>Tool Output Tokens:</span> {attempt.end_of_stream.prompt_cache_usage.tool_output_tokens}
                    </div>
                  )}
                  {attempt.end_of_stream.prompt_cache_usage.model_caller && (
                    <div className="cache-field">
                      <span>Model Caller:</span> {attempt.end_of_stream.prompt_cache_usage.model_caller}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </Details>
      )}

      {/* Tool uses and response nodes could be added here if needed */}
    </div>
  );
}

export default AttemptView;
