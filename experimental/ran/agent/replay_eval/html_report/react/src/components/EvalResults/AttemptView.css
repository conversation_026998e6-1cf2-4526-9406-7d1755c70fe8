.attempt {
  margin-left: 20px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border-left: 3px solid #ddd;
}

.attempt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.attempt-header h5 {
  margin: 0;
}

.attempt-content, .attempt-explanation {
  margin-top: 10px;
}

.attempt-content pre, .attempt-explanation pre {
  white-space: pre-wrap;
  word-break: break-word;
  background-color: #f0f0f0;
  padding: 10px;
  border-radius: 5px;
  margin: 0;
}

.attempt-explanation pre {
  background-color: #f5f5f5;
  border-left: 3px solid #4caf50; /* Green border for explanation */
  font-size: 1rem;
  line-height: 1.5;
}

.tool-uses, .response-nodes {
  margin-top: 10px;
}

/* Node styles */
.node {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 5px;
  background-color: #f5f5f5;
}

.node.raw-response {
  background-color: #f0f0f0;
}

.node.raw-response pre {
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}

/* Style for the empty response message */
.node.raw-response.empty-response-node {
  background-color: #fff3cd;
  border-left: 3px solid #ffc107;
  color: #856404;
  font-weight: bold;
  text-align: center;
  padding: 12px;
  margin-bottom: 15px;
}

.node.tool-use {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.tool-use-header {
  margin-bottom: 5px;
  font-weight: 500;
}

.tool-use-input, .final-parameters-content {
  background-color: #272822; /* Monokai background */
  padding: 8px;
  border-radius: 4px;
  margin: 0;
  overflow: auto;
  max-height: 400px;
}

.final-parameters-wrapper {
  margin-bottom: 10px;
}

.final-parameters-summary {
  cursor: pointer;
  font-weight: 600;
  color: #4169e1;
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #4169e1;
  margin-bottom: 8px;
  transition: background-color 0.2s;
}

.final-parameters-summary:hover {
  background-color: #e6f0ff;
}

.node.final-parameters {
  background-color: #f0f8ff; /* Light blue background */
  border-left: 3px solid #4169e1; /* Royal blue border */
  margin-top: 8px;
}

.final-parameters-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
}

.fullscreen-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #4169e1;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.fullscreen-button:hover {
  background-color: rgba(65, 105, 225, 0.1);
}

.fullscreen-button svg {
  display: block;
}

.final-parameters-content {
  background-color: #fff;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.param-item {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 10px;
  border-left: 3px solid #4169e1;
}

.param-item.full-width {
  grid-column: 1 / -1;
}

.param-label {
  font-weight: 600;
  color: #4169e1;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.param-value {
  font-family: 'Courier New', monospace;
  word-break: break-word;
}

.system-prompt-details {
  width: 100%;
}

.system-prompt-details summary {
  cursor: pointer;
  padding: 5px 0;
  font-weight: 600;
  color: #4169e1;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.system-prompt-details summary:hover {
  text-decoration: underline;
}

.param-value.system-prompt {
  white-space: pre-wrap;
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  margin-top: 8px;
}

.tool-choice-object {
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
}

.messages-section {
  margin-top: 20px;
  margin-bottom: 20px;
}

.messages-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #4169e1;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 5px;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message {
  border-radius: 6px;
  overflow: hidden;
}

.message-user {
  background-color: #f0f0f0;
  border-left: 3px solid #666;
}

.message-assistant {
  background-color: #f0f8ff;
  border-left: 3px solid #4169e1;
}

.message-header {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-index {
  font-size: 0.8rem;
  color: #666;
}

.message-body {
  padding: 10px 12px;
}

.message-content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.message-content-array {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message-content-item {
  border-top: 1px dashed #e0e0e0;
  padding-top: 10px;
}

.message-content-item:first-child {
  border-top: none;
  padding-top: 0;
}

.message-content-type {
  font-weight: 600;
  color: #4169e1;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.message-content-tool {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  margin-top: 5px;
}

.message-content-tool-input {
  margin-top: 8px;
}

.message-content-tool-result {
  background-color: #f0f8ff;
  padding: 8px;
  border-radius: 4px;
  margin-top: 5px;
  border-left: 3px solid #4169e1;
}

.tool-result-details {
  width: 100%;
}

.tool-result-summary {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  font-weight: 500;
}

.tool-result-summary:hover {
  text-decoration: underline;
}

.tool-use-id {
  font-size: 0.85rem;
  color: #666;
  margin-left: 10px;
}

.tool-result-content {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

.tool-result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 0.9rem;
}

.raw-json-details {
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding-top: 10px;
}

.raw-json-details summary {
  cursor: pointer;
  color: #4169e1;
  font-weight: 500;
  margin-bottom: 10px;
}

/* Modal specific styles */
.final-parameters-modal-content {
  padding: 10px;
}

.modal-json-view {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}

.modal-json-view h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #4169e1;
}

.correct {
  color: green;
  font-weight: bold;
}

.incorrect {
  color: red;
  font-weight: bold;
}

.no-response {
  color: #888;
  font-style: italic;
  padding: 10px;
}

/* End of Stream Info styles */
.end-of-stream-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border-left: 3px solid #6c757d;
}

.end-of-stream-field {
  margin-bottom: 8px;
  padding: 5px 0;
}

.end-of-stream-field strong {
  color: #495057;
  margin-right: 8px;
}

.prompt-cache-usage {
  margin-top: 12px;
  padding: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.prompt-cache-usage strong {
  color: #007bff;
  display: block;
  margin-bottom: 8px;
}

.cache-usage-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.cache-field {
  background-color: #f8f9fa;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 0.9rem;
  border-left: 2px solid #007bff;
}

.cache-field span {
  font-weight: 500;
  color: #495057;
  margin-right: 6px;
}
