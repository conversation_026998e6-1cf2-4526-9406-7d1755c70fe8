import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Details from '../Details'

describe('Details', () => {
  it('renders with summary and content', () => {
    render(
      <Details summary="Test Summary">
        <p>Test Content</p>
      </Details>
    )

    expect(screen.getByText('Test Summary')).toBeInTheDocument()
    // Content should not be visible initially when defaultOpen is false
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument()
  })

  it('renders with content visible when defaultOpen is true', () => {
    render(
      <Details summary="Test Summary" defaultOpen={true}>
        <p>Test Content</p>
      </Details>
    )

    expect(screen.getByText('Test Summary')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('toggles content visibility when clicked', async () => {
    render(
      <Details summary="Test Summary">
        <p>Test Content</p>
      </Details>
    )

    // Content should not be visible initially
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument()

    // Click to open
    await userEvent.click(screen.getByText('Test Summary'))

    // Content should now be visible
    expect(screen.getByText('Test Content')).toBeInTheDocument()

    // Click to close
    await userEvent.click(screen.getByText('Test Summary'))

    // Content should not be visible again
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument()
  })

  it('applies additional className', () => {
    render(
      <Details summary="Test Summary" className="custom-class">
        <p>Test Content</p>
      </Details>
    )

    const detailsContainer = screen.getByText('Test Summary').closest('.details-container')
    expect(detailsContainer).toHaveClass('custom-class')
  })
})
