.stats-table-container {
  margin-bottom: 20px;
}

.stats-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
}

/* Override any green/red coloring for the stats table */
.stats-table td {
  color: inherit !important;
  font-weight: normal !important;
}

.stats-table th,
.stats-table td {
  padding: 8px;
  text-align: left;
  border: 1px solid #ddd;
}

.stats-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.stats-table tr.reference {
  background-color: #f8f8f8;
}

.stats-table tr:hover {
  background-color: #f5f5f5;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-name {
  font-weight: bold;
}

.stat-value {
  text-align: right;
}

.model-stats {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.model-stats h6 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
}
