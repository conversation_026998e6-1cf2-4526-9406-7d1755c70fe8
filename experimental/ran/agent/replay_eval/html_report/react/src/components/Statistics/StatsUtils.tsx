import { useMemo } from 'react';
import { Comparison<PERSON><PERSON>mary, <PERSON>lR<PERSON>ult, EvalSummary, PassLogicType } from '../../types';
import { calculateSampleStatistics, calculateSuccessRate, formatSuccessRate } from '../../utils/statistics';
import AllStats from './AllStats';
import { calculateModelCategoryStats, extractAllStats } from './statsUtils';

/**
 * Calculate reference model statistics from eval results
 */
export function useReferenceStats(
  evalResults: EvalResult[],
  passLogic: PassLogicType
) {
  // Calculate statistics for each sample
  const sampleStats = useMemo(() => {
    return evalResults.map(evalResult => calculateSampleStatistics(evalResult, passLogic));
  }, [evalResults, passLogic]);

  // Calculate overall statistics
  const stats = useMemo(() => {
    if (sampleStats.length === 0) return { totalSamples: 0, correctSamples: 0, totalAttempts: 0, totalCorrectAttempts: 0 };

    let totalSamples = sampleStats.length;
    let correctSamples = 0;
    let totalAttempts = 0;
    let totalCorrectAttempts = 0;

    sampleStats.forEach(stats => {
      if (stats.passes) correctSamples++;
      totalAttempts += stats.totalAttempts;
      totalCorrectAttempts += stats.correctAttempts;
    });

    return { totalSamples, correctSamples, totalAttempts, totalCorrectAttempts };
  }, [sampleStats]);

  // Calculate success rate
  const successRate = useMemo(() => {
    return calculateSuccessRate({
      totalSamples: stats.totalSamples,
      correctSamples: stats.correctSamples,
      totalAttempts: stats.totalAttempts,
      totalCorrectAttempts: stats.totalCorrectAttempts
    }, passLogic);
  }, [stats, passLogic]);

  // Format success rate for display
  const formattedSuccessRate = useMemo(() => {
    return formatSuccessRate(successRate);
  }, [successRate]);

  return {
    stats,
    successRate,
    formattedSuccessRate
  };
}

/**
 * Calculate comparison model statistics
 */
export function useComparisonModelStats(
  comparisonSummary: ComparisonSummary,
  comparisonModelNames: string[],
  evalResults: EvalResult[],
  passLogic: PassLogicType,
  category?: string
) {
  return useMemo(() => {
    return comparisonModelNames.map(modelName => {
      const modelEvalResults = Object.values(comparisonSummary.new_model_summaries[modelName]?.eval_results || {});

      return calculateModelCategoryStats(
        modelName,
        modelEvalResults,
        evalResults,
        passLogic,
        category
      );
    });
  }, [comparisonSummary, comparisonModelNames, evalResults, passLogic, category]);
}

/**
 * Extract and process all statistics
 */
export function useAllStats(
  refSummary: EvalSummary,
  refModelName: string,
  comparisonSummary: ComparisonSummary,
  comparisonModelNames: string[],
  category?: string
) {
  // Extract all statistics from the reference model
  const refAllStats = useMemo(() => {
    const allStats = extractAllStats(refSummary);

    if (!category) return allStats;

    // Filter stats to only include those for this category
    const categoryStats: Record<string, number> = {};
    const categoryPrefix = category.toLowerCase().replace(/ /g, '_') + '_';

    Object.entries(allStats).forEach(([key, value]) => {
      if (key.startsWith(categoryPrefix)) {
        // Remove the category prefix for display
        const displayKey = key.replace(categoryPrefix, '');
        categoryStats[displayKey] = value;
      }
    });

    return categoryStats;
  }, [refSummary, category]);

  // Extract all statistics from comparison models
  const modelAllStats = useMemo(() => {
    return comparisonModelNames.map(modelName => {
      const modelSummary = comparisonSummary.new_model_summaries[modelName];
      if (!modelSummary) return { modelName, stats: {} };

      const allStats = extractAllStats(modelSummary);

      if (!category) return { modelName, stats: allStats };

      // Filter stats to only include those for this category
      const categoryStats: Record<string, number> = {};
      const categoryPrefix = category.toLowerCase().replace(/ /g, '_') + '_';

      Object.entries(allStats).forEach(([key, value]) => {
        if (key.startsWith(categoryPrefix)) {
          // Remove the category prefix for display
          const displayKey = key.replace(categoryPrefix, '');
          categoryStats[displayKey] = value;
        }
      });

      return {
        modelName,
        stats: categoryStats
      };
    });
  }, [comparisonSummary.new_model_summaries, comparisonModelNames, category]);

  // Check if there are any stats
  const hasStats = useMemo(() => {
    return Object.keys(refAllStats).length > 0 ||
      modelAllStats.some(model => Object.keys(model.stats).length > 0);
  }, [refAllStats, modelAllStats]);

  // Render stats content
  const statsContent = useMemo(() => {
    if (!hasStats) return null;

    return (
      <>
        <div className="model-stats">
          <h6>{refModelName} (Reference)</h6>
          <AllStats stats={refAllStats} />
        </div>

        {modelAllStats.map(model => (
          Object.keys(model.stats).length > 0 && (
            <div key={model.modelName} className="model-stats">
              <h6>{model.modelName}</h6>
              <AllStats stats={model.stats} />
            </div>
          )
        ))}
      </>
    );
  }, [hasStats, refModelName, refAllStats, modelAllStats]);

  return {
    refAllStats,
    modelAllStats,
    hasStats,
    statsContent
  };
}
