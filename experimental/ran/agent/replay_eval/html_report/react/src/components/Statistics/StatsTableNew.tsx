import React, { useMemo } from 'react';
import { ComparisonSummary, PassLogicType } from '../../types';
import './StatsTableNew.css';

interface StatsTableNewProps {
  title?: string;
  comparisonSummary: ComparisonSummary;
  refModelName: string;
  comparisonModelNames: string[];
  passLogic?: PassLogicType; // Make passLogic optional
}

const StatsTableNew: React.FC<StatsTableNewProps> = ({
  title,
  comparisonSummary,
  refModelName,
  comparisonModelNames
}) => {
  // Get all stats from reference model
  const refStats = useMemo(() => {
    return comparisonSummary.ref_summary.stats || {};
  }, [comparisonSummary.ref_summary]);

  // Get all stats from comparison models
  const modelStats = useMemo(() => {
    const result: Record<string, Record<string, number>> = {};

    comparisonModelNames.forEach(modelName => {
      const modelSummary = comparisonSummary.new_model_summaries[modelName];
      if (modelSummary && modelSummary.stats) {
        result[modelName] = modelSummary.stats;
      } else {
        result[modelName] = {};
      }
    });

    return result;
  }, [comparisonSummary.new_model_summaries, comparisonModelNames]);

  // Collect all unique stat keys
  const allStatKeys = useMemo(() => {
    const keys = new Set<string>();

    // Add keys from reference model
    Object.keys(refStats).forEach(key => keys.add(key));

    // Add keys from all comparison models
    comparisonModelNames.forEach(modelName => {
      Object.keys(modelStats[modelName] || {}).forEach(key => keys.add(key));
    });

    // Convert to array and sort alphabetically
    return Array.from(keys).sort();
  }, [refStats, modelStats, comparisonModelNames]);

  // Format a stat key for display
  const formatStatKey = (key: string) => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Format a stat value
  const formatStatValue = (value: number | undefined) => {
    if (value === undefined) return '-';

    // Format as percentage if between 0 and 1
    if (value >= 0 && value <= 1) {
      return `${(value * 100).toFixed(2)}%`;
    }

    // Format with 2 decimal places if it has a decimal part
    if (Math.floor(value) !== value) {
      return value.toFixed(2);
    }

    // Otherwise just return as is
    return value.toString();
  };

  if (allStatKeys.length === 0) {
    return <p>No statistics available.</p>;
  }

  return (
    <div className={title ? 'stats-table-container' : ''} data-category={title}>
      {title && <h4>{title}</h4>}
      <table className="stats-table">
        <thead>
          <tr>
            <th>Model</th>
            {allStatKeys.map(key => (
              <th key={key}>{formatStatKey(key)}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {/* Reference model row */}
          <tr className="reference" data-model={refModelName}>
            <td>{refModelName}</td>
            {allStatKeys.map(key => (
              <td key={key}>{formatStatValue(refStats[key])}</td>
            ))}
          </tr>

          {/* Comparison model rows */}
          {comparisonModelNames.map(modelName => (
            <tr key={modelName} data-model={modelName}>
              <td>{modelName}</td>
              {allStatKeys.map(key => (
                <td key={key}>{formatStatValue(modelStats[modelName]?.[key])}</td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default StatsTableNew;
