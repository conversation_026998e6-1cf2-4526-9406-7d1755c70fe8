import React from 'react';
import { ChatResultNode, ChatResultNodeType } from '../../types';
import RawResponseNode from './RawResponseNode';
import ToolUseNode from './ToolUseNode';
import FinalParametersNode from './FinalParametersNode';

interface NodeRendererProps {
  node: ChatResultNode;
}

const NodeRenderer: React.FC<NodeRendererProps> = ({ node }) => {
  switch (node.type) {
    case ChatResultNodeType.RAW_RESPONSE:
      return <RawResponseNode node={node} />;
    case ChatResultNodeType.TOOL_USE:
    case ChatResultNodeType.TOOL_USE_START:
      return <ToolUseNode node={node} />;
    case ChatResultNodeType.FINAL_PARAMETERS:
      return (
        <details className="final-parameters-wrapper">
          <summary className="final-parameters-summary">Final Parameters</summary>
          <FinalParametersNode node={node} />
        </details>
      );
    default:
      // For other node types, just render the content
      return node.content ? <div className="node">{node.content}</div> : null;
  }
};

export default NodeRenderer;
