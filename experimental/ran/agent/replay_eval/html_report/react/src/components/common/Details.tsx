import { ReactNode, useState } from 'react';
import './Details.css';

interface DetailsProps {
  summary: ReactNode;
  children: ReactNode;
  defaultOpen?: boolean;
  className?: string;
}

function Details({ summary, children, defaultOpen = false, className = '' }: DetailsProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`details-container ${className} ${isOpen ? 'open' : ''}`}>
      <div className="details-summary" onClick={toggleOpen}>
        <span className="details-arrow">{isOpen ? '▼' : '▶'}</span>
        <span className="details-summary-content">{summary}</span>
      </div>
      {isOpen && <div className="details-content">{children}</div>}
    </div>
  );
}

export default Details;
