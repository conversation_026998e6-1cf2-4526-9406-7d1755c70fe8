.tabs-container {
  width: 100%;
  margin-bottom: 1rem;
}

.tabs-header {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #dee2e6;
}

.tab-button {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  margin-bottom: -1px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.tab-button:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.tab-button.active {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
  display: none;
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-top: none;
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.tab-content.active {
  display: block;
}

/* Variant styles for tabs */
.tab-button.improvement {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
}

.tab-button.improvement.active {
  background-color: var(--color-success-light);
  border-color: var(--color-success) var(--color-success) var(--color-success-light);
}

.tab-button.regression {
  background-color: var(--color-danger-light);
  border-color: var(--color-danger);
}

.tab-button.regression.active {
  background-color: var(--color-danger-light);
  border-color: var(--color-danger) var(--color-danger) var(--color-danger-light);
}

.tab-button.neutral {
  background-color: var(--color-gray-light);
  border-color: var(--color-gray);
}

.tab-button.neutral.active {
  background-color: var(--color-gray-light);
  border-color: var(--color-gray) var(--color-gray) var(--color-gray-light);
}
