import React, { useMemo } from 'react';
import { ComparisonSummary, PassLogicType } from '../../types';
import StatsTable from './StatsTable';
import StatsTableNew from './StatsTableNew';
import ModelStatsRow from './ModelStatsRow';
import { useReferenceStats, useComparisonModelStats } from './StatsUtils';

interface OverallStatsSectionProps {
  comparisonSummary: ComparisonSummary;
  refModelName: string;
  comparisonModelNames: string[];
  passLogic: PassLogicType;
}

const OverallStatsSection: React.FC<OverallStatsSectionProps> = ({
  comparisonSummary,
  refModelName,
  comparisonModelNames,
  passLogic
}) => {
  // Get the eval results from the reference summary
  const evalResults = useMemo(() =>
    Object.values(comparisonSummary.ref_summary.eval_results),
    [comparisonSummary]
  );

  // Calculate reference model statistics
  const { stats: overallStats, formattedSuccessRate } = useReferenceStats(evalResults, passLogic);

  // Calculate comparison model statistics
  const modelStats = useComparisonModelStats(
    comparisonSummary,
    comparisonModelNames,
    evalResults,
    passLogic
  );

  // We don't need to extract stats here as we're using StatsTableNew directly

  return (
    <div className="overall-stats">
      <h3>Overall Success Rates</h3>
      <StatsTable
        passLogic={passLogic}
        averageStats={undefined}
      >
        {/* Reference model row */}
        <ModelStatsRow
          modelName={refModelName}
          successRate={formattedSuccessRate}
          correct={passLogic === 'aggregate' ? overallStats.totalCorrectAttempts : overallStats.correctSamples}
          total={passLogic === 'aggregate' ? overallStats.totalAttempts : overallStats.totalSamples}
          isReference={true}
        />

        {/* Comparison models rows */}
        {modelStats.map(modelStat => (
          <ModelStatsRow
            key={modelStat.modelName}
            modelName={modelStat.modelName}
            successRate={modelStat.formattedSuccessRate}
            correct={passLogic === 'aggregate' ? modelStat.stats.totalCorrectAttempts : modelStat.stats.correct}
            total={passLogic === 'aggregate' ? modelStat.stats.totalAttempts : modelStat.stats.total}
            improvements={modelStat.improvements}
            regressions={modelStat.regressions}
          />
        ))}
      </StatsTable>

      {/* New stats table with all statistics */}
      <h3>All Statistics</h3>
      <StatsTableNew
        comparisonSummary={comparisonSummary}
        refModelName={refModelName}
        comparisonModelNames={comparisonModelNames}
        passLogic={passLogic}
      />
    </div>
  );
};

export default OverallStatsSection;
