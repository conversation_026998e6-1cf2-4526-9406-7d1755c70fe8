/* Common styles for all message types */
.message-section,
.system-prompt-section,
.supervisor-message-section {
  margin-top: 20px;
  margin-bottom: 30px;
}

.message-details,
.system-prompt-details,
.supervisor-message-details {
  background-color: #f8f8f8;
  border-radius: 5px;
}

.collapsible-header {
  margin: 0;
  padding: 8px 0;
  display: inline-block;
}

.message-content,
.system-prompt-content,
.supervisor-message-content {
  padding: 15px;
  border-radius: 0 0 5px 5px;
}

.message-content pre,
.system-prompt-content pre,
.supervisor-message-content pre {
  max-height: 500px;
  overflow-y: auto;
  margin: 0;
}

/* Diff styles */
.diff-container {
  display: flex;
  flex-direction: column;
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.show-full-button {
  padding: 4px 8px;
  background-color: #e0e0e0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.show-full-button:hover {
  background-color: #d0d0d0;
}

/* Full content view */
.diff-container {
  position: relative;
  transition: all 0.3s ease;
}

/* Reference content styles */
.reference {
  border-left: 3px solid #4a6da7;
  background-color: #f8f9fa;
}
