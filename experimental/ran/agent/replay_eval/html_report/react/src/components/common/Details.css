.details-container {
  margin-bottom: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.details-summary {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
}

.details-summary:hover {
  background-color: #e9ecef;
}

.details-arrow {
  margin-right: 0.5rem;
  font-size: 0.75rem;
  transition: transform 0.2s ease-in-out;
}

.details-container.open .details-arrow {
  transform: rotate(0deg);
}

.details-summary-content {
  flex: 1;
}

.details-content {
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #dee2e6;
}
