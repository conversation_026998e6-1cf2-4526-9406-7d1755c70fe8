import React from 'react';

interface StatsTableProps {
  title?: string;
  children: React.ReactNode;
  passLogic?: string;
  averageStats?: React.ReactNode;
}

const StatsTable: React.FC<StatsTableProps> = ({ title, children, passLogic = 'at_least_one', averageStats }) => {
  return (
    <div className={title ? 'category-table' : ''} data-category={title}>
      {title && <h4>{title}</h4>}
      <table>
        <thead>
          <tr>
            <th>Model</th>
            <th>Success Rate</th>
            <th>{passLogic === 'aggregate' ? 'Correct Attempts' : 'Correct Samples'}</th>
            <th>{passLogic === 'aggregate' ? 'Total Attempts' : 'Total Samples'}</th>
            <th>Improvements</th>
            <th>Regressions</th>
          </tr>
        </thead>
        <tbody>
          {children}
        </tbody>
      </table>

      {averageStats && (
        <div className="average-stats-section">
          <h5>Average Statistics</h5>
          <div className="average-stats-content">
            {averageStats}
          </div>
        </div>
      )}
    </div>
  );
};

export default StatsTable;
