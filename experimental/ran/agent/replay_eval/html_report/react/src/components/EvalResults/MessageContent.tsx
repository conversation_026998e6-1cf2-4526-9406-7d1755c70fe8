import React from 'react';
import <PERSON>act<PERSON><PERSON> from 'react-json-view';

interface MessageContentProps {
  content: any;
}

const MessageContent: React.FC<MessageContentProps> = ({ content }) => {
  if (typeof content === 'string') {
    return <div className="message-content-text">{content}</div>;
  }

  if (Array.isArray(content)) {
    return (
      <div className="message-content-array">
        {content.map((item, index) => (
          <div key={index} className="message-content-item">
            {item.type && <div className="message-content-type">{item.type}</div>}
            {item.text && <div className="message-content-text">{item.text}</div>}
            {item.type === 'tool_use' && (
              <div className="message-content-tool">
                <div><strong>Tool:</strong> {item.name}</div>
                {item.id && <div><strong>ID:</strong> {item.id}</div>}
                {item.input && (
                  <div className="message-content-tool-input">
                    <strong>Input:</strong>
                    <ReactJson
                      src={item.input}
                      theme="monokai"
                      name={false}
                      displayDataTypes={false}
                      collapsed={2}
                      enableClipboard={false}
                    />
                  </div>
                )}
              </div>
            )}
            {item.type === 'tool_result' && (
              <div className="message-content-tool-result">
                <details className="tool-result-details">
                  <summary className="tool-result-summary">
                    <strong>Tool Result</strong>
                    {item.tool_use_id && <span className="tool-use-id">ID: {item.tool_use_id}</span>}
                  </summary>
                  {item.content && (
                    <div className="tool-result-content">
                      {typeof item.content === 'string' ? (
                        <pre>{item.content}</pre>
                      ) : (
                        <ReactJson
                          src={item.content}
                          theme="monokai"
                          name={false}
                          displayDataTypes={false}
                          collapsed={2}
                          enableClipboard={false}
                        />
                      )}
                    </div>
                  )}
                </details>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  return <pre>{JSON.stringify(content, null, 2)}</pre>;
};

export default MessageContent;
