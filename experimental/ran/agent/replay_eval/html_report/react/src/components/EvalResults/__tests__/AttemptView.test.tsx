import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import AttemptView from '../AttemptView';
import { Attempt, StopReason, ChatResultNodeType } from '../../../types/index';

describe('AttemptView', () => {
  const mockAttempt: Attempt = {
    response: [
      {
        id: 1,
        type: ChatResultNodeType.RAW_RESPONSE,
        content: 'Test response content'
      }
    ],
    is_correct: true,
    explanation: 'Test explanation',
    stats: { test_stat: 42 }
  };

  it('renders attempt without end_of_stream', () => {
    render(<AttemptView attempt={mockAttempt} index={0} />);

    expect(screen.getByText('Attempt 1')).toBeInTheDocument();
    expect(screen.getByText('CORRECT')).toBeInTheDocument();
    expect(screen.getByText('Test explanation')).toBeInTheDocument();

    // Should not show end of stream section
    expect(screen.queryByText('End of Stream Info')).not.toBeInTheDocument();
  });

  it('renders attempt with end_of_stream information', () => {
    const attemptWithEndOfStream: Attempt = {
      ...mockAttempt,
      end_of_stream: {
        stop_reason: StopReason.END_TURN,
        output_tokens: 150,
        prompt_cache_usage: {
          input_tokens: 100,
          cache_creation_input_tokens: 50,
          cache_read_input_tokens: 25,
          text_input_tokens: 80,
          tool_input_tokens: 20,
          text_output_tokens: 140,
          tool_output_tokens: 10,
          model_caller: 'test_caller'
        }
      }
    };

    render(<AttemptView attempt={attemptWithEndOfStream} index={0} />);

    expect(screen.getByText('Attempt 1')).toBeInTheDocument();
    expect(screen.getByText('CORRECT')).toBeInTheDocument();

    // Should show end of stream section
    expect(screen.getByText('End of Stream Info')).toBeInTheDocument();
  });

  it('renders attempt with minimal end_of_stream information', () => {
    const attemptWithMinimalEndOfStream: Attempt = {
      ...mockAttempt,
      end_of_stream: {
        stop_reason: StopReason.MAX_TOKENS
      }
    };

    render(<AttemptView attempt={attemptWithMinimalEndOfStream} index={1} />);

    expect(screen.getByText('Attempt 2')).toBeInTheDocument();
    expect(screen.getByText('End of Stream Info')).toBeInTheDocument();
  });

  it('handles incorrect attempt', () => {
    const incorrectAttempt: Attempt = {
      ...mockAttempt,
      is_correct: false
    };

    render(<AttemptView attempt={incorrectAttempt} index={0} />);

    expect(screen.getByText('INCORRECT')).toBeInTheDocument();
  });

  it('handles null attempt', () => {
    render(<AttemptView attempt={null as any} index={0} />);

    expect(screen.getByText('Attempt data not available')).toBeInTheDocument();
  });
});
