import { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { PassLogicType } from '../types';

interface PassLogicContextType {
  passLogic: PassLogicType;
  setPassLogic: (passLogic: PassLogicType) => void;
  passLogicDescription: string;
}

const PassLogicContext = createContext<PassLogicContextType | undefined>(undefined);

interface PassLogicProviderProps {
  children: ReactNode;
}

export function PassLogicProvider({ children }: PassLogicProviderProps) {
  const [passLogic, setPassLogic] = useState<PassLogicType>('at_least_one');

  // Load saved pass logic from localStorage on initial render
  useEffect(() => {
    const savedPassLogic = localStorage.getItem('passLogic') as PassLogicType | null;
    if (savedPassLogic) {
      setPassLogic(savedPassLogic);
    }
  }, []);

  // Save pass logic to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('passLogic', passLogic);
  }, [passLogic]);

  // Get the description for the current pass logic
  const getPassLogicDescription = (logic: PassLogicType): string => {
    switch (logic) {
      case 'at_least_one':
        return 'Current: At Least One Attempt - A sample passes if at least one attempt is correct.';
      case 'majority':
        return 'Current: Majority of Attempts - A sample passes if more than half of its attempts are correct.';
      case 'all':
        return 'Current: All Attempts - A sample passes only if all attempts are correct.';
      case 'aggregate':
        return 'Current: Aggregate All Attempts - Success rate is calculated by dividing total correct attempts by total attempts across all samples.';
      default:
        return '';
    }
  };

  const passLogicDescription = getPassLogicDescription(passLogic);

  return (
    <PassLogicContext.Provider value={{ passLogic, setPassLogic, passLogicDescription }}>
      {children}
    </PassLogicContext.Provider>
  );
}

export function usePassLogic() {
  const context = useContext(PassLogicContext);
  if (context === undefined) {
    throw new Error('usePassLogic must be used within a PassLogicProvider');
  }
  return context;
}
