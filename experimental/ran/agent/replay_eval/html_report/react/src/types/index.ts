// Types for the report data

export type PassLogicType = 'at_least_one' | 'majority' | 'all' | 'aggregate';

// Define StopReason enum to match Python IntEnum
export enum StopReason {
  REASON_UNSPECIFIED = 0,
  END_TURN = 1,
  MAX_TOKENS = 2,
  TOOL_USE_REQUESTED = 3
}

// Define PromptCacheUsage interface to match Python dataclass
export interface PromptCacheUsage {
  input_tokens: number;
  cache_creation_input_tokens: number;
  cache_read_input_tokens: number;
  text_input_tokens?: number;
  tool_input_tokens?: number;
  text_output_tokens?: number;
  tool_output_tokens?: number;
  model_caller?: string | null;
}

// Define EndOfStream interface to match Python dataclass
export interface EndOfStream {
  stop_reason: StopReason;
  output_tokens?: number | null;
  prompt_cache_usage?: PromptCacheUsage | null;
}

// Types that match the Python dataclass structure

// Define ChatResultNodeType enum to match Python IntEnum
export enum ChatResultNodeType {
  RAW_RESPONSE = 0,        // The raw response from the model
  SUGGESTED_QUESTIONS = 1, // Our guess of what user will ask next
  MAIN_TEXT_FINISHED = 2,  // Indication that streaming of the main response finished
  WORKSPACE_FILE_CHUNKS = 3, // Workspace file chunks used in prompt
  RELEVANT_SOURCES = 4,    // Sources that were useful to generate the response
  TOOL_USE = 5,            // Tool use requested by the AI model
  FINAL_PARAMETERS = 6,    // Final parameters used for the request
  TOOL_USE_START = 7       // Tool use requested by the AI model
}

// Define ChatResultToolUse interface
export interface ChatResultToolUse {
  name: string;
  input: Record<string, any>;
  tool_use_id: string;
}

export interface ChatResultNode {
  // Matches ChatResultNode from base.prompt_format.common
  id: number;             // Unique id of the node
  type: ChatResultNodeType; // Type of the node
  content: string;        // Content of the node
  tool_use?: ChatResultToolUse | null; // Additional content for tool use nodes
  final_parameters?: Record<string, any> | null; // Final parameters used for the request
  className?: string;    // Optional class name for styling (UI only)
}

// ModelResponse is defined as list[ChatResultNode] in Python
export type ModelResponse = ChatResultNode[];

export interface Attempt {
  // Matches Attempt from eval_output.py
  response: ModelResponse | null;
  is_correct: boolean;
  explanation: string;
  stats: Record<string, number>;
  end_of_stream?: EndOfStream | null;
}

export type ToolChoice = 'auto' | 'none' | 'required' | { type: string; function: { name: string } };

export interface EvalSample {
  // Simplified representation of EvalSample from eval_sample.py
  request_id: string;
  name: string;
  category?: string;
  assistant_message_prefill?: string;
  assistant_message_prefill_from_response?: boolean;
  linear_url?: string | null;
  tool_choice?: ToolChoice | null;
}

export interface ToolDefinition {
  // Simplified representation of ToolDefinition
  name: string;
  description: string;
  input_schema_json: string;
}

export interface EvalResult {
  // Matches EvalResult from eval_output.py
  sample: EvalSample;
  attempts: Attempt[];
  sample_tool_definitions: ToolDefinition[];
  model_tool_definitions: ToolDefinition[];
}

export interface ModelConfig {
  // Matches ModelConfig from model_config.py
  name: string;
  anthropic_model: string;
  system_prompt?: string | null;
  supervisor_message?: string | null;
}

export interface EvalSummary {
  // Matches EvalSummary from eval_output.py
  model_config: ModelConfig;
  dataset_path: string;
  eval_results: Record<string, EvalResult>;
  stats: Record<string, number>;
}

export interface ComparisonSummary {
  // Matches ComparisonSummary from eval_output.py
  ref_summary: EvalSummary;
  new_model_summaries: Record<string, EvalSummary>;
  ref_system_prompt?: string | null;
  model_system_prompts: Record<string, string>;
  ref_supervisor_message?: string | null;
  model_supervisor_messages: Record<string, string>;
}

// Additional types for the React app

export interface CategoryStatistics {
  total: number;
  correct: number;
  totalAttempts: number;
  totalCorrectAttempts: number;
}

export interface OverallStatistics {
  totalSamples: number;
  correctSamples: number;
  totalAttempts: number;
  totalCorrectAttempts: number;
  total?: number; // Added for compatibility
  correct?: number; // Added for compatibility
}

export interface ModelStatistics {
  totalSamples: number;
  correctSamples: number;
  totalAttempts: number;
  totalCorrectAttempts: number;
  categoryStats: Record<string, CategoryStatistics>;
}

export interface ComparisonCounts {
  improvements: number;
  regressions: number;
}

// Simple interfaces for UI components
export interface SystemPrompt {
  content: string;
}

export interface SupervisorMessage {
  content: string;
}

// Main report data interface
export interface ReportData {
  comparison_summary: ComparisonSummary;
  ref_model_name: string;
  model_names: string[];
  dataset_path: string;
}
