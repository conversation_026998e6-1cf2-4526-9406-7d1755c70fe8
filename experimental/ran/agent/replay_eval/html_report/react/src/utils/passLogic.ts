import { PassLogicType, EvalResult, Attempt } from '../types';

/**
 * Determine if a sample passes based on the selected pass logic.
 *
 * @param correctAttempts - Number of correct attempts
 * @param totalAttempts - Total number of attempts
 * @param passLogic - The pass logic to use
 * @returns Whether the sample passes
 */
export function doesSamplePass(
  correctAttempts: number,
  totalAttempts: number,
  passLogic: PassLogicType
): boolean {
  switch (passLogic) {
    case 'at_least_one':
      return correctAttempts > 0;
    case 'majority':
      return correctAttempts > totalAttempts / 2;
    case 'all':
      return correctAttempts === totalAttempts;
    case 'aggregate':
      // For aggregate, we use a default display
      return correctAttempts > 0;
    default:
      return correctAttempts > 0;
  }
}

/**
 * Count correct attempts in a sample result.
 *
 * @param sampleResult - The sample result
 * @returns Number of correct attempts
 */
export function countCorrectAttempts(evalResult: EvalResult): number {
  let correctAttempts = 0;

  evalResult.attempts.forEach((attempt: Attempt) => {
    if (attempt.is_correct) correctAttempts++;
  });

  return correctAttempts;
}
