# React Implementation of HTML Report Utilities

This directory contains a React implementation of the HTML reports generated by the replay evaluation framework.

## Directory Structure

- `src/`: Contains all React source files
  - `components/`: React components organized by feature
    - `common/`: Reusable UI components
    - `PassLogic/`: Components for pass logic selection
    - `Statistics/`: Components for displaying statistics
    - `ModelComparison/`: Components for model comparison
    - `SystemPrompt/`: Components for system prompt display
    - `SupervisorMessage/`: Components for supervisor message display
    - `SampleResults/`: Components for sample results display
  - `contexts/`: React context providers
  - `hooks/`: Custom React hooks
  - `utils/`: Utility functions
  - `styles/`: Global CSS styles
  - `types/`: TypeScript type definitions
  - `App.tsx`: Main application component
  - `main.tsx`: Entry point

## Pass Logic Options

The React implementation includes the same pass logic options as the original JavaScript implementation:

1. **At Least One Attempt** (default) - A sample passes if at least one attempt is correct
2. **Majority of Attempts** - A sample passes if more than half of its attempts are correct
3. **All Attempts** - A sample passes only if all attempts are correct
4. **Aggregate All Attempts** - Success rate is calculated by dividing total correct attempts by total attempts across all samples

The pass logic selection is persisted in localStorage so it will be remembered when the user returns to the report.

## Running the Application

1. Install dependencies:
   ```
   cd react
   npm install
   ```

2. Start the development server:
   ```
   npm run dev
   ```

3. Build for production:
   ```
   npm run build
   ```

   The build is configured to use relative paths (`base: './'` in vite.config.ts), which allows the app to be deployed to any directory or opened directly from the filesystem.

## Testing

The project includes a comprehensive test suite using Vitest, React Testing Library, and Jest DOM matchers. The tests cover:

- Utility functions (passLogic, statistics)
- UI components (Button, Details)
- App component with mocked dependencies

To run tests:
```
npm test           # Run tests once
npm run test:watch # Run tests in watch mode
```

To run tests with coverage:
```
npm run test:coverage
```

## Integration with Python Backend

The React application is designed to work with the same data format as the original JavaScript implementation. The Python backend can generate the necessary data in JSON format, which can then be loaded by the React application.

### Using the React Report Generator

A Python script `react_report_generator.py` is provided to generate JSON data for the React app, build the app, and copy it to the output directory:

```python
from pathlib import Path
from experimental.ran.agent.replay_eval.html_report.react_report_generator import save_react_comparison_html_report

# Generate and save the React report
output_dir = Path('/path/to/output')
html_path = save_react_comparison_html_report(comparison_summary, output_dir)
```

This will:
1. Generate JSON data from the comparison summary using the dataclass JSON serialization
2. Build the React app with the data
3. Copy the built app to the output directory
4. Return the path to the index.html file

The script uses the `to_dict()` method provided by the `dataclass_json` mixin to convert the dataclasses to JSON-serializable dictionaries. This avoids the need for manual conversion and ensures that all the data is properly serialized.

The React app is designed to work directly with the data structure from the Python code, using TypeScript interfaces that match the Python dataclass structure. This approach simplifies the integration between the Python backend and the React frontend.

## Features

- **Component-Based Architecture**: Modular components for better maintainability
- **TypeScript**: Type safety for better developer experience
- **React Hooks**: Modern React patterns for state management
- **CSS Modules**: Scoped CSS for component styling
- **Responsive Design**: Works on different screen sizes
