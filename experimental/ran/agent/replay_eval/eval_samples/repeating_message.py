from typing import Iterable, <PERSON><PERSON>
from base.prompt_format.common import ChatR<PERSON>ultN<PERSON>, ChatResultNodeType, ToolDefinition
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.utils.ri_utils import get_chat_host_request
from experimental.ran.utils.str_utils import calc_common_prefix_length
from services.chat_host.chat_proto_util import convert_history

samples = [
    "f3d98bb5-4720-4798-bbfb-67a063a418ab",
    "8dac4747-2401-49c0-8ccc-e0078e10ea3f",
    "f46c1096-2627-49c3-ad54-1929c2b3a12c",
]

COMMON_PREFIX_LENGTH_THRESHOLD = 50


def extract_text_from_response(chat_result_nodes: Iterable[ChatResultNode]) -> str:
    text = ""
    for node in chat_result_nodes:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            text += node.content
    return text


def eval_func(
    response: ModelResponse,
    tool_definitions: list[ToolDefinition],
    request_id: str,
) -> Tuple[bool, str]:
    cur_response_text = extract_text_from_response(response)
    request = get_chat_host_request(request_id)
    assert request
    chat_history = convert_history(request.chat_history)
    for exchange in chat_history:
        response_str = extract_text_from_response(exchange.response_nodes)
        common_prefix_length = calc_common_prefix_length(
            response_str, cur_response_text
        )
        if common_prefix_length > COMMON_PREFIX_LENGTH_THRESHOLD:
            return False, f"Repeated message found: {response_str}"
    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"repeating_message_{index}",
        eval_response_func=eval_func,
        category="repeating_message",
        linear_url="https://linear.app/augmentcode/issue/AU-8784/agent-repeats-text-block-between-edits",
    )
    for index, sample in enumerate(samples)
]
