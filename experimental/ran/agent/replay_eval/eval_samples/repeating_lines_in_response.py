from typing import Iterable, <PERSON><PERSON>
from base.prompt_format.common import ChatResultNode, ChatResultNodeType, ToolDefinition
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelResponse

samples = [
    "f380ef48-7f6b-4627-81ca-f5c8d7c02e94",
]


def extract_text_from_response(chat_result_nodes: Iterable[ChatResultNode]) -> str:
    text = ""
    for node in chat_result_nodes:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            text += node.content
    return text


def eval_func(
    response: ModelResponse,
    tool_definitions: list[ToolDefinition],
    request_id: str,
) -> Tuple[bool, str]:
    cur_response_text = extract_text_from_response(response)
    cur_response_lines = cur_response_text.splitlines()
    # check if there is a line that is repeated at least 3 times in a row
    for i in range(len(cur_response_lines) - 2):
        if (
            cur_response_lines[i] == cur_response_lines[i + 1]
            and cur_response_lines[i] == cur_response_lines[i + 2]
        ):
            return False, f"Repeated line found: {cur_response_lines[i]}"
    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"repeating_lines_in_response_{index}",
        eval_response_func=eval_func,
        category="repeating_lines_in_response",
        linear_url="https://linear.app/augmentcode/issue/AU-8965/edit-tool-infinite-loop",
    )
    for index, sample in enumerate(samples)
]
