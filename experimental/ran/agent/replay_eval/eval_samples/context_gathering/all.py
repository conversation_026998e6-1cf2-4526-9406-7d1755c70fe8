import experimental.ran.agent.replay_eval.eval_samples.context_gathering.should_use_view as should_use_view
import experimental.ran.agent.replay_eval.eval_samples.context_gathering.should_use_retrieval as should_use_retrieval
import experimental.ran.agent.replay_eval.eval_samples.context_gathering.should_use_some_search_tool as should_use_some_search_tool

samples = (
    should_use_view.samples
    + should_use_retrieval.samples
    + should_use_some_search_tool.samples
)
