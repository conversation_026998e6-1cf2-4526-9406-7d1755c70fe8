#!/usr/bin/env python3

import logging
from typing import List

from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.launch_process_sample_extractor import (
    create_eval_sample_from_tool_call_data,
    extract_launch_process_samples_from_request_ids,
)

logger = logging.getLogger(__name__)

# Request IDs from staging environment conversations with LaunchProcess tool calls
# These were identified from the analytics we ran earlier
STAGING_LAUNCH_PROCESS_REQUEST_IDS = [
    "67787b27-f047-495a-bc95-4b780c1bb299",
    "29bfb9ea-19d4-4978-9d14-57838c269f65",
    "bc395eb4-f38a-4baa-9083-e6afcece7325",
    "a6ec1398-23ea-496c-a54e-a1e11914f196",
    "17203391-f691-4f0d-90d2-66d562bfbbcb",
    "a7d3c048-eae5-4625-a899-d501d5b4b538",
    "084db674-348f-4cd8-9639-1a8a7b793a31",
    "9823e790-3a22-4fe7-9016-b00f5a186a9f",
    "2229dfa9-2093-4990-9a49-9c38268a42e4",
    "c58bd507-ef8b-4233-99c5-6b16f0467c17",
    "c5b2ad79-1c25-4c98-b515-193a325d3b5a",
]


def create_launch_process_focus_samples() -> List[EvalSample]:
    """Create evaluation samples for LaunchProcess focus lines evaluation.

    Returns:
        List of EvalSample objects for LaunchProcess tool calls
    """
    logger.info("Extracting LaunchProcess tool call data from staging request IDs...")

    # Extract tool call data from the request IDs
    tool_call_data_list = extract_launch_process_samples_from_request_ids(
        STAGING_LAUNCH_PROCESS_REQUEST_IDS, tenant_name="dogfood-shard"
    )

    if not tool_call_data_list:
        logger.warning("No LaunchProcess tool call data found!")
        return []

    logger.info(f"Found {len(tool_call_data_list)} LaunchProcess tool calls")

    # Create evaluation samples (without baseline for initial creation)
    samples = []
    for tool_call_data in tool_call_data_list:
        sample = create_eval_sample_from_tool_call_data(
            tool_call_data,
            baseline_line_ranges=None,  # Will be set later after baseline is established
            sample_name_suffix="staging",
        )
        samples.append(sample)

    logger.info(f"Created {len(samples)} evaluation samples")
    return samples


# Create the samples
samples = create_launch_process_focus_samples()
