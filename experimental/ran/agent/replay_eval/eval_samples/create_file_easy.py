from typing import <PERSON><PERSON>
from base.prompt_format.common import ToolDefinition
from base.third_party_clients.third_party_model_client import Too<PERSON><PERSON><PERSON><PERSON>, ToolChoiceType
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.utils import (
    count_tokens,
    extract_tool_call,
)

samples = [
    "4f6241a6-031f-4e22-8232-4ef9f72e23bb",
    "5d5e058d-f3e3-4089-ab4c-574b72f2e845",
]


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_call, message = extract_tool_call(response, "save-file")
    if tool_call is None:
        return False, message
    inputs = tool_call.input
    if inputs.get("file_content") is None and inputs.get("file_outline") is None:
        return False, "No file_content or file_outline found in tool input"

    return True, ""


def gen_stats_func(
    response: ModelResponse, _tool_definitions: list[ToolDefinition], _request_id: str
) -> dict[str, int | float]:
    tool_call, message = extract_tool_call(response, "save-file")
    if (
        tool_call is None
        or tool_call.input is None
        or not tool_call.input.get("file_content")
        and not tool_call.input.get("file_outline")
    ):
        return {}
    file_content = tool_call.input.get("file_content", "")
    file_outline = tool_call.input.get("file_outline", "")
    num_output_lines = (file_content or file_outline).count("\n") + 1
    return {
        "num_output_tokens": count_tokens(response),
        "num_output_lines": num_output_lines,
    }


samples = [
    EvalSample(
        request_id=sample,
        name=f"create_file_easy_{index}",
        eval_response_func=eval_func,
        # assistant_message_prefill_from_response=True,
        # tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="save-file"),
        linear_url="https://linear.app/augmentcode/issue/AU-8049/running-out-of-output-tokens-when-calling-str-replace-editor",
        category="create_file_easy",
        gen_stats_func=gen_stats_func,
    )
    for index, sample in enumerate(samples)
]
