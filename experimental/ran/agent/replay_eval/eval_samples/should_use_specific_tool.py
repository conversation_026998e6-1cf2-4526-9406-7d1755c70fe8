from experimental.ran.agent.replay_eval.eval_sample import <PERSON>l<PERSON>ample
from experimental.ran.agent.replay_eval.eval_functions import (
    contains_one_of_tool_calls,
)

samples = [
    (
        "6e344732-bb5a-4bf7-9521-5a0499889890",
        "",
        "launch-process",
        "https://linear.app/augmentcode/issue/AU-7936/agent-does-retrieval-rather-than-execute-commands",
    ),
    (
        "ce54aef2-470a-4033-b836-c0e215936b42",
        "",
        "launch-process",
        "",
    ),
    (
        "99c5e809-9411-4deb-a71d-0c68090dfcbd",
        "",
        "str-replace-editor",
        "",
    ),
]

samples = [
    EvalSample(
        request_id=sample[0],
        name=f"should_use_{sample[2]}_{index}",
        eval_response_func=contains_one_of_tool_calls([sample[2]]),
        assistant_message_prefill=sample[1],
        linear_url=sample[3],
    )
    for index, sample in enumerate(samples)
]
