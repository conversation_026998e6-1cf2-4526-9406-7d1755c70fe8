from experimental.ran.agent.replay_eval.eval_functions import (
    calls_tool_with_valid_input,
)
from experimental.ran.agent.replay_eval.eval_sample import EvalSample

# https://linear.app/augmentcode/issue/AU-7786/strreplaceeditor-tool-check-if-provided-inputs-are-wrapped-in-a-str
samples = [
    # "5e2e01f4-f652-4646-a228-0602244e5b6c",
    "c0b586a7-41fc-485c-9bc7-64360fa6acbf",
    "a1f12e0e-27e7-4fee-87be-f9628f2d4d2d",
    "b2ae2c39-71f8-4cf0-8455-e3ff3a1abdef",
    "64d536d4-33e7-43b6-a756-ea7efeac2ad8",
    "3498544b-d737-4f60-8bfb-885c7473d198",
    "d75cfb4d-20db-434d-a5c5-36e46c8d254e",
    "3ad22caf-d58a-48fc-bb4d-cae73b40bf46",
    "a5faa29d-b2ba-4753-a8e3-787eaadc1ad4",
    "62f08246-f123-4dc5-aa41-554284fd522d",
    "cf6250be-526f-47af-af48-b668ea4b0172",
    "06656eb6-0be4-4b58-a677-5b0ca8f430f0",
    "2883fd09-3be7-4c7c-a7d3-94d3d75b59cf",
    "edd86df7-6ff8-4e11-9681-4b6a9b78006d",
    "0a1a2142-11a1-424a-bfd9-80b2efd5801b",
    "cb87d821-b860-4eff-a399-f57dbabc0ffe",
    "a9311ddb-770e-4cb2-8b7b-319cb0e5b45f",
    "ead945e3-8e45-47f2-8ad7-26fd7fab2724",
    "a7859357-354d-4437-ba11-4e77271337e3",
    "e69f01d0-7cb6-46dd-8b36-da44e9176ec3",
    "dabb7b59-2afc-45cb-8fab-4cbbc87f6b93",
]

samples = [
    EvalSample(
        request_id=sample,
        name=f"str_replace_editor_invalid_inputs_{index}",
        eval_response_func=calls_tool_with_valid_input("str-replace-editor"),
        assistant_message_prefill_from_response=True,
        category="invalid_inputs",
    )
    for index, sample in enumerate(samples)
]
