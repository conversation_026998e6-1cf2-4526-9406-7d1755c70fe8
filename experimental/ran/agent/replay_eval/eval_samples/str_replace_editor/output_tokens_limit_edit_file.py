from typing import <PERSON><PERSON>
from base.prompt_format.common import ToolDefinition
from base.third_party_clients.third_party_model_client import Too<PERSON><PERSON><PERSON><PERSON>, ToolChoiceType
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.str_replace_editor_utils import (
    extract_str_replace_entries_any_schema,
)
from experimental.ran.agent.replay_eval.utils import (
    count_tokens,
    extract_tool_call,
)

samples = [
    "419ba84d-49ee-47aa-bd13-63013013bcd9",
    "fc57d47e-0e5b-465f-83b2-43d5ba34c31b",
    "70a85184-b083-4440-a00e-4df6efc23888",
    "aaa42ae1-3c05-4dc0-bd5a-555a350f248e",
    "e09faf17-3005-4137-b72a-9a2293e549ac",
    "f8d16f62-394e-4d2b-ae8d-e4c967223a64",
    "8eb63c75-7d38-4f3b-bf69-ed8dbc032b58",
    "d79ad660-9173-4a58-bbee-4c1537f88ae4",
    "56778d77-7106-4f2e-b30d-f810ad10d2eb",
    "f02904ef-d427-43cb-a763-e446b23d8133",
    "2ee904d5-3e6b-42a4-a3c7-19ef3b28ebd4",
    "60a3e623-802b-49b5-b3fb-db424a487c89",
    "3bb00449-bd6d-4083-a976-2166c85ab3e7",
    "65531a05-8b4c-43ea-a96a-8c6054000b5d",
]


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_call, message = extract_tool_call(response, "str-replace-editor")
    if tool_call is None:
        return False, message
    inputs = tool_call.input
    if inputs.get("command") != "str_replace":
        return False, "Response does not use the 'str_replace' command"
    str_replace_entries = extract_str_replace_entries_any_schema(inputs)
    if str_replace_entries is None or len(str_replace_entries) == 0:
        return False, "No str_replace entries found in tool input"
    if not isinstance(str_replace_entries[0], dict):
        return False, "First str_replace entry is not a dict"
    return True, ""


def gen_stats_func(
    response: ModelResponse, _tool_definitions: list[ToolDefinition], _request_id: str
) -> dict[str, int | float]:
    tool_call, message = extract_tool_call(response, "str-replace-editor")
    if tool_call is None:
        return {}
    inputs = tool_call.input
    if inputs.get("command") != "str_replace":
        return {}
    str_replace_entries = extract_str_replace_entries_any_schema(inputs)
    if (
        str_replace_entries is None
        or len(str_replace_entries) == 0
        or not isinstance(str_replace_entries, list)
    ):
        return {}

    if not isinstance(str_replace_entries[0], dict):
        breakpoint()
        return {}

    # Count total number of lines in both old_str and new_str
    num_output_lines = 0
    for entry in str_replace_entries:
        old_str = entry.get("old_str", "")
        new_str = entry.get("new_str", "")
        old_str_lines = old_str.count("\n") + 1 if old_str else 0
        new_str_lines = new_str.count("\n") + 1 if new_str else 0
        num_output_lines += old_str_lines + new_str_lines

    return {
        "num_output_tokens": count_tokens(response),
        "num_output_lines": num_output_lines,
    }


samples = [
    EvalSample(
        request_id=sample,
        name=f"output_tokens_limit_edit_file_{index}",
        eval_response_func=eval_func,
        assistant_message_prefill_from_response=True,
        assistant_message_prefill="Now I am going to use the str_replace command to edit the file.",
        tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="str-replace-editor"),
        linear_url="https://linear.app/augmentcode/issue/AU-8049/running-out-of-output-tokens-when-calling-str-replace-editor",
        category="output_tokens_limit_edit_file",
        gen_stats_func=gen_stats_func,
    )
    for index, sample in enumerate(samples)
]
