from experimental.ran.agent.replay_eval.eval_samples.str_replace_editor.small_view.gen_samples import (
    gen_samples,
)

# first request with a view command in a sequence of at least 3 view calls
samples = gen_samples(
    # Most of these requests (commented out) already contain small request in history,
    # Which nudges model into keep doing them.
    # Uncommented requests, don't have it in history
    # [
    #     "dae01eec-f9b1-44e9-99a8-04b2309cac4c",
    #     "412e1b38-346b-4021-8d6f-a913a2d40ee9",
    #     "efc2c0e6-363a-4373-a359-ebda4e676050",
    #     "20cfa344-2168-43df-8512-39eaa64c8356",
    #     "77a7513d-e561-4c84-816b-1b7b45f2ed28",
    #     "56660fb8-80a3-4a39-bb60-8acc44d15629",
    #     "09101598-de06-4564-9e0a-7165aca75d31",
    #     "4dbf13fc-caef-47e3-b0b1-0f81e334b80f",
    #     "900ae69c-464f-4da3-92f1-7e3e10ce7391",
    #     "9405ce17-e7fa-44bd-ac2c-dce1ea592d10",
    #     "be30d09d-640c-449f-b294-c535665996ff",
    #     "52f95af6-f0a5-4c53-ae53-a521962cc945",
    #     "65e149d6-9d00-4242-94b3-354f43c019be",
    #     "1bcfeb58-94f4-498c-969c-ada0c2241902",
    #     "45853143-7775-4fdb-9822-53658dc9ee62",
    #     "579f5dd9-e83e-4d5e-a07b-defc04b003fa",
    #     "bce8e56d-c7ba-4f95-929d-eca09be655a3",
    #     "4c9356ac-0e0b-434d-a507-76fba7a1ee25",
    #     "28cdc9c0-066d-4312-bf96-9fc15a3e0a2f",
    #     "022c5046-0738-47fa-8742-690893fecb26",
    # ]
    [
        "351c7057-be56-447b-b1bb-42d3b54e072b",
        "f95842cb-1ba5-46ea-b09c-36e5d3974fdd",
        "b291ec3f-9b0f-4167-bc42-49556221c998",
        "56660fb8-80a3-4a39-bb60-8acc44d15629",
        "688f6f89-6a52-41a6-8300-10720a3a9968",
        "9eb471aa-3e76-4835-a490-542ecaa11b47",
        "82b8e8c6-462a-4304-b91d-1d038c5716d6",
        "3b78ebb2-a280-4279-bfb7-6f5cda4503d3",
        "73eb7506-11e4-4e72-beb7-e612f79c90e3",
        "4dbf13fc-caef-47e3-b0b1-0f81e334b80f",
        "579f5dd9-e83e-4d5e-a07b-defc04b003fa",
        "28cdc9c0-066d-4312-bf96-9fc15a3e0a2f",
    ]
)
