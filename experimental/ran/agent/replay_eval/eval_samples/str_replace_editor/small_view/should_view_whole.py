from experimental.ran.agent.replay_eval.eval_samples.str_replace_editor.small_view.gen_samples import (
    gen_samples,
)

# cases where there was no reason to look at specific lines
# - was not retrieved
# - was not mentioned in format services/auth/central/server/async_ops_publisher.go:123
# - was not mentioned with diff
# - was not created by model
samples = gen_samples(
    [
        "b937c5a3-b532-4894-9e02-6acb503c3c17",
        "4dbf13fc-caef-47e3-b0b1-0f81e334b80f",
        "c159ac33-621b-4943-b764-1cca690b0743",
        "89c41c2f-0f1c-4718-b0a1-d8b5a4233ffd",
        "f4f41fc9-7ebe-4ea8-8776-3310c3ba8832",
        "c81dd9ab-aec1-46ad-a926-64831cc69cc3",
        "ac375b87-fd42-42f5-861a-4df10be609ad",
        "8b4fa52e-4274-4150-ad10-151b01370aeb",
        "623cefb2-b847-420e-81b3-00cf8a672536",
        "23a9e924-4190-4391-9200-d4aa82466c78",
        "0a99b890-0ddf-441f-ab12-88aec762a804",
        "c306656b-20a3-471d-8fa1-7e952fdad719",
        "d0963a66-9a2f-4dc4-b49e-945baedaed1a",
        "b6fdd835-f094-482a-9cad-46202b8fbc6a",
    ]
)
