from typing import <PERSON><PERSON>

from experimental.ran.agent.replay_eval.eval_functions import (
    calls_tool_with_valid_input,
)
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.utils import extract_tool_calls

samples = [
    "abe84f1c-82aa-46c9-b75e-e52ac32ff821",
    # "57d7f8ee-ddb7-4afc-8e74-49f443eb385e",
    # "1d3bb312-60f4-4dda-b17b-f8ce9eab3858",
    # "23b1b7fa-16c2-4ad0-aca4-47b40a27980f",
    # "c3a6cd89-d95a-4614-8367-707239ca1733",
    # "a4ae4865-d52e-4e0c-8026-97af11b0c8ea",
    # "0470ce92-2b2c-40fb-b784-b1291096d9d5",
    # "19ffa97c-ece6-4d70-b2c7-c098d6d605b6",
    # "472843be-281a-4a44-8f94-f3c6d7ed2b95",
    # "e05ada0c-f7d8-4d5d-afbb-c31af3c8aa77",
    # "f9437b7d-60c0-4ce8-95f1-ea237ed18bdd",
    # "11a1e2bb-ae57-4376-ab03-2853b3a8c68e",
    # "1e685703-dee6-4b6e-81d4-ec944c0112e6",
    # "f3c526af-71c8-4dc7-9f07-ad99be631203",
    # "af8ac69d-8da3-4063-bdd7-921314d00bc8",
    # "d799f4d8-9d80-4418-a4d1-830eff96dbc4",
    # "4372b876-c20b-4f1e-a943-2acab57a11d5",
    # "e1ae045f-e016-4617-ac57-118f8087ff72",
    # "63708b00-5673-40c5-9263-95e9339f480e",
    # "1a9071c7-0372-410f-9ff4-3e21a58b52f1",
    # "2e8035ae-e5c9-4cb2-8c9b-1ba87140e6cb",
    # "c08a92b1-6dbf-4e80-b073-a4c35deea03d",
    # "b1dab521-c037-4ee8-bd7b-acf89ddef7ea",
    # "f24ea488-b799-4dd2-8cd3-2e0b0dd39d62",
    # "06b6db31-4fdb-462e-9144-41c42a0722e9",
    # "ece795e6-c258-4631-868c-950bcf19e367",
    # "028a4f24-7b67-4417-be2c-7aa7466ac2cf",
    # "ffcb257c-bc2d-40a5-9dba-98fa0febb30c",
    # "bb7b2912-8b3f-4726-aa28-b2b35c48d4a8",
    # "befe836b-8478-418f-b27c-3c3d7995b4e9",
    # "0ddf00d7-773c-4dff-929c-0f01f54def05",
    # "9fb871f6-b23e-4828-873b-2fd31d8897da",
    # "2ed36e12-801a-4f30-b4bd-8594fed4baf0",
    # "d67b80e3-fa9b-45e3-aa1e-02a4695afa34",
    # "554e4c8a-4696-4f24-8fbe-ec9a930e8ef8",
    # "b71c0425-c6e5-45c4-be55-6521bcfc8567",
]


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    for tool_call in tool_calls:
        if tool_call.name == "str-replace-editor":
            if tool_call.input.get("command") == "str_replace":
                old_str = tool_call.input.get("old_str") or tool_call.input.get(
                    "old_str_1"
                )
                if old_str is None:
                    return False, "No old_str found in tool input"
                if not isinstance(old_str, str):
                    return False, "old_str is not a string"
    return True, ""


samples = [
    EvalSample(
        request_id=sample,
        name=f"invalid_inputs_obj_instead_of_str_{index}",
        eval_response_func=eval_func,
        assistant_message_prefill_from_response=True,
        category="invalid_inputs_obj_instead_of_str",
    )
    for index, sample in enumerate(samples)
]
