import pandas as pd
from io import <PERSON><PERSON>

from typing import <PERSON><PERSON>
from base.prompt_format.common import ToolDefinition
from base.third_party_clients.third_party_model_client import Too<PERSON><PERSON><PERSON><PERSON>, ToolChoiceType
from experimental.ran.agent.replay_eval.eval_sample import EvalSample
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.utils import (
    count_tokens,
    extract_tool_call,
)

samples = [
    "24662b6e-2b1e-4456-9468-aa7caa2df1df",
    "558ab8e7-f83a-47a2-92ac-e10bed4d30c1",
    "7ab9f9b0-833c-49eb-8225-67db86e709e2",
    "a6338a2e-9d0c-45b5-ae22-c5f3f42a5ef2",
    "c2f2f3b9-ad14-4eb8-8630-75201edeaa81",
    "18989f71-1f76-47ac-a9a5-cad72e53d8c9",
    "c19f95ab-cdd9-4093-8030-b86711733e04",
    "b04c6e26-e4a7-4103-8c52-93f87b17431e",
    "3403782b-950f-426a-ac53-08f661cacb7e",
    "2410dc65-18b9-4e7a-99f5-6966c4ec5514",
    "1933f0b0-6923-4ead-a29a-e173061915d5",
    "819daa62-244c-45da-9992-2ba5dd4d0c12",
    # potentially bad samples
    # "f6c96f1f-cb4b-44f3-80cc-bf3a3770f9dc",
    # "8299e813-88a8-4e63-8c62-d8cb7813a57a",
    # "3dfc515f-745d-4a83-b3b6-37bb983c862a",
]
df = pd.DataFrame({"request_id": samples})
df["tenant"] = "dogfood-shard"

# Examples that failed even after REMINDER_INSTRUCTION
other_samples = """\
request_id,tenant
e56719a6-4396-4310-9c36-e9c6da256984,i1-vanguard0
# 2233ec96-895a-451a-aeab-e42873552629,i1-vanguard2
4e61a874-1e01-4fc7-a819-20824424b834,i1-vanguard0
# Random 16 samples from 2025-05-14, 5 haven't upgraded.
0065e507-fb62-49ba-b785-122b200e9eb2,i1-vanguard4
# 01e093cd-ca4e-4ba6-9f09-1a88264535d8,i1-vanguard4
02b2af86-0115-47a8-b4fb-cd39049c4328,i1-vanguard5
# 03ec812e-ace9-4935-b91a-f9bfdb429f48,i0-vanguard3
# 05221a69-9637-4f02-bd4a-a5464d679255,i1-vanguard5
# 05f18cb5-ceac-453f-8ebe-15d4bc570be4,i1-vanguard4
# 0633673c-83f2-4561-b826-cf90ecdaaa86,i1-vanguard0
06730bb4-4f8b-4f7a-8d09-ffe630beb2d3,i0-vanguard4
08c88dd1-0457-41ab-a176-0885881ed1d3,i0-vanguard2
09be19ee-5a45-449d-ac29-5fbfc9ff8d41,i0-vanguard3
0a142de0-e08e-4c92-87b4-57d34c367514,i1-vanguard1
0b3164d9-0436-4032-a35d-3bcefb3b27a4,i1-vanguard0
0e16231c-25c4-4720-bc98-bb6df864fbe1,i0-vanguard1
0fa0f3ab-f0b0-4d37-b52c-dfac6c78ec8d,i0-vanguard4
1007eaba-bfee-4c44-96b0-7c1ff21e0fc0,i1-vanguard3
107b4478-6e23-4466-8b4e-f19612b2add6,i0-vanguard2
"""
other_df = pd.read_csv(StringIO(other_samples), comment="#")
df = pd.concat([df, other_df], ignore_index=True)


def extract_file_content(tool_call_input: dict[str, str]) -> str:
    file_content = ""

    if tool_call_input.get("file_content") is not None:
        return tool_call_input["file_content"]

    if tool_call_input.get("file_outline") is not None:
        return tool_call_input["file_outline"]

    for key, value in sorted(tool_call_input.items()):
        if key.startswith("file_content_"):
            file_content += value

    return file_content


def eval_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_call, message = extract_tool_call(response, "save-file")
    if tool_call is None:
        return False, message
    file_content = extract_file_content(tool_call.input)
    if not file_content:
        return False, "No file_content or file_outline found in tool input"
    return True, ""


def gen_stats_func(
    response: ModelResponse, _tool_definitions: list[ToolDefinition], _request_id: str
) -> dict[str, int | float]:
    tool_call, message = extract_tool_call(response, "save-file")
    if tool_call is None or tool_call.input is None:
        return {}
    file_content = extract_file_content(tool_call.input)
    if not file_content:
        return {}
    num_output_lines = file_content.count("\n") + 1
    return {
        "num_output_tokens": count_tokens(response),
        "num_output_lines": num_output_lines,
    }


samples = [
    EvalSample(
        request_id=row.request_id,
        name=f"output_tokens_limit_create_file_{index}",
        eval_response_func=eval_func,
        assistant_message_prefill_from_response=True,
        tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name="save-file"),
        linear_url="https://linear.app/augmentcode/issue/AU-8049/running-out-of-output-tokens-when-calling-str-replace-editor",
        category="output_tokens_limit_create_file",
        gen_stats_func=gen_stats_func,
        tenant_name=row.tenant,
    )
    for index, row in df.iterrows()
]
