from typing import <PERSON><PERSON>
from experimental.ran.agent.replay_eval.eval_sample import <PERSON><PERSON><PERSON><PERSON>
from experimental.ran.agent.replay_eval.model_config import ModelResponse
from experimental.ran.agent.replay_eval.utils import extract_tool_calls

samples = [
    (
        "cf90cbea-48e0-4af9-8385-d166e420e6a3",
        "",
        "https://linear.app/augmentcode/issue/AU-7986/agent-repeats-user-given-plan-and-asks-for-approval",
    ),
]


def eval_response_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    if len(tool_calls) == 0:
        return False, "No tool calls found which means model asks for approval"
    return True, ""


samples = [
    EvalSample(
        request_id=sample[0],
        name=f"no_approval_needed_{index}",
        eval_response_func=eval_response_func,
        assistant_message_prefill=sample[1],
        linear_url=sample[2],
    )
    for index, sample in enumerate(samples)
]
