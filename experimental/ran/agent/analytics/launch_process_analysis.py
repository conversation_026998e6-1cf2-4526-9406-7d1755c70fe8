#!/usr/bin/env python3

import datetime
import logging
from typing import Optional, List

from experimental.ran.agent.analytics.conversation import Conversation

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def is_staging_environment(conversation: Conversation) -> bool:
    """Check if a conversation is from a staging environment.

    Args:
        conversation: Conversation to check

    Returns:
        True if the conversation appears to be from a staging environment
    """
    # Check user messages and tool outputs for staging indicators
    staging_indicators = ["staging", "stage", "test", "dev"]

    for round in conversation.agent_rounds:
        # Check user message
        if round.user_message:
            user_msg_lower = round.user_message.lower()
            if any(indicator in user_msg_lower for indicator in staging_indicators):
                return True

        # Check tool outputs
        for turn in round.agent_turns:
            if (
                turn.tool_call
                and turn.tool_call.tool_result
                and turn.tool_call.tool_result.content
            ):
                tool_output_lower = turn.tool_call.tool_result.content.lower()
                if any(
                    indicator in tool_output_lower for indicator in staging_indicators
                ):
                    return True

    return False


def find_launch_process_requests(
    conversations: List[Conversation], staging_only: bool = True
) -> List[str]:
    """Find request IDs that contain LaunchProcess tool calls.

    Args:
        conversations: List of conversations to analyze
        staging_only: If True, only return staging environment conversations

    Returns:
        List of request IDs that contain LaunchProcess tool calls
    """
    launch_process_request_ids = []

    for conversation in conversations:
        has_launch_process = False

        # Check if this is a staging environment conversation
        if staging_only and not is_staging_environment(conversation):
            continue

        for round in conversation.agent_rounds:
            for turn in round.agent_turns:
                if turn.tool_call and turn.tool_call.tool_use:
                    tool_name = turn.tool_call.tool_use.name

                    # Check if this is a LaunchProcess tool call
                    if tool_name in [
                        "launch-process",
                        "LaunchProcess",
                        "launch_process",
                    ]:
                        has_launch_process = True
                        logger.debug(
                            f"Found LaunchProcess tool call in request {turn.request_id}"
                        )
                        break

            if has_launch_process:
                break

        if has_launch_process:
            launch_process_request_ids.append(conversation.last_request_id)
            logger.debug(
                f"Conversation {conversation.last_request_id} contains LaunchProcess tool calls"
            )

    return launch_process_request_ids


def analyze_launch_process_conversations(
    conversations: List[Conversation],
    from_date: Optional[datetime.datetime] = None,
    to_date: Optional[datetime.datetime] = None,
) -> dict:
    """Analyze conversations for LaunchProcess tool usage.

    Args:
        conversations: List of conversations to analyze
        from_date: Start date for the analysis
        to_date: End date for the analysis

    Returns:
        Dictionary containing analysis results
    """
    launch_process_request_ids = find_launch_process_requests(conversations)

    # Count total LaunchProcess tool calls
    total_launch_process_calls = 0
    successful_calls = 0
    failed_calls = 0

    for conversation in conversations:
        for round in conversation.agent_rounds:
            for turn in round.agent_turns:
                if turn.tool_call and turn.tool_call.tool_use:
                    tool_name = turn.tool_call.tool_use.name

                    if tool_name in [
                        "launch-process",
                        "LaunchProcess",
                        "launch_process",
                    ]:
                        total_launch_process_calls += 1

                        # Check if the call was successful
                        if turn.tool_call.tool_result:
                            if turn.tool_call.tool_result.is_error:
                                failed_calls += 1
                            else:
                                successful_calls += 1

    success_rate = (
        (successful_calls / total_launch_process_calls * 100)
        if total_launch_process_calls > 0
        else 0
    )

    analysis_results = {
        "date_range": {
            "from_date": from_date.isoformat() if from_date else None,
            "to_date": to_date.isoformat() if to_date else None,
        },
        "total_conversations": len(conversations),
        "conversations_with_launch_process": len(launch_process_request_ids),
        "launch_process_request_ids": launch_process_request_ids,
        "total_launch_process_calls": total_launch_process_calls,
        "successful_calls": successful_calls,
        "failed_calls": failed_calls,
        "success_rate": success_rate,
    }

    return analysis_results


def print_launch_process_analysis(analysis_results: dict):
    """Print the LaunchProcess analysis results.

    Args:
        analysis_results: Dictionary containing analysis results
    """
    print("\n===== LaunchProcess Tool Analysis =====\n")

    # Print date range if available
    date_range = analysis_results.get("date_range", {})
    if date_range.get("from_date") and date_range.get("to_date"):
        print(f"Date range: {date_range['from_date']} to {date_range['to_date']}")
    elif date_range.get("from_date"):
        print(f"From date: {date_range['from_date']}")
    elif date_range.get("to_date"):
        print(f"To date: {date_range['to_date']}")

    print(f"Total conversations analyzed: {analysis_results['total_conversations']}")
    print(
        f"Conversations with LaunchProcess tool calls: {analysis_results['conversations_with_launch_process']}"
    )
    print(
        f"Total LaunchProcess tool calls: {analysis_results['total_launch_process_calls']}"
    )
    print(f"Successful calls: {analysis_results['successful_calls']}")
    print(f"Failed calls: {analysis_results['failed_calls']}")
    print(f"Success rate: {analysis_results['success_rate']:.2f}%")

    print("\nRequest IDs with LaunchProcess tool calls:")
    for request_id in analysis_results["launch_process_request_ids"]:
        print(f"  {request_id}")

    print(
        f"\nTotal matching request IDs: {len(analysis_results['launch_process_request_ids'])}"
    )
