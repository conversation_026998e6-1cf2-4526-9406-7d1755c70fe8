#!/usr/bin/env python3

import argparse
from concurrent.futures import ThreadPoolExecutor
import datetime
import logging
from tqdm import tqdm

from base.datasets.tenants import get_tenant

from experimental.ran.agent.analytics.big_query_utils import (
    get_agent_conv_last_request_ids,
)
from experimental.ran.agent.analytics.conversation import Conversation
from experimental.ran.agent.analytics.launch_process_analysis import (
    analyze_launch_process_conversations,
    print_launch_process_analysis,
)
from experimental.ran.utils.ri_utils import (
    get_chat_host_request_factory,
    get_chat_host_response_factory,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(
        description="Find staging environment requests with LaunchProcess tool calls"
    )
    date_group = parser.add_mutually_exclusive_group()
    date_group.add_argument(
        "--from-date",
        type=lambda s: datetime.datetime.fromisoformat(s),
        default=None,
        help="Start date in ISO format (YYYY-MM-DDTHH:MM:SS)",
    )
    date_group.add_argument(
        "-ld",
        "--last-days",
        type=int,
        default=7,
        help="Process data from the last N days (default: 7)",
    )
    parser.add_argument(
        "--to-date",
        type=lambda s: datetime.datetime.fromisoformat(s),
        default=None,
        help="End date in ISO format (YYYY-MM-DDTHH:MM:SS). Default: now",
    )
    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help="Tenant name to filter by. Default: dogfood-shard",
    )
    parser.add_argument(
        "--thread-count",
        type=int,
        default=20,
        help="Number of threads to use for parallel processing. Default: 20",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit the number of requests to process. Default: None (process all)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )
    return parser.parse_args()


def process_request_id(request_id, _get_chat_host_request, _get_chat_host_response):
    """Process a single request ID to get the conversation."""
    try:
        start_time = datetime.datetime.now()
        logger.debug(f"Processing request ID: {request_id}")

        # Get the chat host request and response
        chat_request = _get_chat_host_request(request_id=request_id)
        chat_response = _get_chat_host_response(request_id=request_id)

        if not chat_request or not chat_response:
            logger.warning(f"Could not find chat request or response for {request_id}")
            return None

        # Convert to conversation
        conversation = Conversation.from_chat_request(
            request_id=request_id,
            chat_request=chat_request,
            chat_response=chat_response,
            get_chat_host_request_func=_get_chat_host_request,
        )

        processing_time = datetime.datetime.now() - start_time
        logger.debug(
            f"Processed request ID {request_id} in {processing_time.total_seconds():.2f} seconds"
        )

        return conversation
    except Exception as e:
        logger.error(f"Error processing request ID {request_id}: {e}", exc_info=True)
        return None


def fetch_staging_conversations(
    from_date, to_date, tenant_name, thread_count, limit=None
):
    """Fetch staging conversations with LaunchProcess tool calls."""

    logger.info(
        f"Analyzing staging requests with LaunchProcess from {from_date} to {to_date} for tenant {tenant_name}"
    )

    # Get all conversation request IDs from the time range
    logger.info("Fetching conversation request IDs...")
    request_ids = get_agent_conv_last_request_ids(
        tenant=get_tenant(tenant_name),
        from_datetime=from_date,
        to_datetime=to_date,
    )

    if limit:
        request_ids = request_ids[:limit]

    logger.info(f"Found {len(request_ids)} conversation request IDs")

    # Process each request ID in parallel
    conversations = []
    total_processed = 0
    error_count = 0

    _get_chat_host_request = get_chat_host_request_factory(tenant_name)
    _get_chat_host_response = get_chat_host_response_factory(tenant_name)

    with ThreadPoolExecutor(max_workers=thread_count) as executor:
        futures = [
            executor.submit(
                process_request_id,
                request_id,
                _get_chat_host_request,
                _get_chat_host_response,
            )
            for request_id in request_ids
        ]

        # Use tqdm to show progress
        for future in tqdm(
            futures, desc="Processing conversations", total=len(futures)
        ):
            total_processed += 1
            conversation = future.result()
            if conversation:
                conversations.append(conversation)
            else:
                error_count += 1

    # Calculate error rate
    error_rate = (error_count / total_processed * 100) if total_processed > 0 else 0

    logger.info(f"Successfully processed {len(conversations)} conversations")
    logger.info(
        f"Processing errors: {error_count}/{total_processed} ({error_rate:.2f}%)"
    )
    return conversations, total_processed, error_count


def main():
    args = parse_args()

    # Set root log level
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

    # Get current time
    now = datetime.datetime.now(datetime.timezone.utc)

    # Parse dates
    to_date = args.to_date if args.to_date is not None else now

    # Determine from_date based on arguments
    if args.last_days is not None:
        from_date = now - datetime.timedelta(days=args.last_days)
        logger.info(f"Processing data from the last {args.last_days} days")
    elif args.from_date is not None:
        from_date = args.from_date
    else:
        # Default: process the last 7 days
        from_date = now - datetime.timedelta(days=7)
        logger.info("No time range specified, defaulting to the last 7 days")

    logger.info(f"Analyzing staging requests from {from_date} to {to_date}")

    # Fetch staging conversations with LaunchProcess tool calls
    conversations, total_processed, error_count = fetch_staging_conversations(
        from_date=from_date,
        to_date=to_date,
        tenant_name=args.tenant_name,
        thread_count=args.thread_count,
        limit=args.limit,
    )

    # Analyze the conversations
    analysis_results = analyze_launch_process_conversations(
        conversations, from_date=from_date, to_date=to_date
    )

    # Print the analysis results
    print_launch_process_analysis(analysis_results)

    # Return the request IDs for further use
    return analysis_results["launch_process_request_ids"]


if __name__ == "__main__":
    main()
