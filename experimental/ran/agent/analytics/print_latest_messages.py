#!/usr/bin/env python3
"""
Print Latest Request IDs Script

This script reads request IDs from a file (one per line), fetches each request,
gets the latest request ID from the chat history, and prints it to console.
At the end, it prints all request IDs one per line.
"""

import argparse
import logging
from pathlib import Path
from typing import List

from experimental.ran.utils.ri_utils import (
    get_chat_host_request_factory,
)
from base.datasets.tenants import get_tenant

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def read_request_ids_from_file(file_path: str) -> List[str]:
    """Read request IDs from a file, one per line."""
    request_ids = []
    try:
        with open(file_path, "r") as f:
            for line in f:
                request_id = line.strip()
                if request_id:  # Skip empty lines
                    request_ids.append(request_id)
        logger.info(f"Read {len(request_ids)} request IDs from {file_path}")
        return request_ids
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return []
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return []


def get_latest_request_id_from_history(
    request_id: str, get_chat_host_request_func, tenant_name: str
) -> str:
    """Get the latest request ID from a chat request's history."""
    try:
        chat_request = get_chat_host_request_func(request_id=request_id)
        if not chat_request:
            logger.warning(f"Could not find chat request for {request_id}")
            return f"[ERROR: Could not find chat request for {request_id}]"

        # Check chat history for the most recent request ID
        if hasattr(chat_request, "chat_history") and chat_request.chat_history:
            # Get the last exchange in chat history
            last_exchange = chat_request.chat_history[-1]

            # Return the request_id of the last exchange
            if hasattr(last_exchange, "request_id") and last_exchange.request_id:
                return last_exchange.request_id

        # If no chat history or no request_id in last exchange, return the current request_id
        return request_id

    except Exception as e:
        logger.error(f"Error processing request ID {request_id}: {e}")
        return f"[ERROR: {str(e)}]"


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Print latest request IDs from chat history for given request IDs in a file"
    )

    parser.add_argument(
        "file_path",
        type=str,
        help="Path to file containing request IDs (one per line)",
    )

    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help="Tenant name to use for fetching requests (default: dogfood-shard)",
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    # Set up logging
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

    # Check if file exists
    if not Path(args.file_path).exists():
        logger.error(f"File does not exist: {args.file_path}")
        return

    # Read request IDs from file
    request_ids = read_request_ids_from_file(args.file_path)
    if not request_ids:
        logger.error("No request IDs found in file")
        return

    logger.info(f"Processing {len(request_ids)} request IDs")
    logger.info(f"Tenant: {args.tenant_name}")

    # Set up BigQuery functions
    get_chat_host_request_func = get_chat_host_request_factory(args.tenant_name)

    # Process each request ID
    processed_request_ids = []

    for i, request_id in enumerate(request_ids, 1):
        logger.debug(f"Processing {i}/{len(request_ids)}: {request_id}")

        # Get the latest request ID from history
        latest_request_id = get_latest_request_id_from_history(
            request_id, get_chat_host_request_func, args.tenant_name
        )

        # Print the latest request ID to console
        print(f"\n===== Input Request ID: {request_id} =====")
        print(f"Latest Request ID from history: {latest_request_id}")
        print("=" * (len(request_id) + 25))

        # Keep track of successfully processed request IDs
        if not latest_request_id.startswith("[ERROR:"):
            processed_request_ids.append(latest_request_id)

    # Print all latest request IDs at the end (one per line)
    print(
        f"\n\n===== Latest Request IDs ({len(processed_request_ids)} processed successfully) ====="
    )
    for request_id in processed_request_ids:
        print(request_id)


if __name__ == "__main__":
    main()
