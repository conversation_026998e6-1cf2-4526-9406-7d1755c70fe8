body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    color: #333;
}

h1 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

h2 {
    color: #2980b9;
    margin-top: 30px;
}

.summary-section, .command-section, .histogram-section, .pairs-section, .error-section {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.collapsible-section {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-top: 0;
    margin-bottom: 10px;
}

.toggle-icon {
    margin-right: 10px;
    font-size: 14px;
    color: #3498db;
    font-weight: bold;
    font-family: monospace;
    transition: transform 0.3s ease;
}

.section-content {
    overflow: hidden;
    transition: max-height 0.3s ease;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #3498db;
    color: white;
}

tr:hover {
    background-color: #f5f5f5;
}

.bar {
    height: 20px;
    background-color: #3498db;
    border-radius: 3px;
}

.json-container {
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: auto;
    margin: 10px 0;
}

.json-container pre {
    margin: 0;
    padding: 15px;
    white-space: pre-wrap;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    color: #333;
}

.request-id-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.request-id-table th, .request-id-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.request-id-table th {
    background-color: #3498db;
    color: white;
}

.request-id-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.request-id-table tr:hover {
    background-color: #f1f1f1;
}

.request-id-table a {
    color: #0366d6;
    text-decoration: none;
}

.request-id-table a:hover {
    text-decoration: underline;
}

.request-id-table .error-message {
    font-family: monospace;
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #d32f2f;
    font-size: 0.9em;
}

.command-header {
    background-color: #e3f2fd;
    font-weight: bold;
    text-align: center;
    color: #0d47a1;
}

.command-row td {
    background-color: #f5f5f5;
    font-style: italic;
}

.error-analysis-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin-top: 10px;
}

.error-analysis-table th, .error-analysis-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.error-analysis-table th {
    background-color: #3498db;
    color: white;
}

/* Fixed column widths */
.error-analysis-table .col-tool {
    width: 25%;
}

.error-analysis-table .col-next-tool {
    width: 25%;
}

.error-analysis-table .col-success,
.error-analysis-table .col-error,
.error-analysis-table .col-na,
.error-analysis-table .col-total {
    width: 10%;
    text-align: center;
}

.error-analysis-table .col-rate {
    width: 10%;
    text-align: center;
}

.error-analysis-table .tool-row {
    background-color: #f2f2f2;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
}

.error-analysis-table .tool-row:hover {
    background-color: #e0e0e0;
}

.error-analysis-table .next-tool-row {
    background-color: #ffffff;
}

.error-analysis-table .next-tool-row td.indent {
    padding-left: 30px;
}

.toggle-next-tools {
    cursor: pointer;
    margin-right: 5px;
    font-family: monospace;
    font-weight: bold;
    color: #3498db;
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 3px;
    background-color: #e8f4fc;
}

.toggle-next-tools:hover {
    background-color: #d0e9f7;
}

.code-cell {
    font-family: monospace;
    white-space: pre-wrap;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.error-output {
    color: #d32f2f;
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    table {
        font-size: 14px;
    }

    th, td {
        padding: 8px 10px;
    }
}
