"""
HTML report generation for str-replace-editor tool analysis and conversation analysis.
"""

import uuid
import datetime
import json
import html
from pathlib import Path
import pandas as pd
from experimental.ran.utils.git_utils import get_git_username
from experimental.ran.agent.analytics.str_replace_editor_tool_analysis import (
    StrReplaceEditorAnalysisSummary,
    VIEW_RANGE_LEN_BUCKETS,
    STR_REPLACE_ENTRIES_BUCKETS,
)
from experimental.ran.agent.analytics.conversation_analysis import (
    make_success_rate_df,
    make_action_df,
)

# Constants for web server
WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"


def gen_html_report(
    summary: StrReplaceEditorAnalysisSummary, conversation_df: pd.DataFrame
):
    """Generate an HTML report for the str-replace-editor tool analysis and conversation analysis.

    Args:
        summary: The analysis summary to generate a report for
        conversation_df: DataFrame containing conversation analysis data

    Returns:
        The URL to access the report
    """
    # Create a unique identifier for this report
    git_username = get_git_username()
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    random_suffix = str(uuid.uuid4())[:4]
    rel_path = f"{git_username}/str_replace_editor_analysis/{timestamp}_{random_suffix}"

    # Create web server directory
    web_server_output_dir = WEB_SERVER_DIR / rel_path
    web_server_output_dir.mkdir(parents=True, exist_ok=True)

    # Generate HTML content
    html_content = generate_html_content(summary, conversation_df)

    # Save HTML file
    html_path = web_server_output_dir / "index.html"
    with open(html_path, "w") as f:
        f.write(html_content)

    # Generate and save CSS file
    css_content = generate_css_content()
    css_path = web_server_output_dir / "styles.css"
    with open(css_path, "w") as f:
        f.write(css_content)

    # Generate URL
    url = URL_TEMPLATE.format(f"{rel_path}/index.html")
    print(f"\nAnalysis report available at: {url}")

    return url


def generate_html_content(
    summary: StrReplaceEditorAnalysisSummary, conversation_df: pd.DataFrame
) -> str:
    """Generate the HTML content for the report."""
    # Create the HTML head and start of body
    html_parts = []
    html_parts.append("""<!DOCTYPE html>
<html>
<head>
    <title>str-replace-editor Tool Analysis</title>
    <link rel="stylesheet" href="styles.css">
    <script>
        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            const content = section.querySelector('.section-content');
            const icon = section.querySelector('.toggle-icon');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.textContent = '[-]';
            } else {
                content.style.display = 'none';
                icon.textContent = '[+]';
            }
        }

        // Initialize all sections when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Expand all non-JSON sections by default
            const sections = document.querySelectorAll('.collapsible-section');
            sections.forEach(section => {
                const content = section.querySelector('.section-content');
                const icon = section.querySelector('.toggle-icon');
                const isJsonSection = section.classList.contains('json-section');

                if (isJsonSection) {
                    // Collapse JSON sections by default
                    content.style.display = 'none';
                    icon.textContent = '[+]';
                } else {
                    // Expand other sections by default
                    content.style.display = 'block';
                    icon.textContent = '[-]';
                }
            });
        });
    </script>
</head>
<body>
    """)
    section_id = "conversation-analysis-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h1 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[+]</span>Conversation Analysis Report
        </h1>
        <div class="section-content">
    """)

    # Generate conversation analysis HTML
    conversation_html = generate_conversation_analysis_html(conversation_df)
    html_parts.append(conversation_html)

    html_parts.append("""
        </div>
    </div>
    """)

    # Generate str-replace-editor analysis HTML
    section_id = "str-replace-editor-analysis-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h1 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>str-replace-editor Tool Analysis
        </h1>
        <div class="section-content">
    """)
    html_parts.append(generate_str_replace_analysis_html(summary))
    html_parts.append("""
        </div>
    </div>
    """)

    # Close the HTML body and document
    html_parts.append("""</body>
</html>""")

    return "".join(html_parts)


def generate_str_replace_analysis_html(summary: StrReplaceEditorAnalysisSummary) -> str:
    # Initialize the HTML parts list
    html_parts = []

    # Add summary section
    html_parts.append("""<div id="summary-section" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('summary-section')">
            <span class="toggle-icon">[-]</span>Summary
        </h2>
        <div class="section-content">
    """)

    # Add date range information if available
    date_range_html = ""
    if summary.from_date and summary.to_date:
        date_range_html = f"""<p><strong>Date range:</strong> {summary.from_date.strftime('%Y-%m-%d %H:%M:%S')} to {summary.to_date.strftime('%Y-%m-%d %H:%M:%S')}</p>"""
    elif summary.from_date:
        date_range_html = f"""<p><strong>From date:</strong> {summary.from_date.strftime('%Y-%m-%d %H:%M:%S')}</p>"""
    elif summary.to_date:
        date_range_html = f"""<p><strong>To date:</strong> {summary.to_date.strftime('%Y-%m-%d %H:%M:%S')}</p>"""

    html_parts.append(f"""{date_range_html}
        <p><strong>Total calls:</strong> {summary.total_calls}</p>
        <p><strong>Success rate:</strong> {summary.success_rate:.2%}</p>

        </div>
    </div>
    """)

    # Add token statistics section
    section_id = "token-statistics-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Token Statistics
        </h2>
        <div class="section-content">
            <h3>Text Token Statistics</h3>
            <table>
                <tr>
                    <th>Message Type</th>
                    <th>Token Count</th>
                    <th>Percentage</th>
                </tr>
                <tr>
                    <td>User Messages</td>
                    <td>{summary.text_token_stats.get('user_messages_tokens', 0):,}</td>
                    <td>{summary.text_token_stats.get('user_messages_tokens_pct', 0):.1f}%</td>
                </tr>
                <tr>
                    <td>Agent Messages</td>
                    <td>{summary.text_token_stats.get('agent_messages_tokens', 0):,}</td>
                    <td>{summary.text_token_stats.get('agent_messages_tokens_pct', 0):.1f}%</td>
                </tr>
            </table>

            <h3>Detailed Token Statistics by Tool</h3>
            <p><strong>Note:</strong>
                The "Total Prompt Tokens Used" column represents the total token quota used by or because of all tool calls of this tool.<br>
                "because of" means every time this tool call tokens are included in the prompt of following turns, we count it as used by this tool as well.
            </p>
            <table>
                <tr>
                    <th>Tool</th>
                    <th>Tool Input Tokens</th>
                    <th>Tool Input %</th>
                    <th>Tool Output Tokens</th>
                    <th>Tool Output %</th>
                    <th>History Tokens</th>
                    <th>History %</th>
                    <th>Agent Message Tokens</th>
                    <th>Agent Message %</th>
                    <th>Total Prompt Tokens Used</th>
                    <th>Total Prompt Tokens Used%</th>
                </tr>""")

    # Add rows for each tool
    for tool_name, stats in sorted(
        summary.tool_to_token_stats.items(),
        key=lambda x: x[1].total_prompt_token_used,
        reverse=True,
    ):
        # Calculate percentages relative to sum_token_stats
        tool_input_pct = (
            (
                stats.num_tokens_in_tool_input
                / summary.sum_token_stats.num_tokens_in_tool_input
                * 100
            )
            if summary.sum_token_stats.num_tokens_in_tool_input > 0
            else 0
        )
        tool_output_pct = (
            (
                stats.num_tokens_in_tool_output
                / summary.sum_token_stats.num_tokens_in_tool_output
                * 100
            )
            if summary.sum_token_stats.num_tokens_in_tool_output > 0
            else 0
        )
        history_pct = (
            (
                stats.num_tokens_in_history
                / summary.sum_token_stats.num_tokens_in_history
                * 100
            )
            if summary.sum_token_stats.num_tokens_in_history > 0
            else 0
        )
        agent_message_pct = (
            (
                stats.num_tokens_in_agent_message
                / summary.sum_token_stats.num_tokens_in_agent_message
                * 100
            )
            if summary.sum_token_stats.num_tokens_in_agent_message > 0
            else 0
        )
        total_prompt_pct = (
            (
                stats.total_prompt_token_used
                / summary.sum_token_stats.total_prompt_token_used
                * 100
            )
            if summary.sum_token_stats.total_prompt_token_used > 0
            else 0
        )

        html_parts.append(f"""<tr>
            <td>{tool_name}</td>
            <td>{stats.num_tokens_in_tool_input:,}</td>
            <td>{tool_input_pct:.1f}%</td>
            <td>{stats.num_tokens_in_tool_output:,}</td>
            <td>{tool_output_pct:.1f}%</td>
            <td>{stats.num_tokens_in_history:,}</td>
            <td>{history_pct:.1f}%</td>
            <td>{stats.num_tokens_in_agent_message:,}</td>
            <td>{agent_message_pct:.1f}%</td>
            <td>{stats.total_prompt_token_used:,}</td>
            <td>{total_prompt_pct:.1f}%</td>
        </tr>""")

    # Add sum row
    html_parts.append(f"""<tr class="total-row">
        <td><strong>Total</strong></td>
        <td>{summary.sum_token_stats.num_tokens_in_tool_input:,}</td>
        <td>100.0%</td>
        <td>{summary.sum_token_stats.num_tokens_in_tool_output:,}</td>
        <td>100.0%</td>
        <td>{summary.sum_token_stats.num_tokens_in_history:,}</td>
        <td>100.0%</td>
        <td>{summary.sum_token_stats.num_tokens_in_agent_message:,}</td>
        <td>100.0%</td>
        <td>{summary.sum_token_stats.total_prompt_token_used:,}</td>
        <td>100.0%</td>
    </tr>""")

    html_parts.append("""</table>
        </div>
    </div>
    """)

    # Add command distribution section
    section_id = "command-distribution-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Command Distribution
        </h2>
        <div class="section-content">
            <table>
                <tr>
                    <th>Command</th>
                    <th>Count</th>
                    <th>Percentage</th>
                </tr>
    """)

    for command, count in sorted(
        summary.command_counts.items(), key=lambda x: x[1], reverse=True
    ):
        percentage = count / summary.total_calls * 100 if summary.total_calls > 0 else 0
        html_parts.append(f"""<tr>
                <td>{command}</td>
                <td>{count}</td>
                <td>{percentage:.1f}%</td>
            </tr>
        """)

    html_parts.append("""</table>
        </div>
    </div>
    """)

    # Add view range histogram section
    section_id = "view-range-histogram-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>View Range Length Histogram
        </h2>
        <div class="section-content">
            <table>
                <tr>
                    <th>Range</th>
                    <th>Count</th>
                    <th>Visualization</th>
                </tr>
    """)

    max_count = max(summary.view_range_len_hist) if summary.view_range_len_hist else 0
    total_count = sum(summary.view_range_len_hist)

    for i, bucket in enumerate(VIEW_RANGE_LEN_BUCKETS):
        count = summary.view_range_len_hist[i]
        bar_width = int((count / max_count * 100) if max_count > 0 else 0)

        if i == len(VIEW_RANGE_LEN_BUCKETS) - 1 or bucket == -1:
            range_text = f"> {VIEW_RANGE_LEN_BUCKETS[i-1]} lines"
        elif i == 0:
            range_text = f"<= {bucket} lines"
        else:
            range_text = f"{VIEW_RANGE_LEN_BUCKETS[i-1]} < lines <= {bucket}"

        html_parts.append(f"""<tr>
                <td>{range_text}</td>
                <td>{count} ({count / total_count * 100:.1f}%)</td>
                <td><div class="bar" style="width: {bar_width}%"></div></td>
            </tr>
        """)

    html_parts.append("""</table>
            </div>
        </div>
    """)

    # Add str_replace_entries histogram section
    section_id = "str-replace-entries-histogram-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Str Replace Entries Histogram
        </h2>
        <div class="section-content">
            <table>
                <tr>
                    <th>Range</th>
                    <th>Count</th>
                    <th>Visualization</th>
                </tr>
    """)

    max_count = (
        max(summary.str_replace_entries_hist) if summary.str_replace_entries_hist else 0
    )
    total_count = sum(summary.str_replace_entries_hist)

    for i, bucket in enumerate(STR_REPLACE_ENTRIES_BUCKETS):
        count = summary.str_replace_entries_hist[i]
        bar_width = int((count / max_count * 100) if max_count > 0 else 0)

        if i == len(STR_REPLACE_ENTRIES_BUCKETS) - 1 or bucket == -1:
            range_text = f"> {STR_REPLACE_ENTRIES_BUCKETS[i-1]} entries"
        elif i == 0:
            range_text = f"<= {bucket} entries"
        else:
            range_text = f"{STR_REPLACE_ENTRIES_BUCKETS[i-1]} < entries <= {bucket}"

        html_parts.append(f"""<tr>
                <td>{range_text}</td>
                <td>{count} ({count / total_count * 100:.1f}%)</td>
                <td><div class="bar" style="width: {bar_width}%"></div></td>
            </tr>
        """)

    html_parts.append("""</table>
            </div>
        </div>
    """)

    # Add error statistics section if there are any errors
    if summary.error_counts:
        section_id = "error-statistics-section"
        html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
            <h2 class="section-header" onclick="toggleSection('{section_id}')">
                <span class="toggle-icon">[-]</span>Error Statistics
            </h2>
            <div class="section-content">
                <table>
                    <tr>
                        <th>Error Type</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
        """)

        total_errors = sum(summary.error_counts.values())
        for error_type, count in sorted(
            summary.error_counts.items(), key=lambda x: x[1], reverse=True
        ):
            percentage = count / total_errors * 100 if total_errors > 0 else 0
            html_parts.append(f"""<tr>
                    <td>{error_type}</td>
                    <td>{count}</td>
                    <td>{percentage:.1f}%</td>
                </tr>
            """)

        html_parts.append("""</table>
                </div>
            </div>
        """)

        # Add error request ID pairs section
        for error_type, request_id_pairs in summary.error_request_id_pairs.items():
            if request_id_pairs:
                # Format the pairs as a list of objects with tool_use_id and tool_response_id fields
                formatted_pairs = [
                    {
                        "tool_use_id": pair[0],
                        "tool_response_id": pair[1],
                        "error_message": pair[2][:200] + "..."
                        if len(pair[2]) > 200
                        else pair[2],
                        "tool_use_url": f"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{pair[0]}",
                        "tool_response_url": f"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{pair[1]}",
                    }
                    for pair in request_id_pairs
                ]

                section_id = f"error-pairs-{error_type}-section"
                html_parts.append(f"""<div id="{section_id}" class="collapsible-section json-section">
                    <h3 class="section-header" onclick="toggleSection('{section_id}')">
                        <span class="toggle-icon">[+]</span>Request ID Pairs for Error Type: {error_type}
                    </h3>
                    <div class="section-content">
                        <div class="json-container">
                            <pre>
{json.dumps(formatted_pairs, indent=4)}
                            </pre>
                        </div>
                        <table class="request-id-table">
                            <tr>
                                <th>Tool Use Request ID</th>
                                <th>Tool Response Request ID</th>
                                <th>Error Message</th>
                            </tr>
                """)

                # Add each pair as a row in the table
                for pair in request_id_pairs:
                    tool_use_id = pair[0]
                    tool_response_id = pair[1]
                    # Shorten error message to 200 characters
                    error_message = pair[2]
                    if len(error_message) > 200:
                        error_message = error_message[:200] + "..."

                    html_parts.append(f"""<tr>
                            <td><a href="https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{tool_use_id}" target="_blank">{tool_use_id}</a></td>
                            <td><a href="https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{tool_response_id}" target="_blank">{tool_response_id}</a></td>
                            <td class="error-message">{error_message}</td>
                        </tr>
                    """)

                html_parts.append("""</table>
                        </div>
                    </div>
                """)

    # Add should not use view range request IDs section if there are any
    if summary.should_not_use_view_range_request_ids:
        section_id = "should-not-use-view-range-section"
        html_parts.append(f"""<div id="{section_id}" class="collapsible-section json-section">
            <h2 class="section-header" onclick="toggleSection('{section_id}')">
                <span class="toggle-icon">[+]</span>Views Where We Should Not Have Used View Range Request IDs
            </h2>
            <div class="section-content">
                <div class="json-container">
                    <pre>
""")
        html_parts.append(
            json.dumps(summary.should_not_use_view_range_request_ids, indent=4)
        )

        html_parts.append("""                    </pre>
                </div>
            </div>
        </div>
        """)

    # Add previously retrieved view request IDs section if there are any
    if summary.previously_retrieved_view_request_ids:
        section_id = "previously-retrieved-view-section"
        html_parts.append(f"""<div id="{section_id}" class="collapsible-section json-section">
            <h2 class="section-header" onclick="toggleSection('{section_id}')">
                <span class="toggle-icon">[+]</span>Views of Previously Retrieved Files Request IDs
            </h2>
            <div class="section-content">
                <div class="json-container">
                    <pre>
""")
        html_parts.append(
            json.dumps(summary.previously_retrieved_view_request_ids, indent=4)
        )

        html_parts.append("""                    </pre>
                </div>
            </div>
        </div>
        """)

    # Add large str_replace request IDs section if there are any
    if summary.large_str_replace_request_ids:
        section_id = "large-str-replace-section"
        html_parts.append(f"""<div id="{section_id}" class="collapsible-section json-section">
            <h2 class="section-header" onclick="toggleSection('{section_id}')">
                <span class="toggle-icon">[+]</span>Str Replace Calls With More Than 5 Entries Request IDs
            </h2>
            <div class="section-content">
                <div class="json-container">
                    <pre>
""")
        html_parts.append(json.dumps(summary.large_str_replace_request_ids, indent=4))

        html_parts.append("""                    </pre>
                </div>
                <table class="request-id-table">
                    <tr>
                        <th>Request ID</th>
                        <th>Link</th>
                    </tr>
""")

        # Add each request ID as a row in the table
        for request_id in summary.large_str_replace_request_ids:
            html_parts.append(f"""<tr>
                <td>{request_id}</td>
                <td><a href="https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}" target="_blank">View Request</a></td>
            </tr>
            """)

        html_parts.append("""</table>
            </div>
        </div>
        """)
    return "".join(html_parts)


def generate_conversation_analysis_html(df: pd.DataFrame) -> str:
    """Generate the HTML content for the conversation analysis report.

    Args:
        df: DataFrame containing conversation analysis data

    Returns:
        HTML content as a string
    """
    # Initialize the HTML parts list
    html_parts = []

    # Add summary section
    num_conversations = df.last_request_id.nunique()
    num_rounds = df.round_id.nunique()
    num_turns = len(df[df["message_type"] != "user_message"])

    section_id = "summary-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Summary
        </h2>
        <div class="section-content">
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>Total Conversations</td>
                    <td>{num_conversations}</td>
                </tr>
    """)

    html_parts.append(f"""<tr>
                    <td>Total Rounds</td>
                    <td>{num_rounds}</td>
                </tr>
                <tr>
                    <td>Total Turns</td>
                    <td>{num_turns}</td>
                </tr>
                <tr>
                    <td>Average Rounds per Conversation</td>
                    <td>{num_rounds / num_conversations:.2f}</td>
                </tr>
                <tr>
                    <td>Average Turns per Round</td>
                    <td>{num_turns / num_rounds:.2f}</td>
                </tr>
            </table>
        </div>
    </div>
    """)

    # Add tool usage statistics section
    df_tool_calls = df[df.tool_name.notnull()]
    convs_with_tool_calls = df_tool_calls.last_request_id.nunique()
    rounds_with_tool_calls = df_tool_calls.round_id.nunique()

    section_id = "tool-usage-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Tool Usage Statistics
        </h2>
        <div class="section-content">
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                    <th>Percentage</th>
                </tr>
                <tr>
                    <td>Conversations with Tool Calls</td>
                    <td>{convs_with_tool_calls}</td>
                    <td>{convs_with_tool_calls / num_conversations * 100:.1f}%</td>
                </tr>
                <tr>
                    <td>Rounds with Tool Calls</td>
                    <td>{rounds_with_tool_calls}</td>
                    <td>{rounds_with_tool_calls / num_rounds * 100:.1f}%</td>
                </tr>
            </table>
        </div>
    </div>
    """)

    # Add tool call distribution section
    section_id = "tool-distribution-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Tool Call Distribution
        </h2>
        <div class="section-content">
    """)

    # Get the success rate dataframe
    success_rate_df = make_success_rate_df(
        df_tool_calls, ["tool_name"], "tool_output_is_error"
    )

    # Convert the dataframe to HTML table
    html_parts.append("""<table>
                <tr>
                    <th>Tool</th>
                    <th>Success</th>
                    <th>Error</th>
                    <th>N/A</th>
                    <th>Total</th>
                    <th>Success Rate</th>
                </tr>
    """)

    for tool_name, row in success_rate_df.iterrows():
        html_parts.append(f"""<tr>
                    <td>{tool_name}</td>
                    <td>{row['Success']}</td>
                    <td>{row['Error']}</td>
                    <td>{row['N/A']}</td>
                    <td>{row['Total']}</td>
                    <td>{row['Rate']}</td>
                </tr>
        """)

    html_parts.append("""</table>
        </div>
    </div>
    """)

    # Add character statistics section
    section_id = "char-stats-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Character Statistics per Action (Top 20)
        </h2>
        <div class="section-content">
    """)

    # Get the action dataframe
    action_df = make_action_df(df)
    avg_tool_chars = (
        action_df.groupby("action")["chars"].sum() / df.round_id.nunique()
    ).sort_values(ascending=False)

    # Convert the dataframe to HTML table
    html_parts.append("""<table>
                <tr>
                    <th>Action</th>
                    <th>Average Characters per Round</th>
                </tr>
    """)

    for action, chars in avg_tool_chars.head(20).items():
        html_parts.append(f"""<tr>
                    <td>{action}</td>
                    <td>{chars:.2f}</td>
                </tr>
        """)

    html_parts.append("""</table>
        </div>
    </div>
    """)

    # Add tool call frequency section
    section_id = "tool-frequency-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Tool Call Frequency per Round (Top 20)
        </h2>
        <div class="section-content">
    """)

    # Calculate tool call frequencies
    overall_tool_call_counts = df_tool_calls.round_id.nunique() / num_rounds
    nonnull_tool_calls = df_tool_calls[(df_tool_calls.tool_output_is_error.notnull())]
    nonnull_rate = nonnull_tool_calls.round_id.nunique() / num_rounds
    successful_tool_calls = df_tool_calls[df_tool_calls.tool_output_is_error == False]  # noqa: E712
    success_rate = successful_tool_calls.round_id.nunique() / num_rounds

    html_parts.append(f"""<p>Overall tool call rate: {overall_tool_call_counts:.2%}</p>
            <p>At least one non-NA tool call: {nonnull_rate:.2%}</p>
            <p>At least one successful tool call: {success_rate:.2%}</p>

            <table>
                <tr>
                    <th>Action</th>
                    <th>Frequency</th>
                </tr>
    """)

    tool_call_counts = action_df.groupby("action").round_id.nunique() / num_rounds
    for action, freq in tool_call_counts.sort_values(ascending=False).head(20).items():
        html_parts.append(f"""<tr>
                    <td>{action}</td>
                    <td>{freq:.2%}</td>
                </tr>
        """)

    html_parts.append("""</table>
        </div>
    </div>
    """)

    # Add error analysis section
    section_id = "error-analysis-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Error Analysis
        </h2>
        <div class="section-content">
    """)

    # Filter for error cases
    error_df = df_tool_calls[df_tool_calls.tool_output_is_error == True]  # noqa: E712

    # Create a combined table for next tool error analysis
    html_parts.append("""<h3>Next Tool Error Analysis</h3>""")

    # Create a success rate dataframe for overall statistics (next_tool="ALL")
    overall_success_rate_df = make_success_rate_df(
        error_df,
        ["tool_name"],
        "next_tool_output_is_error",
    )

    # Create a success rate dataframe broken down by next_tool_name
    # First, create a copy of the error_df with both tool_name and next_tool_name as grouping keys
    detailed_success_rate_df = make_success_rate_df(
        error_df,
        ["tool_name", "next_tool_name"],
        "next_tool_output_is_error",
    )

    # Create the table with fixed column widths
    html_parts.append("""<table class="error-analysis-table">
                <tr>
                    <th class="col-tool">Tool</th>
                    <th class="col-next-tool">Next Tool</th>
                    <th class="col-success">Success</th>
                    <th class="col-error">Error</th>
                    <th class="col-na">N/A</th>
                    <th class="col-total">Total</th>
                    <th class="col-rate">Rate</th>
                </tr>
    """)

    # Add rows for each tool
    for tool_name, overall_row in overall_success_rate_df.iterrows():
        tool_name = str(tool_name)
        # Create a unique ID for this tool's collapsible section
        tool_id = f"tool-{tool_name.replace(' ', '-').lower()}"

        success_count = overall_row.get("Success", 0)
        error_count = overall_row.get("Error", 0)
        na_count = overall_row.get("N/A", 0)
        total = overall_row["Total"]
        rate = overall_row["Rate"]

        html_parts.append(f"""<tr class="tool-row" onclick="toggleNextTools('{tool_id}')">
                    <td class="col-tool"><span class="toggle-next-tools" id="toggle-{tool_id}">[+]</span> {tool_name}</td>
                    <td class="col-next-tool">All</td>
                    <td class="col-success">{success_count}</td>
                    <td class="col-error">{error_count}</td>
                    <td class="col-na">{na_count}</td>
                    <td class="col-total">{total}</td>
                    <td class="col-rate">{rate}</td>
                </tr>
        """)

        # Collect all next tools for this tool
        try:
            next_tools = []
            for idx, row in detailed_success_rate_df.iterrows():
                if isinstance(idx, tuple) and len(idx) == 2:
                    t, next_t = idx
                    if t == tool_name and next_t is not None:
                        next_tools.append((next_t, row))

            # Sort by Total (descending)
            next_tools.sort(key=lambda x: x[1].get("Total", 0), reverse=True)

            # Add a row for each next tool
            for next_tool_name, row in next_tools:
                success = row.get("Success", 0)
                error = row.get("Error", 0)
                na = row.get("N/A", 0)
                count = row.get("Total", 0)
                subrate = row.get("Rate", "N/A")

                html_parts.append(f"""<tr class="next-tool-row {tool_id}" style="display: none;">
                            <td class="col-tool indent"></td>
                            <td class="col-next-tool">{next_tool_name}</td>
                            <td class="col-success">{success}</td>
                            <td class="col-error">{error}</td>
                            <td class="col-na">{na}</td>
                            <td class="col-total">{count}</td>
                            <td class="col-rate">{subrate}</td>
                        </tr>
                """)
        except Exception:
            # If there's an error accessing the data, skip this tool
            pass

    html_parts.append("""</table>""")

    # Add JavaScript for toggling next tools
    html_parts.append("""<script>
    function toggleNextTools(toolId) {
        const nextToolRows = document.getElementsByClassName(toolId);
        const toggleIcon = document.getElementById(`toggle-${toolId}`);

        let isVisible = nextToolRows.length > 0 && nextToolRows[0].style.display !== 'none';

        for (let i = 0; i < nextToolRows.length; i++) {
            nextToolRows[i].style.display = isVisible ? 'none' : 'table-row';
        }

        toggleIcon.textContent = isVisible ? '[+]' : '[-]';
    }
    </script>
        </div>
    </div>
    """)

    # Add tool-specific error analysis section
    section_id = "tool-error-section"
    html_parts.append(f"""<div id="{section_id}" class="collapsible-section">
        <h2 class="section-header" onclick="toggleSection('{section_id}')">
            <span class="toggle-icon">[-]</span>Tool-Specific Error Analysis
        </h2>
        <div class="section-content">
    """)

    # Group errors by tool
    tool_errors = {}
    for _, row in error_df.iterrows():
        tool_name = row.tool_name
        if tool_name not in tool_errors:
            tool_errors[tool_name] = []

        tool_errors[tool_name].append(
            {
                "request_id": row.request_id,
                "tool_input": row.tool_input,
                "tool_output": row.tool_output,
                "next_tool_name": row.next_tool_name,
                "next_tool_output_is_error": row.next_tool_output_is_error,
                "last_request_id": row.last_request_id,
            }
        )

    # Create a tab-like interface for each tool
    html_parts.append("""<div class="tool-tabs">
        <div class="tab-buttons">
    """)

    for i, tool_name in enumerate(tool_errors.keys()):
        active_class = "active" if i == 0 else ""
        html_parts.append(
            f"""<button class="tab-button {active_class}" onclick="openToolTab(event, '{tool_name}')">{tool_name}</button>"""
        )

    html_parts.append("""</div>""")

    # Create tab content for each tool
    for i, (tool_name, errors) in enumerate(tool_errors.items()):
        display_style = "block" if i == 0 else "none"
        html_parts.append(f"""<div id="{tool_name}" class="tab-content" style="display: {display_style};">
            <h3>Errors for {tool_name}</h3>
            <p>Total errors: {len(errors)}</p>
            <table class="error-table">
                <tr>
                    <th>Request ID</th>
                    <th>Tool Input</th>
                    <th>Error Output</th>
                    <th>Next Tool</th>
                    <th>Next Tool Error</th>
                    <th>Request Link</th>
                    <th>Conversation Link</th>
                </tr>
        """)

        for error in errors:
            next_tool = (
                error["next_tool_name"]
                if error["next_tool_name"] is not None
                else "None"
            )
            # Truncate and escape long outputs for display
            tool_input = error["tool_input"]
            if len(tool_input) > 100:
                tool_input = tool_input[:100] + "..."
            tool_input = html.escape(tool_input)

            tool_output = error["tool_output"]
            if len(tool_output) > 100:
                tool_output = tool_output[:100] + "..."
            tool_output = html.escape(tool_output)

            # Get next tool error status
            next_tool_error = error.get("next_tool_output_is_error", "N/A")
            if next_tool_error is True:
                next_tool_error_display = "Error"
            elif next_tool_error is False:
                next_tool_error_display = "Success"
            else:
                next_tool_error_display = "N/A"

            # Get conversation ID from the request ID
            conversation_id = error.get("last_request_id", error["request_id"])

            html_parts.append(f"""<tr>
                    <td>{error["request_id"]}</td>
                    <td class="code-cell">{tool_input}</td>
                    <td class="code-cell error-output">{tool_output}</td>
                    <td>{next_tool}</td>
                    <td>{next_tool_error_display}</td>
                    <td><a href="https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{error["request_id"]}" target="_blank">View Request</a></td>
                    <td><a href="https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{conversation_id}" target="_blank">View Conversation</a></td>
                </tr>
            """)

        html_parts.append("""</table>
        </div>""")

    html_parts.append("""</div>
        </div>
    </div>
    """)

    # Add JavaScript for tab functionality
    html_parts.append("""<script>
    function openToolTab(evt, toolName) {
        // Hide all tab content
        var tabContents = document.getElementsByClassName("tab-content");
        for (var i = 0; i < tabContents.length; i++) {
            tabContents[i].style.display = "none";
        }

        // Remove active class from all tab buttons
        var tabButtons = document.getElementsByClassName("tab-button");
        for (var i = 0; i < tabButtons.length; i++) {
            tabButtons[i].className = tabButtons[i].className.replace(" active", "");
        }

        // Show the current tab and add active class to the button
        document.getElementById(toolName).style.display = "block";
        evt.currentTarget.className += " active";
    }
    </script>""")

    # Close the HTML body and document
    html_parts.append("""</body>
</html>""")

    return "".join(html_parts)


def generate_css_content() -> str:
    """Read the CSS content from the styles.css file."""
    css_path = Path(__file__).parent / "styles.css"
    with open(css_path, "r") as f:
        return f.read()
