from dataclasses import dataclass, field
import json
import logging
from typing import Callable, Iterable, Dict

from more_itertools import unique

from base.prompt_format.common import (
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
)
from base.prompt_format_chat.lib.chat_history_builder import format_chat_history
import services.chat_host.chat_pb2 as chat_pb2
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.chat_host.chat_proto_util import (
    convert_history as services_convert_history,
)
# Removed str_replace_editor specific import

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Initialize the token counter
_TOKEN_COUNTER = ClaudeTokenCounter()


def count_tokens(text: str) -> int:
    """Count the number of tokens in a text string using <PERSON>'s tokenizer."""
    if not text:
        return 0
    return _TOKEN_COUNTER.count_tokens(text)


def extract_tool_use(
    response_nodes: Iterable[ChatResultNode],
) -> ChatResultToolUse | None:
    for node in response_nodes:
        if node.type == ChatResultNodeType.TOOL_USE:
            return node.tool_use
    return None


@dataclass
class ToolCallAnalysis:
    # Generic tool call analysis fields
    tool_name: str | None = None
    command: str | None = None

    # Error classification fields
    error_type: str | None = None  # Type of error if the tool call failed
    error_message: str | None = None  # Full error message if the tool call failed


@dataclass
class TokenStats:
    num_tokens_in_tool_input: int = 0
    num_tokens_in_tool_output: int = 0
    num_tokens_in_history: int = 0
    num_tokens_in_agent_message: int = 0

    # sum of num_tokens_in_tool_input, num_tokens_in_tool_output, num_tokens_in_agent_message
    # not including num_tokens_in_history
    num_tokens_total: int = 0

    # num_tokens_total when tool output is truncated
    num_tokens_total_truncated: int = 0

    # num times this tool call's tokens in full are counted in the prompt_tokens of following turns
    num_following_turns_using_full_output: int = 0
    # num times this tool call's tokens with truncated output are counted in the prompt_tokens of following turns
    num_following_turns_using_truncated_output: int = 0

    # num_tokens_total + num_tokens_in_history +
    # num_tokens_total * num_following_turns_using_full_output +
    # num_tokens_total_truncated * num_following_turns_using_truncated_output
    total_prompt_token_used: int = 0

    def init(
        self,
        tool_use: ChatResultToolUse,
        tool_result: ChatRequestToolResult,
        agent_message: str,
        tool_use_request: chat_pb2.ChatRequest | None,
    ):
        if tool_use_request is None:
            return

        # Calculate tokens in tool input
        if tool_use and tool_use.input:
            input_json = json.dumps(tool_use.input)
            self.num_tokens_in_tool_input = count_tokens(input_json)

        # Calculate tokens in tool output
        if tool_result and tool_result.content:
            self.num_tokens_in_tool_output = count_tokens(tool_result.content)

        # Calculate tokens in agent message
        if agent_message:
            self.num_tokens_in_agent_message = count_tokens(agent_message)

        chat_history = services_convert_history(tool_use_request.chat_history)
        self.num_tokens_in_history = format_chat_history(
            chat_history=chat_history,
            token_counter=_TOKEN_COUNTER,
            token_budget=1000000,
        )[1]

        # Calculate total tokens (excluding history)
        self.num_tokens_total = (
            self.num_tokens_in_tool_input
            + self.num_tokens_in_tool_output
            + self.num_tokens_in_agent_message
        )

        self.num_tokens_total_truncated = (
            self.num_tokens_in_tool_input
            + truncated_tool_result_num_tokens()
            + self.num_tokens_in_agent_message
        )

        self.num_following_turns_using_full_output = 0
        self.num_following_turns_using_truncated_output = 0
        self.total_prompt_token_used = 0

    def update_tokens_used(self, was_tool_output_truncated: bool) -> None:
        if was_tool_output_truncated:
            self.num_following_turns_using_truncated_output += 1
        else:
            self.num_following_turns_using_full_output += 1

        self.total_prompt_token_used = (
            self.num_tokens_total
            + self.num_tokens_in_history
            + (self.num_tokens_total * self.num_following_turns_using_full_output)
            + (
                self.num_tokens_total_truncated
                * self.num_following_turns_using_truncated_output
            )
        )

    def add(self, other: "TokenStats") -> None:
        self.num_tokens_in_tool_input += other.num_tokens_in_tool_input
        self.num_tokens_in_tool_output += other.num_tokens_in_tool_output
        self.num_tokens_in_history += other.num_tokens_in_history
        self.num_tokens_in_agent_message += other.num_tokens_in_agent_message
        self.num_tokens_total += other.num_tokens_total
        self.num_tokens_total_truncated += other.num_tokens_total_truncated
        self.num_following_turns_using_full_output += (
            other.num_following_turns_using_full_output
        )
        self.num_following_turns_using_truncated_output += (
            other.num_following_turns_using_truncated_output
        )
        self.total_prompt_token_used += other.total_prompt_token_used


@dataclass
class ToolCall:
    tool_use_request_id: str
    tool_use_request: chat_pb2.ChatRequest | None
    tool_use: ChatResultToolUse

    tool_result_request_id: str | None = None
    tool_result_request: chat_pb2.ChatRequest | None = None
    tool_result: ChatRequestToolResult | None = None

    analysis: ToolCallAnalysis | None = None
    token_stats: TokenStats = field(default_factory=TokenStats)


@dataclass
class AgentTurn:
    message: str
    request_id: str
    request: chat_pb2.ChatRequest | None
    tool_call: ToolCall | None = None
    message_token_count: int = 0  # Number of tokens in the agent message


@dataclass
class AgentRound:
    """
    One round in a conversation between user and agent.

    A round starts with a user message followed by a sequence of agent turns.
    The round ends when the agent has completed its task(meaning its last turn does not have a tool call)
    """

    user_message: str
    user_message_request_id: str
    user_message_request: chat_pb2.ChatRequest | None
    agent_turns: list[AgentTurn] = field(default_factory=list)
    user_message_token_count: int = 0  # Number of tokens in the user message


@dataclass
class ExchangeData:
    request_id: str | None = None

    user_message: str | None = None
    tool_result: ChatRequestToolResult | None = None

    agent_response: str | None = None
    tool_use: ChatResultToolUse | None = None


def convert_history(history: Iterable[chat_pb2.Exchange]) -> list[ExchangeData]:
    exchange_data_list = []
    logger.debug("Converting history")
    for exchange in history:
        logger.debug(f"Processing exchange {exchange.request_id}")
        request_nodes = list(exchange.request_nodes)

        if len(request_nodes) == 0 and exchange.request_message:
            request_nodes = [
                chat_pb2.ChatRequestNode(
                    id=0,
                    type=chat_pb2.ChatRequestNodeType.TEXT,
                    text_node=chat_pb2.ChatRequestText(
                        content=exchange.request_message
                    ),
                )
            ]

        text_nodes = [
            node
            for node in request_nodes
            if node.type == chat_pb2.ChatRequestNodeType.TEXT
        ]
        tool_result_nodes = [
            node
            for node in request_nodes
            if node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT
        ]

        # there is a bug on the client that causes TEXT nodes to be duplicated
        # we should fix the client, but for now we just dedup here
        text_nodes = list(
            unique(text_nodes, key=lambda node: node.text_node.content.strip())
        )

        assert len(text_nodes) <= 1
        assert len(tool_result_nodes) <= 1

        assert len(text_nodes) + len(tool_result_nodes) == 1

        request_node = text_nodes[0] if text_nodes else tool_result_nodes[0]

        user_message = None
        tool_result_node = None
        if request_node.type == chat_pb2.ChatRequestNodeType.TEXT:
            user_message = (
                request_node.text_node.content if request_node.text_node else None
            )
        elif request_node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
            tool_result_node = ChatRequestToolResult(
                tool_use_id=request_node.tool_result_node.tool_use_id,
                content=request_node.tool_result_node.content,
                is_error=request_node.tool_result_node.is_error,
                request_id=exchange.request_id,
            )

        agent_response = ""
        tool_use = None
        for response_node in exchange.response_nodes:
            if response_node.type == ChatResultNodeType.RAW_RESPONSE:
                agent_response += response_node.content
            elif response_node.type == ChatResultNodeType.TOOL_USE:
                tool_use = ChatResultToolUse(
                    tool_use_id=response_node.tool_use.tool_use_id,
                    name=response_node.tool_use.tool_name,
                    input=json.loads(response_node.tool_use.input_json),
                )

        exchange_data_list.append(
            ExchangeData(
                request_id=exchange.request_id,
                user_message=user_message,
                tool_result=tool_result_node,
                agent_response=agent_response,
                tool_use=tool_use,
            )
        )
    return exchange_data_list


def is_tool_result_truncated(tool_result_node: chat_pb2.ChatRequestToolResult) -> bool:
    return tool_result_node.content.startswith("[Truncated...")


def truncated_tool_result_num_tokens():
    return count_tokens("[Truncated...re-run tool if you need to see output again.]")


@dataclass
class Conversation:
    last_request_id: str
    agent_rounds: list[AgentRound]
    tool_result_request_id_to_turn: Dict[str, AgentTurn] = field(default_factory=dict)

    def calculate_token_stats(self) -> None:
        def update_prev_turns(request: chat_pb2.ChatRequest | None) -> None:
            if request is None:
                return
            for exchange in request.chat_history:
                turn = self.tool_result_request_id_to_turn.get(exchange.request_id)
                if not turn or not turn.tool_call:
                    continue
                request_nodes = list(exchange.request_nodes)
                tool_result_nodes = [
                    node
                    for node in request_nodes
                    if node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT
                ]
                if len(tool_result_nodes) != 1:
                    breakpoint()

                assert len(tool_result_nodes) == 1
                turn.tool_call.token_stats.update_tokens_used(
                    is_tool_result_truncated(tool_result_nodes[0].tool_result_node)
                )

        for round in self.agent_rounds:
            update_prev_turns(round.user_message_request)
            for turn in round.agent_turns:
                if round.user_message_request_id != turn.request_id:
                    update_prev_turns(turn.request)
                if not turn.tool_call or not turn.tool_call.tool_result:
                    continue
                turn.tool_call.token_stats.init(
                    tool_use=turn.tool_call.tool_use,
                    tool_result=turn.tool_call.tool_result,
                    agent_message=turn.message,
                    tool_use_request=turn.tool_call.tool_use_request,
                )

    def get_token_statistics(self) -> Dict[str, Dict[str, int]]:
        """Calculate token statistics for the conversation.

        Returns:
            A dictionary with token statistics for each tool and message type.
            Format: {
                'tools': {
                    'tool_name': {
                        'prompt_tokens': int,  # Tokens sent TO the model (tool outputs)
                        'completion_tokens': int,  # Tokens generated BY the model (tool inputs)
                        'prompt_tokens_pct': float,
                        'completion_tokens_pct': float,
                        'call_count': int
                    }
                },
                'text': {
                    'user_messages_tokens': int,  # Tokens sent TO the model
                    'agent_messages_tokens': int,  # Tokens generated BY the model
                    'user_messages_tokens_pct': float,
                    'agent_messages_tokens_pct': float
                },
                'total_tokens': int
            }
        """
        # Initialize statistics
        stats = {
            "tools": {},
            "text": {
                "user_messages_tokens": 0,
                "agent_messages_tokens": 0,
            },
            "total_tokens": 0,
        }

        # Calculate token counts for each tool and message type
        for round in self.agent_rounds:
            # Count user message tokens
            user_tokens = count_tokens(round.user_message)
            stats["text"]["user_messages_tokens"] += user_tokens
            stats["total_tokens"] += user_tokens

            for turn in round.agent_turns:
                # Count agent message tokens
                agent_tokens = count_tokens(turn.message)
                stats["text"]["agent_messages_tokens"] += agent_tokens
                stats["total_tokens"] += agent_tokens

                # Count tool tokens
                if turn.tool_call and turn.tool_call.tool_use:
                    tool_name = turn.tool_call.tool_use.name

                    # Initialize tool stats if not already present
                    if tool_name not in stats["tools"]:
                        stats["tools"][tool_name] = {
                            "prompt_tokens": 0,  # Tokens sent TO the model (tool outputs)
                            "completion_tokens": 0,  # Tokens generated BY the model (tool inputs)
                            "call_count": 0,
                        }

                    # Count completion tokens (generated BY the model - tool inputs)
                    input_json = json.dumps(turn.tool_call.tool_use.input)
                    completion_tokens = count_tokens(input_json)
                    stats["tools"][tool_name]["completion_tokens"] += completion_tokens
                    stats["total_tokens"] += completion_tokens

                    # Count prompt tokens (sent TO the model - tool outputs)
                    prompt_tokens = 0
                    if turn.tool_call.tool_result:
                        prompt_tokens = count_tokens(turn.tool_call.tool_result.content)
                        stats["tools"][tool_name]["prompt_tokens"] += prompt_tokens
                        stats["total_tokens"] += prompt_tokens

                    # Increment call count
                    stats["tools"][tool_name]["call_count"] += 1

        # Calculate percentages
        total_tokens = stats["total_tokens"]
        if total_tokens > 0:
            stats["text"]["user_messages_tokens_pct"] = (
                stats["text"]["user_messages_tokens"] / total_tokens
            ) * 100
            stats["text"]["agent_messages_tokens_pct"] = (
                stats["text"]["agent_messages_tokens"] / total_tokens
            ) * 100

            for tool_name in stats["tools"]:
                stats["tools"][tool_name]["prompt_tokens_pct"] = (
                    stats["tools"][tool_name]["prompt_tokens"] / total_tokens
                ) * 100
                stats["tools"][tool_name]["completion_tokens_pct"] = (
                    stats["tools"][tool_name]["completion_tokens"] / total_tokens
                ) * 100

        return stats

    @classmethod
    def from_chat_request(
        cls,
        request_id: str,
        chat_request: chat_pb2.ChatRequest,
        chat_response: chat_pb2.ChatResponse,
        get_chat_host_request_func: Callable[[str], chat_pb2.ChatRequest | None],
    ) -> "Conversation":
        # Initialize chat_history
        if hasattr(chat_request, "chat_history"):
            chat_history = list(chat_request.chat_history)
        else:
            chat_history = []

        current_exchange = chat_pb2.Exchange(
            request_id=request_id,
            request_message=chat_request.message,
            request_nodes=list(chat_request.nodes),
            response_nodes=list(chat_response.nodes),
        )
        chat_history.append(current_exchange)

        chat_history = convert_history(chat_history)

        # Process the chat history to create agent rounds
        agent_rounds = []
        tool_result_request_id_to_turn = {}

        current_agent_round = None
        logger.debug("Processing chat history")
        for exchange in chat_history:
            assert exchange.request_id is not None
            # Determine exchange type: either a user message starting a new round,
            # or a tool response to a previous tool call. This affects how we
            # process the current exchange and update the conversation structure.
            if exchange.user_message is not None:
                # If we have a user message, we are starting a new agent round

                if current_agent_round:
                    # current agent round was interrupted
                    agent_rounds.append(current_agent_round)

                user_message_request = get_chat_host_request_func(exchange.request_id)
                current_agent_round = AgentRound(
                    user_message=exchange.user_message,
                    user_message_request_id=exchange.request_id,
                    user_message_request=user_message_request,
                )
            else:
                # Otherwise, we are in the middle of an agent round.
                # This exchange contains a tool response to the tool call
                # made by the model in the previous exchange
                assert current_agent_round is not None
                assert current_agent_round.agent_turns
                turn = current_agent_round.agent_turns[-1]
                assert turn.tool_call is not None
                tool_call = turn.tool_call
                tool_call.tool_result = exchange.tool_result
                tool_call.tool_result_request_id = exchange.request_id
                tool_call.tool_result_request = get_chat_host_request_func(
                    exchange.request_id
                )
                tool_result_request_id_to_turn[exchange.request_id] = turn

            user_message_request = get_chat_host_request_func(exchange.request_id)
            turn = AgentTurn(
                request_id=exchange.request_id,
                request=user_message_request,
                message=exchange.agent_response or "",
            )
            current_agent_round.agent_turns.append(turn)
            if exchange.tool_use is None:
                # If the agent didn't make a tool call, this round is complete.
                # We add the current round to our collection and reset for the next round.
                agent_rounds.append(current_agent_round)
                current_agent_round = None
            else:
                turn.tool_call = ToolCall(
                    tool_use_request_id=exchange.request_id,
                    tool_use_request=user_message_request,
                    tool_use=exchange.tool_use,
                )

        if current_agent_round is not None:
            agent_rounds.append(current_agent_round)

        # Create the conversation object
        conversation = cls(
            last_request_id=request_id,
            agent_rounds=agent_rounds,
            tool_result_request_id_to_turn=tool_result_request_id_to_turn,
        )

        # Calculate token statistics for the conversation
        conversation.calculate_token_stats()

        return conversation
