import datetime
import logging
from typing import Optional

from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DatasetTenant

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Constants
CACHE_DIRECTORY = "/home/<USER>/ri_cache"


def get_agent_conv_last_request_ids(
    tenant: DatasetTenant,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> list[str]:
    """Get the last request_id for each conversation in the given time range.

    Args:
        from_datetime: Start of the time range (inclusive). If None, no lower bound.
        to_datetime: End of the time range (inclusive). If None, no upper bound.
        tenant: The tenant to filter by.

    Returns:
        List of request_ids for the last request in each conversation.
    """
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Build time filter conditions
    time_conditions = []
    if from_datetime:
        time_conditions.append(f"time >= TIMESTAMP('{from_datetime.isoformat()}')")
    if to_datetime:
        time_conditions.append(f"time <= TIMESTAMP('{to_datetime.isoformat()}')")
    time_filter = " AND ".join(time_conditions) if time_conditions else "1=1"

    # Query to get the last request_id for each conversation
    query = f"""
    WITH agent_requests AS (
        SELECT
            request_id,
            time,
            JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') as conversation_id
        FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
        WHERE tenant = '{tenant.name}' AND {time_filter}
        AND JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') IS NOT NULL
    ),
    latest_requests AS (
        SELECT
            conversation_id,
            MAX(time) as latest_time
        FROM agent_requests
        GROUP BY conversation_id
    )
    SELECT ar.request_id
    FROM agent_requests ar
    JOIN latest_requests lr
    ON ar.conversation_id = lr.conversation_id AND ar.time = lr.latest_time
    ORDER BY ar.time DESC
    """

    rows = bigquery_client.query_and_wait(query)
    return [row.request_id for row in rows]


def get_requests_with_launch_process(
    tenant: DatasetTenant,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> list[str]:
    """Get request_ids for requests that contain LaunchProcess tool calls.

    Args:
        tenant: The tenant to filter by.
        from_datetime: Start of the time range (inclusive). If None, no lower bound.
        to_datetime: End of the time range (inclusive). If None, no upper bound.

    Returns:
        List of request_ids that have LaunchProcess tool calls.
    """
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Build time filter conditions
    time_conditions = []
    if from_datetime:
        time_conditions.append(f"time >= TIMESTAMP('{from_datetime.isoformat()}')")
    if to_datetime:
        time_conditions.append(f"time <= TIMESTAMP('{to_datetime.isoformat()}')")
    time_filter = " AND ".join(time_conditions) if time_conditions else "1=1"

    # Query to find requests with LaunchProcess tool calls
    # Note: We'll filter for staging environment in the conversation analysis step
    query = f"""
    SELECT DISTINCT
        request_id,
        time
    FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
    WHERE tenant = '{tenant.name}'
    AND {time_filter}
    AND (
        -- Check for LaunchProcess tool calls in the JSON
        JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') = 'launch-process'
        OR JSON_EXTRACT_SCALAR(sanitized_json, '$.name') = 'launch-process'
    )
    ORDER BY time DESC
    """

    logger.info("Querying for requests with LaunchProcess tool calls...")
    rows = bigquery_client.query_and_wait(query)
    request_ids = [row.request_id for row in rows]
    logger.info(f"Found {len(request_ids)} requests with LaunchProcess tool calls")
    return request_ids
