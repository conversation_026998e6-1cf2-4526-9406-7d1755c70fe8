# Agent Analytics Module

This module provides tools for analyzing agent conversations and tool usage patterns. It helps identify patterns, inefficiencies, and potential improvements in how agents interact with tools.

## Overview

The analytics module consists of several components:

1. **Conversation Data Model**: Structures for representing agent conversations, rounds, turns, and tool calls
2. **BigQuery Integration**: Utilities for fetching conversation data from BigQuery
3. **Analysis Tools**: Functions for analyzing tool usage patterns and specific tool behaviors
4. **LaunchProcess Analysis**: Tools for finding and analyzing staging environment requests with LaunchProcess tool calls

## Key Components

### Conversation Model (`conversation.py`)

Defines the data structures for representing agent conversations:

- `Conversation`: Represents a full conversation with a sequence of agent rounds
- `AgentRound`: A round starting with a user message followed by agent turns
- `AgentTurn`: A single agent response, potentially with a tool call
- `ToolCall`: Represents a tool call with request, response, and analysis data
- `ToolCallAnalysis`: Generic analysis data for tool calls

### BigQuery Utilities (`big_query_utils.py`)

Provides functions for fetching conversation data from BigQuery:

- `get_agent_conv_last_request_ids()`: Gets the last request ID for each conversation in a given time range
- `get_staging_requests_with_launch_process()`: Gets request IDs for staging environment requests that contain LaunchProcess tool calls

### LaunchProcess Analysis (`launch_process_analysis.py`)

Analyzes usage patterns of the LaunchProcess tool:

- `find_launch_process_requests()`: Finds request IDs that contain LaunchProcess tool calls
- `analyze_launch_process_conversations()`: Analyzes conversations for LaunchProcess tool usage
- `print_launch_process_analysis()`: Prints the analysis results to the console

Key metrics tracked:
- Total conversations analyzed
- Conversations with LaunchProcess tool calls
- Total LaunchProcess tool calls
- Success rates for LaunchProcess calls
- Request IDs containing LaunchProcess tool calls

### Staging LaunchProcess Finder (`find_staging_launch_process.py`)

Main script for finding staging environment requests with LaunchProcess tool calls:

- Queries BigQuery for staging environment requests
- Filters for requests containing LaunchProcess tool calls
- Processes conversations to extract detailed analysis
- Returns list of matching request IDs

## Usage

### General Analysis

To run general conversation analysis:

```bash
# Process data from the last 24 hours
python -m experimental.ran.agent.analytics.run_analysis --last-hours 24 --limit 100

# Process data from the last 7 days
python -m experimental.ran.agent.analytics.run_analysis --last-days 7 --limit 100
```

### Finding Staging LaunchProcess Requests

To find staging environment requests with LaunchProcess tool calls:

```bash
# Find staging requests with LaunchProcess from the last 7 days
python -m experimental.ran.agent.analytics.find_staging_launch_process --last-days 7

# Find staging requests with LaunchProcess from a specific date range
python -m experimental.ran.agent.analytics.find_staging_launch_process --from-date 2024-01-01T00:00:00 --to-date 2024-01-07T23:59:59
```

Command-line options:
- `--from-date`: Start date in ISO format (YYYY-MM-DDTHH:MM:SS)
- `--to-date`: End date in ISO format (YYYY-MM-DDTHH:MM:SS)
- `-ld, --last-days`: Process data from the last N days (default: 7)
- `--tenant-name`: Tenant name to filter by (default: dogfood-shard)
- `--thread-count`: Number of threads to use for processing (default: 20)
- `--limit`: Maximum number of requests to process
- `--debug`: Enable debug logging

## Output

The analysis provides detailed output including:

1. **LaunchProcess Analysis Results**:
   - Total conversations analyzed
   - Number of conversations with LaunchProcess tool calls
   - Total LaunchProcess tool calls made
   - Success and failure rates
   - List of request IDs containing LaunchProcess tool calls

2. **General Tool Statistics**:
   - Tool usage distribution
   - Success rates by tool
   - Error analysis and patterns

## Key Insights

This module helps identify:

1. **Tool Usage Patterns**:
   - Which tools are used most frequently
   - Success and failure patterns for different tools
   - Staging environment specific tool usage

2. **LaunchProcess Analysis**:
   - Staging environment requests that use LaunchProcess
   - Success rates for LaunchProcess tool calls
   - Patterns in process execution

3. **Agent Behavior Analysis**:
   - How agents structure their conversations
   - Tool usage sequences and patterns

## Development

When extending this module:

1. Add new analysis metrics to `ToolCallAnalysis` class
2. Create new analysis functions for specific tools
3. Update the conversation processing logic
4. Add tests for new functionality
