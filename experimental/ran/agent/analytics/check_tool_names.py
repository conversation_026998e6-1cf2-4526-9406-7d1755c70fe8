#!/usr/bin/env python3

import argparse
import datetime
import logging

from base.datasets.tenants import get_tenant
from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def check_tool_names(tenant_name: str, last_days: int = 7):
    """Check what tool names are available in the last N days."""

    tenant = get_tenant(tenant_name)
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Get current time and calculate from_date
    now = datetime.datetime.now(datetime.timezone.utc)
    from_date = now - datetime.timedelta(days=last_days)

    time_filter = f"time >= TIMESTAMP('{from_date.isoformat()}') AND time <= TIMESTAMP('{now.isoformat()}')"

    # Query to find distinct tool names
    query = f"""
    SELECT
        JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') as tool_name,
        JSON_EXTRACT_SCALAR(sanitized_json, '$.name') as name,
        COUNT(*) as count
    FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
    WHERE tenant = '{tenant.name}'
    AND {time_filter}
    AND (
        JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') IS NOT NULL
        OR JSON_EXTRACT_SCALAR(sanitized_json, '$.name') IS NOT NULL
    )
    GROUP BY tool_name, name
    ORDER BY count DESC
    LIMIT 50
    """

    # First check if there are any records at all
    count_query = f"""
    SELECT COUNT(*) as total_count
    FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
    WHERE tenant = '{tenant.name}'
    AND {time_filter}
    """

    logger.info(
        f"Checking total record count in tenant {tenant_name} from the last {last_days} days..."
    )
    count_rows = bigquery_client.query_and_wait(count_query)
    total_count = list(count_rows)[0].total_count
    print(f"\nTotal records in {tenant_name} (last {last_days} days): {total_count}")

    if total_count == 0:
        print("No records found in the specified time range.")
        return

    logger.info(
        f"Querying for tool names in tenant {tenant_name} from the last {last_days} days..."
    )
    rows = bigquery_client.query_and_wait(query)

    print(f"\nTool names found in {tenant_name} (last {last_days} days):")
    print("=" * 60)

    tool_count = 0
    for row in rows:
        tool_count += 1
        tool_name = row.tool_name or "NULL"
        name = row.name or "NULL"
        count = row.count
        print(f"tool_name: {tool_name:20} | name: {name:20} | count: {count}")

    if tool_count == 0:
        print("No tool names found in the records.")

        # Let's check what the JSON structure actually looks like
        sample_query = f"""
        SELECT sanitized_json
        FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
        WHERE tenant = '{tenant.name}'
        AND {time_filter}
        LIMIT 5
        """

        logger.info("Checking sample JSON structures...")
        sample_rows = bigquery_client.query_and_wait(sample_query)

        print("\nSample JSON structures:")
        print("=" * 60)

        for i, row in enumerate(sample_rows):
            print(f"Sample {i+1}:")
            print(row.sanitized_json)
            print("-" * 40)

        # Check what event types are available
        event_query = f"""
        SELECT
            JSON_EXTRACT_SCALAR(sanitized_json, '$.event_name') as event_name,
            COUNT(*) as count
        FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
        WHERE tenant = '{tenant.name}'
        AND {time_filter}
        GROUP BY event_name
        ORDER BY count DESC
        """

        logger.info("Checking available event types...")
        event_rows = bigquery_client.query_and_wait(event_query)

        print("\nAvailable event types:")
        print("=" * 60)

        for row in event_rows:
            event_name = row.event_name or "NULL"
            count = row.count
            print(f"event_name: {event_name:30} | count: {count}")

    # Also check for any launch-related patterns
    launch_query = f"""
    SELECT
        JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') as tool_name,
        JSON_EXTRACT_SCALAR(sanitized_json, '$.name') as name,
        COUNT(*) as count
    FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
    WHERE tenant = '{tenant.name}'
    AND {time_filter}
    AND (
        LOWER(JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name')) LIKE '%launch%'
        OR LOWER(JSON_EXTRACT_SCALAR(sanitized_json, '$.name')) LIKE '%launch%'
        OR LOWER(JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name')) LIKE '%process%'
        OR LOWER(JSON_EXTRACT_SCALAR(sanitized_json, '$.name')) LIKE '%process%'
    )
    GROUP BY tool_name, name
    ORDER BY count DESC
    """

    logger.info("Checking for launch/process related tool names...")
    launch_rows = bigquery_client.query_and_wait(launch_query)

    print("\nLaunch/Process related tool names:")
    print("=" * 60)

    found_launch = False
    for row in launch_rows:
        found_launch = True
        tool_name = row.tool_name or "NULL"
        name = row.name or "NULL"
        count = row.count
        print(f"tool_name: {tool_name:20} | name: {name:20} | count: {count}")

    if not found_launch:
        print("No launch/process related tool names found.")


def main():
    parser = argparse.ArgumentParser(description="Check available tool names")
    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help="Tenant name to check (default: dogfood-shard)",
    )
    parser.add_argument(
        "--last-days",
        type=int,
        default=7,
        help="Number of days to look back (default: 7)",
    )

    args = parser.parse_args()
    check_tool_names(args.tenant_name, args.last_days)


if __name__ == "__main__":
    main()
