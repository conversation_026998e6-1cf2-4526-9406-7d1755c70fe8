#!/usr/bin/env python3
"""
Tests for random_tool_call_sampler.py
"""

import pytest
from unittest.mock import MagicMock

from experimental.ran.agent.analytics.random_tool_call_sampler import (
    is_qualifying_view_call,
    is_codebase_retrieval_call,
    extract_qualifying_tool_calls,
)
from experimental.ran.agent.analytics.conversation import (
    Conversation,
    AgentRound,
    AgentTurn,
    ToolCall,
    ChatResultToolUse,
)


def create_mock_tool_call(
    tool_name: str, tool_input: dict, tool_use_request_id: str = "test-request-id"
) -> ToolCall:
    """Create a mock ToolCall for testing."""
    tool_use = ChatResultToolUse(
        tool_use_id="test-id",
        name=tool_name,
        input=tool_input,
    )

    tool_call = ToolCall(
        tool_use_request_id=tool_use_request_id,
        tool_use_request=MagicMock(),
        tool_use=tool_use,
    )

    return tool_call


class TestIsQualifyingViewCall:
    """Test the is_qualifying_view_call function."""

    def test_non_view_tool(self):
        """Test that non-view tools are not qualifying."""
        tool_call = create_mock_tool_call("other-tool", {"path": "test.py"})
        assert not is_qualifying_view_call(tool_call)

    def test_str_replace_editor_tool(self):
        """Test that str-replace-editor tools are not qualifying (since we now use dedicated view tool)."""
        tool_call = create_mock_tool_call(
            "str-replace-editor", {"command": "view", "path": "test.py"}
        )
        assert not is_qualifying_view_call(tool_call)

    def test_view_whole_file(self):
        """Test that view tool without view_range is qualifying."""
        tool_call = create_mock_tool_call("view", {"path": "test.py"})
        assert is_qualifying_view_call(tool_call)

    def test_view_with_end_of_file(self):
        """Test that view tool with -1 end line is qualifying."""
        tool_call = create_mock_tool_call(
            "view", {"path": "test.py", "view_range": [100, -1]}
        )
        assert is_qualifying_view_call(tool_call)

    def test_view_with_500_lines(self):
        """Test that view tool with exactly 500 lines is qualifying."""
        tool_call = create_mock_tool_call(
            "view", {"path": "test.py", "view_range": [1, 500]}
        )
        assert is_qualifying_view_call(tool_call)

    def test_view_with_more_than_500_lines(self):
        """Test that view tool with more than 500 lines is qualifying."""
        tool_call = create_mock_tool_call(
            "view", {"path": "test.py", "view_range": [1, 600]}
        )
        assert is_qualifying_view_call(tool_call)

    def test_view_with_less_than_500_lines(self):
        """Test that view tool with less than 500 lines is not qualifying."""
        tool_call = create_mock_tool_call(
            "view", {"path": "test.py", "view_range": [1, 499]}
        )
        assert not is_qualifying_view_call(tool_call)

    def test_view_with_invalid_range(self):
        """Test that view tool with invalid range is not qualifying."""
        tool_call = create_mock_tool_call(
            "view",
            {
                "path": "test.py",
                "view_range": [100],  # Invalid range with only one element
            },
        )
        assert not is_qualifying_view_call(tool_call)


class TestIsCodebaseRetrievalCall:
    """Test the is_codebase_retrieval_call function."""

    def test_codebase_retrieval_tool(self):
        """Test that codebase-retrieval tools are qualifying."""
        tool_call = create_mock_tool_call(
            "codebase-retrieval",
            {"information_request": "Find all functions related to authentication"},
        )
        assert is_codebase_retrieval_call(tool_call)

    def test_non_codebase_retrieval_tool(self):
        """Test that non-codebase-retrieval tools are not qualifying."""
        tool_call = create_mock_tool_call("view", {"path": "test.py"})
        assert not is_codebase_retrieval_call(tool_call)


class TestExtractQualifyingToolCalls:
    """Test the extract_qualifying_tool_calls function."""

    def test_extract_qualifying_calls(self):
        """Test extracting qualifying calls from conversations."""
        # Create mock conversations with various tool calls
        conversations = []

        # Conversation 1: qualifying view call
        agent_turn1 = AgentTurn(
            message="Looking at the file",
            request_id="turn1-id",
            request=MagicMock(),
            tool_call=create_mock_tool_call(
                "view",
                {"path": "test1.py", "view_range": [1, 500]},
                "request1-id",
            ),
        )

        agent_round1 = AgentRound(
            user_message="Show me the file",
            user_message_request_id="user1-id",
            user_message_request=MagicMock(),
            agent_turns=[agent_turn1],
        )

        conversation1 = Conversation(
            last_request_id="conv1-id", agent_rounds=[agent_round1]
        )
        conversations.append(conversation1)

        # Conversation 2: non-qualifying view call and codebase-retrieval call
        agent_turn2a = AgentTurn(
            message="Small view",
            request_id="turn2a-id",
            request=MagicMock(),
            tool_call=create_mock_tool_call(
                "view",
                {
                    "path": "test2.py",
                    "view_range": [1, 100],  # Too small
                },
                "request2a-id",
            ),
        )

        agent_turn2b = AgentTurn(
            message="Searching codebase",
            request_id="turn2b-id",
            request=MagicMock(),
            tool_call=create_mock_tool_call(
                "codebase-retrieval",
                {"information_request": "Find authentication functions"},
                "request2b-id",
            ),
        )

        agent_round2 = AgentRound(
            user_message="Help me find code",
            user_message_request_id="user2-id",
            user_message_request=MagicMock(),
            agent_turns=[agent_turn2a, agent_turn2b],
        )

        conversation2 = Conversation(
            last_request_id="conv2-id", agent_rounds=[agent_round2]
        )
        conversations.append(conversation2)

        # Extract qualifying calls
        qualifying_calls = extract_qualifying_tool_calls(conversations)

        # Should have 2 qualifying calls: the 500-line view and the codebase-retrieval
        assert len(qualifying_calls) == 2

        # Check the first call (view)
        tool_use_request_id1, tool_result_request_id1, tool_name1, description1 = (
            qualifying_calls[0]
        )
        assert tool_use_request_id1 == "request1-id"
        assert tool_result_request_id1 == "N/A"  # No tool result request ID set in mock
        assert tool_name1 == "view"
        assert "view (500 lines, 1-500): test1.py" == description1

        # Check the second call (codebase-retrieval)
        tool_use_request_id2, tool_result_request_id2, tool_name2, description2 = (
            qualifying_calls[1]
        )
        assert tool_use_request_id2 == "request2b-id"
        assert tool_result_request_id2 == "N/A"  # No tool result request ID set in mock
        assert tool_name2 == "codebase-retrieval"
        assert "codebase-retrieval: Find authentication functions" == description2

    def test_extract_no_qualifying_calls(self):
        """Test extracting from conversations with no qualifying calls."""
        # Create conversation with only non-qualifying calls
        agent_turn = AgentTurn(
            message="Small view",
            request_id="turn-id",
            request=MagicMock(),
            tool_call=create_mock_tool_call(
                "view",
                {
                    "path": "test.py",
                    "view_range": [1, 10],  # Too small
                },
            ),
        )

        agent_round = AgentRound(
            user_message="Show me a small part",
            user_message_request_id="user-id",
            user_message_request=MagicMock(),
            agent_turns=[agent_turn],
        )

        conversation = Conversation(
            last_request_id="conv-id", agent_rounds=[agent_round]
        )

        qualifying_calls = extract_qualifying_tool_calls([conversation])
        assert len(qualifying_calls) == 0

    def test_extract_with_no_tool_calls(self):
        """Test extracting from conversations with no tool calls."""
        # Create conversation with agent turn but no tool call
        agent_turn = AgentTurn(
            message="Just a message",
            request_id="turn-id",
            request=MagicMock(),
            tool_call=None,
        )

        agent_round = AgentRound(
            user_message="Just talk",
            user_message_request_id="user-id",
            user_message_request=MagicMock(),
            agent_turns=[agent_turn],
        )

        conversation = Conversation(
            last_request_id="conv-id", agent_rounds=[agent_round]
        )

        qualifying_calls = extract_qualifying_tool_calls([conversation])
        assert len(qualifying_calls) == 0


if __name__ == "__main__":
    pytest.main([__file__])
