from experimental.ran.agent.analytics.conversation import <PERSON><PERSON><PERSON><PERSON>
from experimental.ran.agent.replay_eval.str_replace_editor_utils import (
    extract_str_replace_entries_any_schema,
)
import os

OUTPUT_DIR = "/home/<USER>/augment/clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/__tests__/match_test_data"


def _save_sample(
    request_id,
    original_str,
    old_str,
    new_str,
):
    """
    Save a no match sample to the output directory.

    Args:
        request_id: The request id
        original_str: The original string
        old_str: The old string
        new_str: The new string

    Creates a subdir with the request id, and saves the original string, old string, and new string to files in that dir.
    Also copies old_str and new_str to modified_old_str and modified_new_str so it's easier to create correct labels later
    """
    sample_dir = os.path.join(OUTPUT_DIR, request_id)
    os.makedirs(sample_dir, exist_ok=True)

    with open(os.path.join(sample_dir, "original_str.txt"), "w") as f:
        f.write(original_str)
    with open(os.path.join(sample_dir, "old_str.txt"), "w") as f:
        f.write(old_str)
    with open(os.path.join(sample_dir, "new_str.txt"), "w") as f:
        f.write(new_str)
    with open(os.path.join(sample_dir, "modified_old_str.txt"), "w") as f:
        f.write(old_str)
    with open(os.path.join(sample_dir, "modified_new_str.txt"), "w") as f:
        f.write(new_str)


def save_no_match_sample(tool_call: ToolCall):
    if tool_call.analysis is None:
        return
    if tool_call.analysis.error_type != "str_replace_no_verbatim_match":
        return
    if tool_call.tool_result is None:
        return
    if tool_call.tool_result.content is None:
        return
    if tool_call.analysis.str_replace_tool_result is None:
        return

    str_replace_entries = extract_str_replace_entries_any_schema(
        tool_call.tool_use.input
    )

    results = tool_call.analysis.str_replace_tool_result.results

    if len(str_replace_entries) != 1:
        return

    assert len(results) == 1

    for i, result in enumerate(results):
        if result.success:
            continue
        if not result.original_snippet_lines:
            continue
        entry = str_replace_entries[i]
        _save_sample(
            tool_call.tool_result_request_id,
            "\n".join(result.original_snippet_lines),
            entry["old_str"],
            entry["new_str"],
        )
