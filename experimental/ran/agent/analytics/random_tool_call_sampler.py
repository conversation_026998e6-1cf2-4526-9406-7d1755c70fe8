#!/usr/bin/env python3
"""
Random Tool Call Sampler

This script selects random view or codebase-retrieval tool calls from agent conversations.
For view calls (using the dedicated view tool), it only includes those that either read the whole file or read at least 500 lines.
"""

import argparse
import logging
import random
from datetime import datetime, timedelta
from typing import List, Tuple

from experimental.ran.agent.analytics.conversation import Conversation, ToolCall
from experimental.ran.agent.analytics.run_analysis import (
    fetch_conversations,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def parse_date_args(args):
    """Parse date arguments from command line args."""
    # Get current time
    now = datetime.now()

    # Parse dates
    to_date = args.to_date if args.to_date is not None else now

    # Determine from_date based on arguments
    if args.last_hours is not None:
        from_date = now - timedelta(hours=args.last_hours)
        logger.info(f"Processing data from the last {args.last_hours} hours")
    elif args.last_days is not None:
        from_date = now - timedelta(days=args.last_days)
        logger.info(f"Processing data from the last {args.last_days} days")
    elif args.from_date is not None:
        from_date = args.from_date
    else:
        # Default: process the last 1 hour
        from_date = now - timedelta(hours=1)
        logger.info("No time range specified, defaulting to the last 1 hour")

    return from_date, to_date


def is_qualifying_view_call(tool_call: ToolCall) -> bool:
    """
    Check if a tool call is a qualifying view call.

    A qualifying view call is one that:
    1. Is a dedicated view tool
    2. Either reads the whole file (no view_range) or reads at least 500 lines
    """
    if not tool_call.tool_use or tool_call.tool_use.name != "view":
        return False

    tool_input = tool_call.tool_use.input
    if not isinstance(tool_input, dict):
        return False

    # Check if it reads the whole file (no view_range)
    view_range = tool_input.get("view_range")
    if not view_range:
        return True

    # Check if it reads at least 500 lines
    if isinstance(view_range, list) and len(view_range) >= 2:
        start_line, end_line = view_range[0], view_range[1]
        if end_line == -1:  # Reads to end of file
            return True
        if isinstance(start_line, int) and isinstance(end_line, int):
            lines_read = end_line - start_line + 1
            return lines_read >= 500

    return False


def is_codebase_retrieval_call(tool_call: ToolCall) -> bool:
    """Check if a tool call is a codebase-retrieval call."""
    return tool_call.tool_use and tool_call.tool_use.name == "codebase-retrieval"


def extract_qualifying_tool_calls(
    conversations: List[Conversation],
) -> List[Tuple[str, str, str, str]]:
    """
    Extract qualifying tool calls from conversations.

    Returns a list of tuples: (tool_use_request_id, tool_result_request_id, tool_name, description)
    """
    qualifying_calls = []

    for conversation in conversations:
        for agent_round in conversation.agent_rounds:
            for agent_turn in agent_round.agent_turns:
                if not agent_turn.tool_call:
                    continue

                tool_call = agent_turn.tool_call
                tool_use_request_id = tool_call.tool_use_request_id
                tool_result_request_id = tool_call.tool_result_request_id or "N/A"

                if is_qualifying_view_call(tool_call):
                    # Get view range info for description
                    tool_input = tool_call.tool_use.input
                    view_range = tool_input.get("view_range")
                    path = tool_input.get("path", "unknown")

                    if not view_range:
                        description = f"view (whole file): {path}"
                    else:
                        start_line, end_line = view_range[0], view_range[1]
                        if end_line == -1:
                            description = f"view (lines {start_line} to end): {path}"
                        else:
                            lines_read = end_line - start_line + 1
                            description = f"view ({lines_read} lines, {start_line}-{end_line}): {path}"

                    qualifying_calls.append(
                        (
                            tool_use_request_id,
                            tool_result_request_id,
                            "view",
                            description,
                        )
                    )

                elif is_codebase_retrieval_call(tool_call):
                    # Get information request for description
                    tool_input = tool_call.tool_use.input
                    info_request = tool_input.get("information_request", "")
                    # Truncate long requests for readability
                    if len(info_request) > 100:
                        info_request = info_request[:97] + "..."
                    description = f"codebase-retrieval: {info_request}"

                    qualifying_calls.append(
                        (
                            tool_use_request_id,
                            tool_result_request_id,
                            "codebase-retrieval",
                            description,
                        )
                    )

    return qualifying_calls


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Select random view or codebase-retrieval tool calls"
    )

    # Date range arguments (reuse from run_analysis.py)
    parser.add_argument(
        "--from-date",
        type=lambda s: datetime.fromisoformat(s),
        help="Start date in ISO format (YYYY-MM-DDTHH:MM:SS)",
    )
    parser.add_argument(
        "--to-date",
        type=lambda s: datetime.fromisoformat(s),
        help="End date in ISO format (YYYY-MM-DDTHH:MM:SS)",
    )
    parser.add_argument(
        "-lh",
        "--last-hours",
        type=int,
        help="Process data from the last N hours",
    )
    parser.add_argument(
        "-ld",
        "--last-days",
        type=int,
        help="Process data from the last N days",
    )

    # Sampling arguments
    parser.add_argument(
        "--count",
        type=int,
        default=30,
        help="Number of random tool calls to select (default: 30)",
    )

    # Other arguments
    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help="Tenant name to filter by (default: dogfood-shard)",
    )
    parser.add_argument(
        "--thread-count",
        type=int,
        default=20,
        help="Number of threads to use for processing (default: 20)",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit the number of conversations to process (default: None)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )
    parser.add_argument(
        "--model",
        type=str,
        default="claude",
        choices=["claude", "gemini"],
        help="Filter conversations by model name (default: claude)",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="Random seed for reproducible results",
    )

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    # Set up logging
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

    # Set random seed if provided
    if args.seed is not None:
        random.seed(args.seed)
        logger.info(f"Using random seed: {args.seed}")

    # Parse date arguments
    from_date, to_date = parse_date_args(args)

    logger.info(f"Fetching conversations from {from_date} to {to_date}")
    logger.info(f"Tenant: {args.tenant_name}")
    logger.info(f"Model: {args.model}")

    # Fetch conversations
    conversations, _, _ = fetch_conversations(
        from_date=from_date,
        to_date=to_date,
        tenant_name=args.tenant_name,
        thread_count=args.thread_count,
        limit=args.limit,
        model=args.model,
    )

    if not conversations:
        logger.warning("No conversations found")
        return

    # Extract qualifying tool calls
    logger.info("Extracting qualifying tool calls...")
    qualifying_calls = extract_qualifying_tool_calls(conversations)

    logger.info(f"Found {len(qualifying_calls)} qualifying tool calls")

    if not qualifying_calls:
        logger.warning("No qualifying tool calls found")
        return

    # Randomly sample the requested number of calls
    sample_size = min(args.count, len(qualifying_calls))
    sampled_calls = random.sample(qualifying_calls, sample_size)

    logger.info(f"Randomly selected {sample_size} tool calls")

    # Print results
    print("\n===== Random Tool Call Sample =====")
    print(f"Sample size: {sample_size} out of {len(qualifying_calls)} qualifying calls")
    print(f"Date range: {from_date} to {to_date}")
    print(f"Tenant: {args.tenant_name}")
    print(f"Model: {args.model}")
    if args.seed is not None:
        print(f"Random seed: {args.seed}")
    print()

    print("Request IDs:")
    for i, (
        tool_use_request_id,
        tool_result_request_id,
        tool_name,
        description,
    ) in enumerate(sampled_calls, 1):
        print(
            f"{i:2d}. Tool Use: {tool_use_request_id} | Tool Result: {tool_result_request_id} | {tool_name}: {description}"
        )

    print()
    print("Tool Use Request IDs only (for easy copying):")
    for tool_use_request_id, _, _, _ in sampled_calls:
        print(tool_use_request_id)

    print()
    print("Tool Result Request IDs only (for easy copying):")
    for _, tool_result_request_id, _, _ in sampled_calls:
        if tool_result_request_id != "N/A":
            print(tool_result_request_id)


if __name__ == "__main__":
    main()
