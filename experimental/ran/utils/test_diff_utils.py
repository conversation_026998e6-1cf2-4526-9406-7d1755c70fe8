"""Tests for diff_utils.py."""

import pytest
from pathlib import Path

from experimental.ran.utils.diff_utils import normalize_imports_and_compare_asts


def test_normalize_imports_and_compare_asts_python():
    """Test normalize_imports_and_compare_asts with Python code."""
    code1 = """\
from b import y
from a import x
"""

    code2 = """\
from a import x
from b import y
"""

    # Same imports in different order should match
    assert (
        normalize_imports_and_compare_asts(code1, code2, "python", Path("test.py"))
        is True
    )

    # Different imports should not match
    code3 = """\
from a import x
from c import z
"""
    assert (
        normalize_imports_and_compare_asts(code1, code3, "python", Path("test.py"))
        is False
    )

    # Non-import differences should be detected
    code4 = """\
from b import y
from a import x

def foo():
    pass
"""
    assert (
        normalize_imports_and_compare_asts(code1, code4, "python", Path("test.py"))
        is False
    )


def test_normalize_imports_and_compare_asts_unsupported_lang():
    """Test normalize_imports_and_compare_asts with unsupported language."""
    code1 = "some code"
    code2 = "other code"

    assert (
        normalize_imports_and_compare_asts(
            code1, code2, "unsupported_lang", Path("test.txt")
        )
        is None
    )


def test_normalize_imports_and_compare_asts_invalid_code():
    """Test normalize_imports_and_compare_asts with invalid code."""
    code1 = "invalid python code )"
    code2 = "also invalid )"

    assert (
        normalize_imports_and_compare_asts(code1, code2, "python", Path("test.py"))
        is None
    )
