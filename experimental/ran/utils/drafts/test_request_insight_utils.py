import unittest
from unittest.mock import patch, MagicMock
from experimental.ran.utils.drafts.request_insight_utils import (
    fetch_request,
    fetch_edit_request,
    AUGMENT_TOKEN,
)


class TestRequestInsightUtils(unittest.TestCase):
    @patch("experimental.ran.utils.request_insight_utils.AugmentClient")
    def test_fetch_request_dev_namespace(self, mock_client_class):
        # Setup mock
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client
        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.json.return_value = {"test": "data"}
        mock_client._post.return_value = (mock_response, None)

        # Call function
        result = fetch_request("test-id", "some-tenant", "dev-test")

        # Verify client initialization
        mock_client_class.assert_called_once_with(
            url="https://support.dev-test.t.us-central1.prod.augmentcode.com",
            token=AUGMENT_TOKEN,
            user_agent="request_insight_utils/0",
        )

        # Verify API call
        mock_client._post.assert_called_once_with(
            "api/tenant/augment/request/test-id",
            json={},
        )

        # Verify result
        self.assertEqual(result, {"test": "data"})

    @patch("experimental.ran.utils.request_insight_utils.AugmentClient")
    def test_fetch_request_dogfood_tenant(self, mock_client_class):
        # Setup mock
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client
        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.json.return_value = {"test": "data"}
        mock_client._post.return_value = (mock_response, None)

        # Call function
        result = fetch_request("test-id", "dogfood-shard", "any-namespace")

        # Verify client initialization
        mock_client_class.assert_called_once_with(
            url="https://support.staging-shard-0.t.us-central1.prod.augmentcode.com",
            token=AUGMENT_TOKEN,
            user_agent="request_insight_utils/0",
        )

        # Verify API call
        mock_client._post.assert_called_once_with(
            "api/tenant/dogfood-shard/request/test-id",
            json={},
        )

        # Verify result
        self.assertEqual(result, {"test": "data"})

    @patch("experimental.ran.utils.request_insight_utils.AugmentClient")
    def test_fetch_request_error(self, mock_client_class):
        # Setup mock
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client
        mock_response = MagicMock()
        mock_response.ok = False
        mock_response.status_code = 404
        mock_response.text = "Not found"
        mock_client._post.return_value = (mock_response, None)

        # Verify error handling
        with self.assertRaises(ValueError) as cm:
            fetch_request("test-id", "some-tenant", "some-namespace")
        self.assertEqual(
            str(cm.exception), "Failed to fetch request test-id: 404 Not found"
        )

    @patch("experimental.ran.utils.request_insight_utils.fetch_request")
    def test_fetch_edit_request(self, mock_fetch_request):
        # Setup mock
        mock_fetch_request.return_value = {
            "instructionHostRequest": {"request": {"test": "data"}}
        }

        # Call function
        result = fetch_edit_request("test-id", "some-tenant", "some-namespace")

        # Verify result
        self.assertEqual(result, {"test": "data"})

    @patch("experimental.ran.utils.request_insight_utils.fetch_request")
    def test_fetch_edit_request_no_instruction_host(self, mock_fetch_request):
        # Setup mock
        mock_fetch_request.return_value = {"other": "data"}

        # Verify error handling
        with self.assertRaises(ValueError) as cm:
            fetch_edit_request("test-id", "some-tenant", "some-namespace")
        self.assertEqual(
            str(cm.exception), "Request test-id does not have an edit host request"
        )


if __name__ == "__main__":
    unittest.main()
