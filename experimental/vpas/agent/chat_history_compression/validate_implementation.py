#!/usr/bin/env python3
"""Validation script to check the implementation without running full tests."""

import os
import ast
import sys
from pathlib import Path


def check_syntax(file_path):
    """Check if a Python file has valid syntax."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Parse the AST to check syntax
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, f"Error reading file: {e}"


def validate_file_structure():
    """Validate that all expected files exist."""
    base_dir = Path(
        "/home/<USER>/augment/experimental/vpas/agent/chat_history_compression"
    )

    expected_files = [
        "chat_history_compression.py",
        "placeholder_compressor.py",
        "gemini_flash_compressor.py",
        "utils.py",
        "metrics.py",
        "llm_judge.py",
        "evaluation_pipeline.py",
        "eval_types.py",
        "config.py",
        "run_compression.py",
        "__init__.py",
        "tests/__init__.py",
        "tests/test_utils.py",
        "tests/test_utils_module.py",
        "tests/test_compressors.py",
        "tests/test_integration.py",
        "html_report/__init__.py",
        "html_report/report_generator.py",
    ]

    missing_files = []
    syntax_errors = []

    for file_path in expected_files:
        full_path = base_dir / file_path
        if not full_path.exists():
            missing_files.append(str(file_path))
        elif file_path.endswith(".py"):
            # Check syntax
            is_valid, error = check_syntax(full_path)
            if not is_valid:
                syntax_errors.append(f"{file_path}: {error}")

    return missing_files, syntax_errors


def check_class_definitions():
    """Check that key classes are properly defined."""
    base_dir = Path(
        "/home/<USER>/augment/experimental/vpas/agent/chat_history_compression"
    )

    checks = []

    # Check ChatHistoryCompressor
    try:
        with open(base_dir / "chat_history_compression.py", "r") as f:
            content = f.read()

        if "class ChatHistoryCompressor(ABC):" in content:
            checks.append("✓ ChatHistoryCompressor class defined")
        else:
            checks.append("✗ ChatHistoryCompressor class not found")

        if "class CompressionStats:" in content:
            checks.append("✓ CompressionStats dataclass defined")
        else:
            checks.append("✗ CompressionStats dataclass not found")

    except Exception as e:
        checks.append(f"✗ Error checking chat_history_compression.py: {e}")

    # Check PlaceholderCompressor
    try:
        with open(base_dir / "placeholder_compressor.py", "r") as f:
            content = f.read()

        if "class PlaceholderCompressor(ChatHistoryCompressor):" in content:
            checks.append("✓ PlaceholderCompressor class defined")
        else:
            checks.append("✗ PlaceholderCompressor class not found")

    except Exception as e:
        checks.append(f"✗ Error checking placeholder_compressor.py: {e}")

    # Check GeminiFlashCompressor
    try:
        with open(base_dir / "gemini_flash_compressor.py", "r") as f:
            content = f.read()

        if "class GeminiFlashCompressor(ChatHistoryCompressor):" in content:
            checks.append("✓ GeminiFlashCompressor class defined")
        else:
            checks.append("✗ GeminiFlashCompressor class not found")

    except Exception as e:
        checks.append(f"✗ Error checking gemini_flash_compressor.py: {e}")

    return checks


def check_function_definitions():
    """Check that key utility functions are defined."""
    base_dir = Path(
        "/home/<USER>/augment/experimental/vpas/agent/chat_history_compression"
    )

    checks = []

    try:
        with open(base_dir / "utils.py", "r") as f:
            content = f.read()

        expected_functions = [
            "should_compress_tool",
            "register_tool_uses",
            "is_tool_output_node",
            "is_tool_use_node",
            "find_tool_name_for_result",
            "extract_tool_content_summary",
        ]

        for func in expected_functions:
            if f"def {func}(" in content:
                checks.append(f"✓ {func} function defined")
            else:
                checks.append(f"✗ {func} function not found")

    except Exception as e:
        checks.append(f"✗ Error checking utils.py: {e}")

    return checks


def main():
    """Run all validation checks."""
    print("=" * 70)
    print("CHAT HISTORY COMPRESSION IMPLEMENTATION VALIDATION")
    print("=" * 70)

    # Check file structure
    print("\n1. CHECKING FILE STRUCTURE...")
    missing_files, syntax_errors = validate_file_structure()

    if not missing_files:
        print("✓ All expected files are present")
    else:
        print("✗ Missing files:")
        for file in missing_files:
            print(f"  - {file}")

    if not syntax_errors:
        print("✓ All Python files have valid syntax")
    else:
        print("✗ Syntax errors found:")
        for error in syntax_errors:
            print(f"  - {error}")

    # Check class definitions
    print("\n2. CHECKING CLASS DEFINITIONS...")
    class_checks = check_class_definitions()
    for check in class_checks:
        print(f"  {check}")

    # Check function definitions
    print("\n3. CHECKING FUNCTION DEFINITIONS...")
    function_checks = check_function_definitions()
    for check in function_checks:
        print(f"  {check}")

    # Summary
    print("\n" + "=" * 70)
    total_issues = len(missing_files) + len(syntax_errors)
    failed_checks = len(
        [c for c in class_checks + function_checks if c.startswith("✗")]
    )

    if total_issues == 0 and failed_checks == 0:
        print("✅ VALIDATION PASSED - Implementation appears to be complete!")
    else:
        print(
            f"❌ VALIDATION ISSUES FOUND - {total_issues} file issues, {failed_checks} definition issues"
        )

    print("=" * 70)

    # Additional information
    print("\nNOTE: This validation only checks file structure and basic definitions.")
    print("To run full functional tests, you would need:")
    print("1. All required dependencies installed")
    print("2. Access to the base.prompt_format.common module")
    print("3. pytest and other testing dependencies")
    print("4. Proper PYTHONPATH configuration")

    return total_issues == 0 and failed_checks == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
