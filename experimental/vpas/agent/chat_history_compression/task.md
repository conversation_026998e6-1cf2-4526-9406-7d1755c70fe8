# Goal
We want to compress Agent chat history as much as possible without losing any information that might be important further down the conversation.

# Which tools to compress
- view tool output
- retrieval tool output
- launch-process tool output
- str-replace-editor tool input and output
- save-file tool input

# Compression ideas
- simply remove and replace with a placeholder message
- summarize with a fast LLM like gemini flash
- use some heuristics to remove less important information

# Implementation details
- ChatHistoryCompressor interface with compress(chat_history: list[Exchange]) -> list[Exchange] func
- func to run compression on a given request_id
- func to calc compression rate in tokens
- func to evaluate compression using an LLM judge
- ChatHistoryCompressionEvalResult to store all eval and stats info
- script to generate React based report page. Similar to experimental/vpas/agent/replay_eval/html_report/react/

# Different compressors
- all should implement ChatHistoryCompressor
- all should only compress the tools mentioned above and leave other tools, user and agent messages as is
- placeholder compressor(remove and replace with a placeholder message)
- gemini flash compressor(summarize with a fast LLM like gemini flash). Use following approach. Put whole chat history in the context. Then put each exchange again one by one asking LLM to summarize each exchange one at a time.

# Useful pointers
- conversation analysis folder. Good reference on how to query requests and their chat history: experimental/vpas/agent/analytics/README.md
- replay analysis framework: experimental/vpas/agent/replay_eval/README.md
- client to run gemini flash: base/third_party_clients/google_genai_client.py
