"""
This script evaluates cost savings of chat history summarization on real data.

It compares the cost of running conversations with and without summarization by:
1. Fetching tool use data from BigQuery
2. Calculating costs with summarization using simulation
3. Calculating actual production costs from prompt cache usage data
4. Comparing the results

Usage:
    python eval_cost_on_real_data.py [--tenant TENANT] [--hours HOURS] [--days DAYS]
"""

import argparse
import json
import logging
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from google.cloud import bigquery
from tqdm import tqdm

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DATASET_TENANTS, DatasetTenant, get_tenant

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def fetch_conversations_data(
    project_id: str,
    dataset_name: str,
    from_datetime: datetime,
    to_datetime: datetime,
    tenant_filter: Optional[str] = None,
    num_conversations: Optional[int] = None,
) -> Dict[str, List[Dict]]:
    """Fetch tool use data grouped by conversation from BigQuery.

    Args:
        project_id: GCP project ID
        dataset_name: BigQuery dataset name (e.g., us_prod_request_insight_analytics_dataset)
        from_datetime: Start time (inclusive)
        to_datetime: End time (inclusive)
        tenant_filter: Optional tenant name to filter by
        num_conversations: Optional number of conversations to sample randomly

    Returns:
        Dictionary mapping conversation_id to sorted list of exchanges
    """
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)

    # Build tenant filter
    tenant_condition = f"AND tenant = '{tenant_filter}'" if tenant_filter else ""

    # Build sampling logic
    if num_conversations:
        # First get random conversation IDs
        conversation_sample_query = f"""
        WITH conversations AS (
            SELECT DISTINCT JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') as conversation_id
            FROM `{project_id}.{dataset_name}.tool_use_data`
            WHERE time >= TIMESTAMP('{from_datetime.isoformat()}')
                AND time <= TIMESTAMP('{to_datetime.isoformat()}')
                AND JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') IS NOT NULL
                {tenant_condition}
        )
        SELECT conversation_id
        FROM conversations
        ORDER BY RAND()
        LIMIT {num_conversations}
        """

        logger.info(f"Sampling {num_conversations} random conversations...")
        sample_rows = bigquery_client.query_and_wait(conversation_sample_query)
        sampled_conversation_ids = [row.conversation_id for row in sample_rows]

        if not sampled_conversation_ids:
            logger.warning("No conversations found for sampling")
            return {}

        # Create IN clause for sampled conversations
        conversation_ids_str = "', '".join(sampled_conversation_ids)
        conversation_filter = f"AND JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') IN ('{conversation_ids_str}')"

        logger.info(
            f"Selected {len(sampled_conversation_ids)} conversations for analysis"
        )
    else:
        conversation_filter = ""

    # Main query to get tool use data
    query = f"""
    SELECT
        time,
        request_id,
        JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') as conversation_id,
        CAST(JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_input_len') AS INT64) as tool_input_len,
        CAST(JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_output_len') AS INT64) as tool_output_len,
        JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_name') as tool_name
    FROM `{project_id}.{dataset_name}.tool_use_data`
    WHERE time >= TIMESTAMP('{from_datetime.isoformat()}')
        AND time <= TIMESTAMP('{to_datetime.isoformat()}')
        AND JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') IS NOT NULL
        AND JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_input_len') IS NOT NULL
        AND JSON_EXTRACT_SCALAR(sanitized_json, '$.tool_output_len') IS NOT NULL
        {tenant_condition}
        {conversation_filter}
    ORDER BY conversation_id, time
    """

    filter_desc = f"tenant {tenant_filter}" if tenant_filter else "all tenants"
    logger.info(
        f"Fetching tool use data for {filter_desc} from {from_datetime} to {to_datetime}"
    )

    rows = bigquery_client.query_and_wait(query)

    # Group by conversation in Python (but data is already ordered by conversation_id, time)
    conversations = defaultdict(list)
    for row in rows:
        conversations[row.conversation_id].append(
            {
                "time": row.time,
                "request_id": row.request_id,
                "conversation_id": row.conversation_id,
                "tool_input_len": row.tool_input_len,
                "tool_output_len": row.tool_output_len,
                "tool_name": row.tool_name,
            }
        )

    total_records = sum(len(exchanges) for exchanges in conversations.values())
    logger.info(
        f"Fetched {total_records} tool use records across {len(conversations)} conversations"
    )

    return dict(conversations)


def fetch_prompt_cache_usage_data(
    project_id: str,
    dataset_name: str,
    request_ids: List[str],
    tenant_filter: Optional[str] = None,
) -> Dict[str, Dict]:
    """Fetch prompt cache usage data for the given request IDs.

    Args:
        project_id: GCP project ID
        dataset_name: BigQuery dataset name
        request_ids: List of request IDs to fetch data for
        tenant_filter: Optional tenant name to filter by

    Returns:
        Dictionary mapping request_id to cache usage data
    """
    if not request_ids:
        return {}

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)

    # Build tenant filter
    tenant_condition = f"AND tenant = '{tenant_filter}'" if tenant_filter else ""

    # Split request_ids into batches to avoid query size limits
    batch_size = 10000
    all_results = {}

    for i in range(0, len(request_ids), batch_size):
        batch_request_ids = request_ids[i : i + batch_size]
        request_ids_str = "', '".join(batch_request_ids)

        query = f"""
        SELECT
            request_id,
            CAST(JSON_EXTRACT_SCALAR(sanitized_json, '$.input_tokens') AS INT64) as input_tokens,
            CAST(JSON_EXTRACT_SCALAR(sanitized_json, '$.cache_read_input_tokens') AS INT64) as cache_read_input_tokens,
            CAST(JSON_EXTRACT_SCALAR(sanitized_json, '$.cache_creation_input_tokens') AS INT64) as cache_creation_input_tokens,
            CAST(JSON_EXTRACT_SCALAR(sanitized_json, '$.total_output_tokens') AS INT64) as total_output_tokens
        FROM `{project_id}.{dataset_name}.prompt_cache_usage`
        WHERE request_id IN ('{request_ids_str}')
            {tenant_condition}
        """

        logger.info(
            f"Fetching prompt cache usage data for batch {i//batch_size + 1}/{(len(request_ids) + batch_size - 1)//batch_size}"
        )
        rows = bigquery_client.query_and_wait(query)

        for row in rows:
            all_results[row.request_id] = {
                "input_tokens": row.input_tokens or 0,
                "cache_read_input_tokens": row.cache_read_input_tokens or 0,
                "cache_creation_input_tokens": row.cache_creation_input_tokens or 0,
                "total_output_tokens": row.total_output_tokens or 0,
            }

    logger.info(f"Fetched prompt cache usage data for {len(all_results)} requests")
    return all_results


def calc_cost_with_summarization_real_samples(
    exchanges: List[Dict],
    summarization_threshold_tokens,
    avg_summary_size_tokens=2_000,
    keep_last_n_tokens=30_000,
    summarization_prompt_size=759,
    cache_expiration_enabled=False,
):
    # simulate agent run on a real conv data(exchange_sizes_tokens)
    total_cost = 0

    cur_context_tokens = 0
    cur_exchanges = []
    prev_tool_use_tokens = 0
    prev_tool_result_tokens = 0
    for i, exchange in enumerate(exchanges):
        cache_read_cost = 0.3
        if (
            cache_expiration_enabled
            and i > 0
            and exchange["time"] - exchanges[i - 1]["time"] > timedelta(minutes=5)
        ):
            cache_read_cost = 3.75

        cur_input_tokens = prev_tool_use_tokens + prev_tool_result_tokens
        if cur_context_tokens + cur_input_tokens > summarization_threshold_tokens:
            tail_exchanges = [cur_input_tokens]
            while sum(tail_exchanges) < keep_last_n_tokens and len(cur_exchanges) > 0:
                tail_exchanges.append(cur_exchanges.pop())

            tail_exchanges = list(reversed(tail_exchanges))
            tail_tokens = sum(tail_exchanges)

            summarization_cost = (
                sum(cur_exchanges) * cache_read_cost
                + summarization_prompt_size * 3.75
                + avg_summary_size_tokens * 15
            )

            cur_context_tokens = avg_summary_size_tokens + tail_tokens
            cur_exchange_cost = cur_context_tokens * 3.75

            total_cost += summarization_cost + cur_exchange_cost
            cur_exchanges = tail_exchanges
        else:
            total_cost += cur_context_tokens * cache_read_cost + cur_input_tokens * 3.75
            cur_context_tokens += cur_input_tokens
            cur_exchanges.append(cur_input_tokens)

        prev_tool_use_tokens = exchange["tool_input_len"] / 3
        prev_tool_result_tokens = exchange["tool_output_len"] / 3
        total_cost += prev_tool_use_tokens * 15

    cur_input_tokens = prev_tool_use_tokens + prev_tool_result_tokens
    total_cost += cur_input_tokens * 3.75

    return total_cost / 1_000_000


def calculate_cost_with_summarization(
    conversations: Dict[str, List[Dict]],
    summarization_threshold_tokens: int = 100_000,
    keep_last_n_tokens: int = 30_000,
    cache_expiration_enabled: bool = False,
) -> float:
    """Calculate total cost using summarization simulation.

    Args:
        conversations: Dictionary mapping conversation_id to list of exchanges
        summarization_threshold_tokens: Threshold for triggering summarization
        keep_last_n_tokens: Number of tokens to keep from recent context
        cache_expiration_enabled: Whether to enable cache expiration in cost calculation

    Returns:
        Total cost in dollars
    """
    total_cost = 0.0

    logger.info(
        f"Calculating costs with summarization for {len(conversations)} conversations"
    )

    for conversation_id, exchanges in tqdm(
        conversations.items(), desc="Processing conversations"
    ):
        # Calculate cost for this conversation using summarization
        conversation_cost = calc_cost_with_summarization_real_samples(
            exchanges=exchanges,
            summarization_threshold_tokens=summarization_threshold_tokens,
            keep_last_n_tokens=keep_last_n_tokens,
            cache_expiration_enabled=cache_expiration_enabled,
        )

        total_cost += conversation_cost

    logger.info(f"Total cost with summarization: ${total_cost:.2f}")
    return total_cost


def calculate_cost_without_summarization(
    conversations: Dict[str, List[Dict]],
    prompt_cache_usage: Dict[str, Dict],
) -> float:
    """Calculate total cost using actual production prompt cache usage data.

    Args:
        conversations: Dictionary mapping conversation_id to list of exchanges
        prompt_cache_usage: Dictionary mapping request_id to cache usage data

    Returns:
        Total cost in dollars
    """
    total_cost = 0.0
    total_requests = 0
    missing_cache_data = 0

    logger.info(
        f"Calculating costs without summarization for {len(conversations)} conversations"
    )

    for conversation_id, exchanges in tqdm(
        conversations.items(), desc="Processing conversations"
    ):
        conversation_cost = 0.0

        for exchange in exchanges:
            request_id = exchange["request_id"]
            total_requests += 1

            if request_id not in prompt_cache_usage:
                missing_cache_data += 1
                continue

            cache_data = prompt_cache_usage[request_id]

            # Calculate cost using the specified formula:
            # input_tokens * 3 + cache_read_input_tokens * 0.3 +
            # cache_creation_input_tokens * 3.75 + total_output_tokens * 15
            request_cost = (
                cache_data["input_tokens"] * 3
                + cache_data["cache_read_input_tokens"] * 0.3
                + cache_data["cache_creation_input_tokens"] * 3.75
                + cache_data["total_output_tokens"] * 15
            ) / 1_000_000  # Convert to dollars

            conversation_cost += request_cost

        total_cost += conversation_cost

    if missing_cache_data > 0:
        logger.warning(
            f"Missing cache data for {missing_cache_data}/{total_requests} requests ({missing_cache_data/total_requests*100:.1f}%)"
        )

    logger.info(f"Total cost without summarization: ${total_cost:.2f}")
    return total_cost


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Evaluate cost savings of chat history summarization on real data"
    )

    # Dataset selection (mutually exclusive with tenant)
    dataset_group = parser.add_mutually_exclusive_group()
    dataset_group.add_argument(
        "--prod",
        action="store_true",
        help="Use production dataset (us_prod_request_insight_analytics_dataset)",
    )
    dataset_group.add_argument(
        "--staging",
        action="store_true",
        help="Use staging dataset (us_staging_request_insight_analytics_dataset)",
    )

    parser.add_argument(
        "--tenant",
        type=str,
        default=None,
        help=f"Tenant name to analyze. Available: {list(DATASET_TENANTS.keys())}. Cannot be used with --prod/--staging",
    )

    parser.add_argument(
        "--num-conversations",
        type=int,
        default=None,
        help="Number of conversations to sample randomly. If not specified, analyzes all conversations",
    )

    parser.add_argument(
        "--hours",
        type=int,
        default=24,
        help="Number of hours to look back from now (default: 24)",
    )

    parser.add_argument(
        "--days",
        type=int,
        default=None,
        help="Number of days to look back from now (overrides --hours if specified)",
    )

    parser.add_argument(
        "--summarization-threshold",
        type=int,
        default=100_000,
        help="Token threshold for triggering summarization (default: 100,000)",
    )

    parser.add_argument(
        "--threshold-range",
        action="store_true",
        help="Test multiple summarization thresholds and show comparison table",
    )

    parser.add_argument(
        "--threshold-values",
        type=str,
        default="50000,75000,100000,125000,150000,200000",
        help="Comma-separated list of threshold values to test (default: 50000,75000,100000,125000,150000,200000)",
    )

    parser.add_argument(
        "--keep-last-range",
        action="store_true",
        help="Test multiple keep_last_n_tokens values and show 2D comparison table",
    )

    parser.add_argument(
        "--keep-last-values",
        type=str,
        default="20000,25000,30000,35000,40000",
        help="Comma-separated list of keep_last_n_tokens values to test (default: 20000,25000,30000,35000,40000)",
    )

    return parser.parse_args()


def get_time_range(
    hours: Optional[int], days: Optional[int]
) -> Tuple[datetime, datetime]:
    """Get the time range for the query."""
    now = datetime.utcnow()

    if days is not None:
        from_datetime = now - timedelta(days=days)
    else:
        from_datetime = now - timedelta(hours=hours or 24)

    return from_datetime, now


def process_conversations(
    conversations: Dict[str, List[Dict]],
    project_id: str,
    dataset_name: str,
    tenant_filter: Optional[str],
    summarization_threshold: int,
) -> Tuple[float, float]:
    """Process conversations and return costs with and without summarization.

    Returns:
        Tuple of (cost_with_summarization, cost_without_summarization)
    """
    # Calculate cost with summarization
    cost_with_summarization = calculate_cost_with_summarization(
        conversations, summarization_threshold
    )

    # Get all request IDs for prompt cache usage lookup
    request_ids = []
    for exchanges in conversations.values():
        request_ids.extend([exchange["request_id"] for exchange in exchanges])

    # Fetch prompt cache usage data
    prompt_cache_usage = fetch_prompt_cache_usage_data(
        project_id, dataset_name, request_ids, tenant_filter
    )

    # Calculate cost without summarization
    cost_without_summarization = calculate_cost_without_summarization(
        conversations, prompt_cache_usage
    )

    return cost_with_summarization, cost_without_summarization


def print_tenant_results(tenant_desc: str, cost_without: float, cost_with: float):
    """Print results for a specific tenant or dataset."""
    savings = cost_without - cost_with
    savings_percent = (savings / cost_without * 100) if cost_without > 0 else 0

    print(f"\n--- Results for {tenant_desc} ---")
    print(f"Cost without summarization: ${cost_without:.2f}")
    print(f"Cost with summarization:    ${cost_with:.2f}")
    print(f"Savings:                    ${savings:.2f} ({savings_percent:.1f}%)")


def print_final_results(
    cost_with_summarization: float, cost_without_summarization: float
):
    """Print final aggregated results."""
    total_savings = cost_without_summarization - cost_with_summarization
    total_savings_percent = (
        (total_savings / cost_without_summarization * 100)
        if cost_without_summarization > 0
        else 0
    )

    print(f"\n{'='*50}")
    print("TOTAL RESULTS")
    print(f"{'='*50}")
    print(f"Total cost without summarization: ${cost_without_summarization:.2f}")
    print(f"Total cost with summarization:    ${cost_with_summarization:.2f}")
    print(
        f"Total savings:                    ${total_savings:.2f} ({total_savings_percent:.1f}%)"
    )

    if total_savings > 0:
        print(
            f"\n✅ Summarization would save ${total_savings:.2f} ({total_savings_percent:.1f}%)"
        )
    elif total_savings < 0:
        print(
            f"\n❌ Summarization would cost an additional ${-total_savings:.2f} ({-total_savings_percent:.1f}%)"
        )
    else:
        print("\n➖ No cost difference")


def run_threshold_comparison(
    conversations: Dict[str, List[Dict]],
    project_id: str,
    dataset_name: str,
    tenant_filter: Optional[str],
    threshold_values: List[int],
    cost_without_summarization: float,
) -> None:
    """Run comparison across multiple summarization thresholds."""
    print(f"\n{'='*80}")
    print("SUMMARIZATION THRESHOLD COMPARISON")
    print(f"{'='*80}")

    results = []

    for threshold in tqdm(threshold_values, desc="Testing thresholds"):
        cost_with = calculate_cost_with_summarization(
            conversations,
            threshold,
            keep_last_n_tokens=30_000,
            cache_expiration_enabled=False,
        )
        savings = cost_without_summarization - cost_with
        savings_percent = (
            (savings / cost_without_summarization * 100)
            if cost_without_summarization > 0
            else 0
        )

        results.append(
            {
                "threshold": threshold,
                "cost_with": cost_with,
                "savings": savings,
                "savings_percent": savings_percent,
            }
        )

    # Print comparison table
    print(
        f"\n{'Threshold':<12} {'Cost w/ Sum':<12} {'Savings':<12} {'Savings %':<12} {'Status':<10}"
    )
    print("-" * 70)

    for result in results:
        threshold_str = f"{result['threshold']:,}"
        cost_str = f"${result['cost_with']:.2f}"
        savings_str = f"${result['savings']:.2f}"
        percent_str = f"{result['savings_percent']:.1f}%"

        if result["savings"] > 0:
            status = "✅ Save"
        elif result["savings"] < 0:
            status = "❌ Cost"
        else:
            status = "➖ Same"

        print(
            f"{threshold_str:<12} {cost_str:<12} {savings_str:<12} {percent_str:<12} {status:<10}"
        )

    # Find optimal threshold
    best_result = max(results, key=lambda x: x["savings"])
    print(f"\n🎯 OPTIMAL THRESHOLD: {best_result['threshold']:,} tokens")
    print(
        f"   Maximum savings: ${best_result['savings']:.2f} ({best_result['savings_percent']:.1f}%)"
    )

    # Show threshold analysis
    print("\n📊 THRESHOLD ANALYSIS:")
    print(f"   • Baseline cost (no summarization): ${cost_without_summarization:.2f}")
    print(f"   • Best threshold: {best_result['threshold']:,} tokens")
    print(
        f"   • Savings range: ${min(r['savings'] for r in results):.2f} to ${max(r['savings'] for r in results):.2f}"
    )


def run_2d_parameter_comparison(
    conversations: Dict[str, List[Dict]],
    project_id: str,
    dataset_name: str,
    tenant_filter: Optional[str],
    threshold_values: List[int],
    keep_last_values: List[int],
    cost_without_summarization: float,
) -> None:
    """Run 2D comparison across threshold and keep_last_n_tokens values with and without cache expiration."""
    print(f"\n{'='*100}")
    print("2D PARAMETER OPTIMIZATION: THRESHOLD vs KEEP_LAST_N_TOKENS")
    print(f"{'='*100}")

    # Calculate all combinations for both cache scenarios
    results_matrix_no_cache = {}
    results_matrix_with_cache = {}
    total_combinations = (
        len(threshold_values) * len(keep_last_values) * 2
    )  # x2 for both cache scenarios

    with tqdm(total=total_combinations, desc="Testing parameter combinations") as pbar:
        # First calculate without cache expiration
        for keep_last in keep_last_values:
            results_matrix_no_cache[keep_last] = {}
            for threshold in threshold_values:
                cost_with = calculate_cost_with_summarization(
                    conversations,
                    threshold,
                    keep_last_n_tokens=keep_last,
                    cache_expiration_enabled=False,
                )
                savings = cost_without_summarization - cost_with
                savings_percent = (
                    (savings / cost_without_summarization * 100)
                    if cost_without_summarization > 0
                    else 0
                )
                results_matrix_no_cache[keep_last][threshold] = savings_percent
                pbar.update(1)

        # Then calculate with cache expiration
        for keep_last in keep_last_values:
            results_matrix_with_cache[keep_last] = {}
            for threshold in threshold_values:
                cost_with = calculate_cost_with_summarization(
                    conversations,
                    threshold,
                    keep_last_n_tokens=keep_last,
                    cache_expiration_enabled=True,
                )
                savings = cost_without_summarization - cost_with
                savings_percent = (
                    (savings / cost_without_summarization * 100)
                    if cost_without_summarization > 0
                    else 0
                )
                results_matrix_with_cache[keep_last][threshold] = savings_percent
                pbar.update(1)

    # Helper function to print a table
    def print_table(results_matrix, title):
        print(f"\n{title}")
        print("=" * 100)

        # Create header row
        header = f"{'Keep Last':<12}"
        for threshold in threshold_values:
            header += f"{threshold//1000}K{'':<8}"
        print(header)
        print("-" * 100)

        # Print data rows
        best_savings = -float("inf")
        best_params = None

        for keep_last in keep_last_values:
            row = f"{keep_last//1000}K{'':<8}"
            for threshold in threshold_values:
                savings_percent = results_matrix[keep_last][threshold]

                # Track best combination
                if savings_percent > best_savings:
                    best_savings = savings_percent
                    best_params = (keep_last, threshold)

                # Color coding for display
                if savings_percent > 40:
                    status = "🟢"
                elif savings_percent > 20:
                    status = "🟡"
                elif savings_percent > 0:
                    status = "🟠"
                else:
                    status = "🔴"

                cell = f"{status}{savings_percent:5.1f}%{'':<4}"
                row += cell
            print(row)

        return best_savings, best_params

    # Print both tables
    print(f"\nSavings Percentage Tables (Baseline: ${cost_without_summarization:.2f})")

    best_savings_no_cache, best_params_no_cache = print_table(
        results_matrix_no_cache, "WITHOUT Cache Expiration"
    )

    best_savings_with_cache, best_params_with_cache = print_table(
        results_matrix_with_cache, "WITH Cache Expiration (5min timeout)"
    )

    # Print legend
    print("\n📊 LEGEND:")
    print("🟢 Excellent (>40%)  🟡 Good (20-40%)  🟠 Fair (0-20%)  🔴 Poor (<0%)")

    # Compare optimal parameters from both scenarios
    print("\n🎯 OPTIMAL PARAMETERS COMPARISON:")

    if best_params_no_cache:
        best_keep_last_no_cache, best_threshold_no_cache = best_params_no_cache
        best_cost_no_cache = calculate_cost_with_summarization(
            conversations,
            best_threshold_no_cache,
            keep_last_n_tokens=best_keep_last_no_cache,
            cache_expiration_enabled=False,
        )
        best_savings_amount_no_cache = cost_without_summarization - best_cost_no_cache

        print("WITHOUT Cache Expiration:")
        print(f"   • Keep Last N Tokens: {best_keep_last_no_cache:,}")
        print(f"   • Summarization Threshold: {best_threshold_no_cache:,}")
        print(
            f"   • Maximum Savings: ${best_savings_amount_no_cache:.2f} ({best_savings_no_cache:.1f}%)"
        )
        print(f"   • Cost with optimization: ${best_cost_no_cache:.2f}")

    if best_params_with_cache:
        best_keep_last_with_cache, best_threshold_with_cache = best_params_with_cache
        best_cost_with_cache = calculate_cost_with_summarization(
            conversations,
            best_threshold_with_cache,
            keep_last_n_tokens=best_keep_last_with_cache,
            cache_expiration_enabled=True,
        )
        best_savings_amount_with_cache = (
            cost_without_summarization - best_cost_with_cache
        )

        print("\nWITH Cache Expiration:")
        print(f"   • Keep Last N Tokens: {best_keep_last_with_cache:,}")
        print(f"   • Summarization Threshold: {best_threshold_with_cache:,}")
        print(
            f"   • Maximum Savings: ${best_savings_amount_with_cache:.2f} ({best_savings_with_cache:.1f}%)"
        )
        print(f"   • Cost with optimization: ${best_cost_with_cache:.2f}")

    # Show cache impact analysis
    if best_params_no_cache and best_params_with_cache:
        cache_impact = best_savings_no_cache - best_savings_with_cache
        print("\n📊 CACHE EXPIRATION IMPACT:")
        print(f"   • Savings difference: {cache_impact:.1f} percentage points")
        if cache_impact > 0:
            print(f"   • Cache expiration reduces savings by {cache_impact:.1f}%")
        elif cache_impact < 0:
            print(f"   • Cache expiration improves savings by {-cache_impact:.1f}%")
        else:
            print("   • Cache expiration has no impact on savings")

    # Show recommendations
    print("\n💡 RECOMMENDATIONS:")
    best_overall_savings = max(best_savings_no_cache, best_savings_with_cache)
    if best_overall_savings > 30:
        print(
            f"   ✅ Strong optimization opportunity: {best_overall_savings:.1f}% savings achievable"
        )
        if best_savings_no_cache > best_savings_with_cache:
            print("   • Recommend: Disable cache expiration for maximum savings")
        else:
            print("   • Recommend: Enable cache expiration for maximum savings")
    elif best_overall_savings > 10:
        print(
            f"   ⚠️  Moderate optimization opportunity: {best_overall_savings:.1f}% savings achievable"
        )
    else:
        print(
            f"   ❌ Limited optimization opportunity: Only {best_overall_savings:.1f}% savings achievable"
        )


def main():
    """Main function to orchestrate the cost evaluation."""
    args = parse_arguments()

    # Validate arguments
    if args.tenant and (args.prod or args.staging):
        logger.error("Cannot specify both --tenant and --prod/--staging options")
        return

    # Get time range
    from_datetime, to_datetime = get_time_range(args.hours, args.days)
    logger.info(f"Analyzing data from {from_datetime} to {to_datetime}")

    # Determine dataset configuration
    if args.prod:
        project_id = "system-services-prod"
        dataset_name = "us_prod_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using production dataset for all tenants")
    elif args.staging:
        project_id = "system-services-prod"
        dataset_name = "us_staging_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using staging dataset for all tenants")
    elif args.tenant:
        tenant = get_tenant(args.tenant)
        project_id = tenant.project_id
        dataset_name = tenant.analytics_dataset_name
        tenant_filter = tenant.name
        logger.info(f"Analyzing specific tenant: {args.tenant}")
    else:
        # Default: use production dataset with subset of tenants
        project_id = "system-services-prod"
        dataset_name = "us_prod_request_insight_analytics_dataset"
        tenant_names = ["dogfood", "dogfood-shard", "aitutor-pareto", "aitutor-turing"]
        logger.info(f"Using production dataset for tenants: {tenant_names}")

        # Process each tenant separately for better reporting
        total_cost_with_summarization = 0.0
        total_cost_without_summarization = 0.0

        for tenant_name in tenant_names:
            logger.info(f"\n=== Processing tenant: {tenant_name} ===")

            try:
                # Fetch conversations data
                conversations = fetch_conversations_data(
                    project_id=project_id,
                    dataset_name=dataset_name,
                    from_datetime=from_datetime,
                    to_datetime=to_datetime,
                    tenant_filter=tenant_name,
                    num_conversations=args.num_conversations,
                )

                if not conversations:
                    logger.warning(f"No conversations found for tenant {tenant_name}")
                    continue

                # Calculate costs and print results
                tenant_cost_with, tenant_cost_without = process_conversations(
                    conversations,
                    project_id,
                    dataset_name,
                    tenant_name,
                    args.summarization_threshold,
                )

                total_cost_with_summarization += tenant_cost_with
                total_cost_without_summarization += tenant_cost_without

            except Exception as e:
                logger.error(f"Error processing tenant {tenant_name}: {e}")
                continue

        # Print total results
        print_final_results(
            total_cost_with_summarization, total_cost_without_summarization
        )
        return

    # Single dataset/tenant processing
    try:
        # Fetch conversations data
        conversations = fetch_conversations_data(
            project_id=project_id,
            dataset_name=dataset_name,
            from_datetime=from_datetime,
            to_datetime=to_datetime,
            tenant_filter=tenant_filter,
            num_conversations=args.num_conversations,
        )

        if not conversations:
            logger.warning("No conversations found")
            return

        # Check if 2D parameter comparison is requested
        if args.keep_last_range:
            # Parse both threshold and keep_last values
            try:
                threshold_values = [
                    int(x.strip()) for x in args.threshold_values.split(",")
                ]
                keep_last_values = [
                    int(x.strip()) for x in args.keep_last_values.split(",")
                ]
            except ValueError:
                logger.error(
                    "Invalid parameter values. Please provide comma-separated integers."
                )
                return

            logger.info("Running 2D parameter comparison:")
            logger.info(f"  Threshold values: {threshold_values}")
            logger.info(f"  Keep_last values: {keep_last_values}")

            # Calculate cost without summarization first
            request_ids = []
            for exchanges in conversations.values():
                request_ids.extend([exchange["request_id"] for exchange in exchanges])

            prompt_cache_usage = fetch_prompt_cache_usage_data(
                project_id, dataset_name, request_ids, tenant_filter
            )

            cost_without = calculate_cost_without_summarization(
                conversations, prompt_cache_usage
            )

            # Run 2D parameter comparison
            run_2d_parameter_comparison(
                conversations,
                project_id,
                dataset_name,
                tenant_filter,
                threshold_values,
                keep_last_values,
                cost_without,
            )
        # Check if threshold comparison is requested
        elif args.threshold_range:
            # Parse threshold values
            try:
                threshold_values = [
                    int(x.strip()) for x in args.threshold_values.split(",")
                ]
            except ValueError:
                logger.error(
                    "Invalid threshold values. Please provide comma-separated integers."
                )
                return

            logger.info(f"Running threshold comparison with values: {threshold_values}")

            # Calculate cost without summarization first
            request_ids = []
            for exchanges in conversations.values():
                request_ids.extend([exchange["request_id"] for exchange in exchanges])

            prompt_cache_usage = fetch_prompt_cache_usage_data(
                project_id, dataset_name, request_ids, tenant_filter
            )

            cost_without = calculate_cost_without_summarization(
                conversations, prompt_cache_usage
            )

            # Run threshold comparison
            run_threshold_comparison(
                conversations,
                project_id,
                dataset_name,
                tenant_filter,
                threshold_values,
                cost_without,
            )
        else:
            # Regular single threshold analysis
            cost_with, cost_without = process_conversations(
                conversations,
                project_id,
                dataset_name,
                tenant_filter,
                args.summarization_threshold,
            )

            # Print results
            dataset_desc = (
                "production"
                if args.prod
                else "staging"
                if args.staging
                else f"tenant {args.tenant}"
            )
            print_tenant_results(dataset_desc, cost_without, cost_with)
            print_final_results(cost_with, cost_without)

    except Exception as e:
        logger.error(f"Error processing data: {e}")
        return


if __name__ == "__main__":
    main()
