"""Gemini Flash-based compression strategy.

This module implements a compression strategy that uses Gemini Flash LLM
to intelligently summarize tool outputs while preserving important information.
"""

import copy
import json
from typing import List, Dict, Optional, cast
from .chat_history_compression import ChatHistoryCompressor, CompressionStats
from .utils import (
    register_tool_uses,
    is_tool_output_node,
    should_compress_tool,
    find_tool_name_for_result,
    extract_tool_content_summary,
)
from base.prompt_format.common import Exchange, ChatRequestNode, ChatResultNode
from base.third_party_clients.google_genai_client import GoogleGenaiClient


class GeminiFlashCompressor(ChatHistoryCompressor):
    """Use Gemini Flash to intelligently summarize tool outputs."""

    COMPRESSION_PROMPT = """You are an expert at compressing tool outputs while preserving essential information.

Your task is to create a concise summary of the tool output that maintains all critical information needed for the conversation to continue naturally.

Tool: {tool_name}
Tool Use ID: {tool_use_id}
Original Output:
{original_content}

Context from conversation:
{conversation_context}

Please provide a compressed version that:
1. Preserves all essential information and key results
2. Maintains enough detail for the conversation to continue
3. Removes verbose formatting, repetitive content, and unnecessary details
4. Keeps error messages and important status information
5. For code/file content: preserve structure and key elements
6. For search results: keep relevant matches and context

Respond with ONLY the compressed content, no additional commentary."""

    def __init__(self, client: Optional[GoogleGenaiClient] = None):
        super().__init__()
        self.client = client or GoogleGenaiClient(
            project_id="augment-research-gsc",
            region="us-central1",
            model_name="gemini-2.5-flash-preview-04-17",
            temperature=0.3,
            max_output_tokens=1000,
        )
        self.original_tokens = 0
        self.compressed_tokens = 0
        self.tool_compressions: Dict[str, int] = {}

    def compress(self, chat_history: List[Exchange]) -> List[Exchange]:
        """
        Compress the chat history using Gemini Flash for intelligent summarization.

        Args:
            chat_history: List of Exchange objects representing the conversation

        Returns:
            Compressed list of Exchange objects
        """
        if not chat_history:
            # Create empty stats for empty input
            stats = CompressionStats(
                original_tokens=0,
                compressed_tokens=0,
                compression_rate=0,
                exchanges_compressed=0,
                tool_outputs_compressed={},
            )
            self._update_stats(stats)
            return chat_history

        # Register tool uses for lookup
        register_tool_uses(chat_history)

        # Reset compression tracking
        self.original_tokens = 0
        self.compressed_tokens = 0
        self.tool_compressions = {}

        # Build conversation context
        context = self._build_conversation_context(chat_history)

        compressed_history = []
        for i, exchange in enumerate(chat_history):
            compressed_exchange = self._compress_exchange_with_llm(exchange, context, i)
            compressed_history.append(compressed_exchange)

        # Calculate and store stats
        compression_rate = (
            (self.original_tokens - self.compressed_tokens) / self.original_tokens
            if self.original_tokens > 0
            else 0
        )

        stats = CompressionStats(
            original_tokens=self.original_tokens,
            compressed_tokens=self.compressed_tokens,
            compression_rate=compression_rate,
            exchanges_compressed=len(compressed_history),
            tool_outputs_compressed=self.tool_compressions.copy(),
        )
        self._update_stats(stats)

        return compressed_history

    def _build_conversation_context(self, chat_history: List[Exchange]) -> str:
        """Build context about the conversation for better compression.

        Args:
            chat_history: List of exchanges

        Returns:
            Context string describing the conversation
        """
        context_parts = []

        # Extract user messages and agent responses
        for i, exchange in enumerate(chat_history[:5]):  # Limit to first 5 exchanges
            if isinstance(exchange.request_message, list):
                for node in exchange.request_message:
                    if hasattr(node, "text_node") and node.text_node:
                        context_parts.append(f"User: {node.text_node.content[:200]}")
                        break
            elif isinstance(exchange.request_message, str):
                context_parts.append(f"User: {exchange.request_message[:200]}")

            if isinstance(exchange.response_text, list):
                for node in exchange.response_text:
                    # Only ChatResultNode has content attribute
                    if hasattr(node, "content") and node.content:  # type: ignore
                        context_parts.append(f"Agent: {node.content[:200]}")  # type: ignore
                        break
            elif isinstance(exchange.response_text, str):
                context_parts.append(f"Agent: {exchange.response_text[:200]}")

        return "\n".join(context_parts)

    def _compress_exchange_with_llm(
        self, exchange: Exchange, context: str, index: int
    ) -> Exchange:
        """Compress a single exchange using LLM.

        Args:
            exchange: Exchange to compress
            context: Conversation context
            index: Index of exchange in conversation

        Returns:
            Compressed exchange
        """
        # Compress request nodes (tool results)
        if isinstance(exchange.request_message, list):
            compressed_request_message = self._compress_request_nodes_with_llm(
                exchange.request_message, context
            )
            # Create a new Exchange instance since the dataclass is frozen
            return Exchange(
                request_message=compressed_request_message,
                response_text=exchange.response_text,
                request_id=exchange.request_id,
            )
        else:
            # No compression needed, return the original exchange
            return exchange

    def _compress_request_nodes_with_llm(
        self, nodes: List[ChatRequestNode], context: str
    ) -> List[ChatRequestNode]:
        """Compress request nodes using LLM.

        Args:
            nodes: List of request nodes to compress
            context: Conversation context

        Returns:
            List of compressed request nodes
        """
        compressed_nodes = []

        for node in nodes:
            if is_tool_output_node(node) and node.tool_result_node:
                # Find corresponding tool use to get tool name
                tool_name = find_tool_name_for_result(node.tool_result_node.tool_use_id)

                if tool_name and should_compress_tool(tool_name):
                    # Track original content length
                    original_content = node.tool_result_node.content
                    self.original_tokens += len(original_content.split())

                    # Compress with LLM
                    compressed_node = self._compress_tool_output_with_llm(
                        node, tool_name, context
                    )
                    compressed_nodes.append(compressed_node)

                    # Track compressed content length
                    if compressed_node.tool_result_node:
                        compressed_content = compressed_node.tool_result_node.content
                        self.compressed_tokens += len(compressed_content.split())

                    # Track tool compression count
                    self.tool_compressions[tool_name] = (
                        self.tool_compressions.get(tool_name, 0) + 1
                    )
                else:
                    # Don't compress this tool output
                    if node.tool_result_node:
                        content_tokens = len(node.tool_result_node.content.split())
                        self.original_tokens += content_tokens
                        self.compressed_tokens += content_tokens
                    compressed_nodes.append(node)
            else:
                # Not a tool output node, keep as-is
                compressed_nodes.append(node)

        return compressed_nodes

    def _compress_tool_output_with_llm(
        self, node: ChatRequestNode, tool_name: str, context: str
    ) -> ChatRequestNode:
        """Compress a tool output using LLM.

        Args:
            node: Original tool result node
            tool_name: Name of the tool
            context: Conversation context

        Returns:
            Compressed node with LLM-generated summary
        """
        if not node.tool_result_node:
            return node

        original_content = node.tool_result_node.content

        # Skip compression for very short content
        if len(original_content) < 200:
            return node

        # Prepare prompt for LLM
        prompt = self.COMPRESSION_PROMPT.format(
            tool_name=tool_name,
            tool_use_id=node.tool_result_node.tool_use_id,
            original_content=original_content[:4000],  # Limit input size
            conversation_context=context[:1000],  # Limit context size
        )

        try:
            # Generate compressed content using Gemini Flash
            response_generator = self.client.generate_response_stream(
                model_caller="gemini_flash_compressor",
                system_prompt="You are an expert at compressing tool outputs while preserving essential information.",
                cur_message=prompt,
                temperature=0.3,
                max_output_tokens=1000,
            )

            # Collect the response text
            compressed_parts = []
            for response in response_generator:
                if response.text:
                    compressed_parts.append(response.text)

            compressed_content = "".join(compressed_parts).strip()

            # Fallback to original if compression failed or is too similar in length
            if (
                not compressed_content
                or len(compressed_content) > len(original_content) * 0.8
            ):
                return node

            # Create a new tool result node with compressed content
            from base.prompt_format.common import ChatRequestToolResult, ChatRequestNode

            # Assert for type checker (we already checked this at the beginning)
            assert node.tool_result_node is not None

            compressed_tool_result = ChatRequestToolResult(
                tool_use_id=node.tool_result_node.tool_use_id,
                content=compressed_content,
                is_error=node.tool_result_node.is_error,
                request_id=node.tool_result_node.request_id,
                content_nodes=None,
            )

            # Create a new ChatRequestNode with the compressed tool result
            compressed_node = ChatRequestNode(
                id=node.id,
                type=node.type,
                text_node=node.text_node,
                tool_result_node=compressed_tool_result,
                image_node=node.image_node,
                ide_state_node=node.ide_state_node,
                edit_events_node=node.edit_events_node,
            )

            return compressed_node

        except Exception as e:
            # Fallback to original content if LLM compression fails
            print(f"LLM compression failed for {tool_name}: {e}")
            return node
