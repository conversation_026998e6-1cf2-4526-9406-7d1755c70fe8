/**
 * TypeScript types for chat history compression analysis data.
 * These types mirror the Python dataclasses for seamless data transfer.
 */

export interface ToolCompressionResult {
  tool_name: string;
  tool_use_id: string;
  original_tokens: number;
  compressed_tokens: number;
  compression_method: string;
  preserved_info: Record<string, any>;
  compression_rate: number;
  token_savings: number;
}

export interface ExchangeCompressionResult {
  request_id: string;
  tool_compressions: ToolCompressionResult[];
  original_tokens: number;
  compressed_tokens: number;
  compression_rate: number;
  token_savings: number;
}

export interface CompressionQualityMetrics {
  information_retention_score: number;
  context_preservation_score: number;
  tool_output_relevance_score: number;
  overall_quality_score: number;
  average_quality_score: number;
  average_retention_score: number;
  total_evaluated: number;
  quality_distribution: Record<string, number>;
  critical_losses: string[];
  feedback: string;
  average_score: number;
}

export interface ChatHistoryCompressionEvalResult {
  conversation_id: string;
  timestamp: string;
  compressor_name: string;
  exchange_results: ExchangeCompressionResult[];
  total_original_tokens: number;
  total_compressed_tokens: number;
  compression_rate: number;
  tool_compression_stats: Record<string, Record<string, number>>;
  llm_judge_score?: number;
  llm_judge_feedback?: string;
  information_retention_score?: number;
  compression_time_seconds: number;
  evaluation_time_seconds: number;
  token_savings: number;
  exchanges_count: number;
  tools_compressed_count: number;
}

export interface ToolCompressionStats {
  tool_name: string;
  total_compressions: number;
  total_original_tokens: number;
  total_compressed_tokens: number;
  average_compression_rate: number;
  method_distribution: Record<string, number>;
  quality_scores: number[];
  total_token_savings: number;
  average_quality_score: number;
}

export interface CompressionAnalysisSummary {
  analysis_id: string;
  timestamp: string;
  total_conversations: number;
  total_exchanges: number;
  compressor_summaries: Record<string, Record<string, any>>;
  tool_summaries: Record<string, ToolCompressionStats>;
  quality_analysis: Record<string, CompressionQualityMetrics>;
  recommendations: string[];
  best_compressor_by_rate?: string;
  best_compressor_by_quality?: string;
}

export interface CompressionBenchmarkResult {
  benchmark_id: string;
  timestamp: string;
  dataset_info: Record<string, any>;
  compressor_results: Record<string, ChatHistoryCompressionEvalResult[]>;
  summary_stats: Record<string, Record<string, any>>;
  benchmark_time_seconds: number;
  compressor_names: string[];
  total_conversations: number;
}

// Chart data interfaces
export interface ChartDataPoint {
  name: string;
  value: number;
  label?: string;
}

export interface ComparisonChartData {
  compressor: string;
  compression_rate: number;
  quality_score?: number;
  token_savings: number;
  processing_time: number;
}

export interface ToolPerformanceData {
  tool_name: string;
  compression_rate: number;
  total_compressions: number;
  token_savings: number;
  quality_score?: number;
}

// Component props interfaces
export interface CompressionSummaryProps {
  data: CompressionAnalysisSummary;
  showQualityMetrics?: boolean;
}

export interface ExchangeComparisonProps {
  originalExchange: any; // Exchange data structure
  compressedExchange: any; // Exchange data structure
  compressionResult: ExchangeCompressionResult;
}

export interface MetricsDisplayProps {
  benchmarkResult: CompressionBenchmarkResult;
  showDetailedBreakdown?: boolean;
}

export interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  className?: string;
}

export interface FilterControlsProps {
  compressors: string[];
  tools: string[];
  selectedCompressors: string[];
  selectedTools: string[];
  onCompressorChange: (compressors: string[]) => void;
  onToolChange: (tools: string[]) => void;
}

// Utility types
export type SortDirection = 'asc' | 'desc';
export type SortField = 'compression_rate' | 'quality_score' | 'token_savings' | 'processing_time';

export interface SortConfig {
  field: SortField;
  direction: SortDirection;
}
