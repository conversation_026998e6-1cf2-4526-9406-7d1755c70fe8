import { useEffect, useState } from 'react'
import CompressionSummary from './components/CompressionSummary/CompressionSummary'
import ExchangeComparison from './components/ExchangeComparison/ExchangeComparison'
import MetricsDisplay from './components/MetricsDisplay/MetricsDisplay'
import { CompressionAnalysisSummary, CompressionBenchmarkResult } from './types'

interface AppData {
  comparison_summary?: {
    analysis_summary: CompressionAnalysisSummary;
    benchmark_result?: CompressionBenchmarkResult;
    sample_exchanges?: Array<{
      originalExchange: any;
      compressedExchange: any;
      compressionResult: any;
    }>;
  };
  analysis_summary?: CompressionAnalysisSummary;
  benchmark_result?: CompressionBenchmarkResult;
  exchange_data?: {
    originalExchange: any;
    compressedExchange: any;
    compressionResult: any;
  };
  report_type?: 'summary' | 'benchmark' | 'exchange' | 'comprehensive';
}

function App() {
  const [data, setData] = useState<AppData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Try to load data from data.json file
    fetch('./data.json')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        return response.json()
      })
      .then((jsonData: AppData) => {
        setData(jsonData)
        setLoading(false)
      })
      .catch(err => {
        console.error('Error loading data:', err)
        setError(`Failed to load data: ${err.message}`)
        setLoading(false)
      })
  }, [])

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px'
      }}>
        Loading compression analysis data...
      </div>
    )
  }

  if (error) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        color: '#d32f2f'
      }}>
        <h2>Error Loading Data</h2>
        <p>{error}</p>
        <p style={{ color: '#666', fontSize: '14px' }}>
          Make sure the data.json file is present and properly formatted.
        </p>
      </div>
    )
  }

  if (!data) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        No data available
      </div>
    )
  }

  // Render based on the type of data available
  const renderContent = () => {
    // Comprehensive report with multiple sections
    if (data.comparison_summary) {
      const { analysis_summary, benchmark_result, sample_exchanges } = data.comparison_summary
      return (
        <div>
          <h1>Comprehensive Compression Analysis Report</h1>

          <section style={{ marginBottom: '40px' }}>
            <h2>Analysis Summary</h2>
            <CompressionSummary data={analysis_summary} showQualityMetrics={true} />
          </section>

          {benchmark_result && (
            <section style={{ marginBottom: '40px' }}>
              <h2>Benchmark Results</h2>
              <MetricsDisplay benchmarkResult={benchmark_result} showDetailedBreakdown={true} />
            </section>
          )}

          {sample_exchanges && sample_exchanges.length > 0 && (
            <section style={{ marginBottom: '40px' }}>
              <h2>Sample Exchange Comparisons</h2>
              {sample_exchanges.map((exchange, index) => (
                <div key={index} style={{ marginBottom: '30px' }}>
                  <h3>Exchange {index + 1}</h3>
                  <ExchangeComparison
                    originalExchange={exchange.originalExchange}
                    compressedExchange={exchange.compressedExchange}
                    compressionResult={exchange.compressionResult}
                  />
                </div>
              ))}
            </section>
          )}
        </div>
      )
    }

    // Analysis summary report
    if (data.analysis_summary) {
      return (
        <div>
          <h1>Compression Analysis Summary</h1>
          <CompressionSummary data={data.analysis_summary} showQualityMetrics={true} />
        </div>
      )
    }

    // Benchmark report
    if (data.benchmark_result) {
      return (
        <div>
          <h1>Compression Benchmark Results</h1>
          <MetricsDisplay benchmarkResult={data.benchmark_result} showDetailedBreakdown={true} />
        </div>
      )
    }

    // Exchange comparison report
    if (data.exchange_data) {
      return (
        <div>
          <h1>Exchange Comparison</h1>
          <ExchangeComparison
            originalExchange={data.exchange_data.originalExchange}
            compressedExchange={data.exchange_data.compressedExchange}
            compressionResult={data.exchange_data.compressionResult}
          />
        </div>
      )
    }

    return (
      <div>
        <h1>Compression Analysis Report</h1>
        <p>No recognized data format found.</p>
        <pre style={{ background: '#f5f5f5', padding: '20px', borderRadius: '8px' }}>
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    )
  }

  return (
    <div style={{
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '20px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
    }}>
      {renderContent()}
    </div>
  )
}

export default App
