import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, Legend, <PERSON>Chart, Line, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON><PERSON>, Scatter } from 'recharts';
import { ChevronDown, ChevronRight, Filter, TrendingUp, Clock, Target } from 'lucide-react';
import { MetricsDisplayProps, ComparisonChartData, SortConfig } from '../../types';
import './MetricsDisplay.css';

const MetricsDisplay: React.FC<MetricsDisplayProps> = ({
  benchmarkResult,
  showDetailedBreakdown = true
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    overview: true,
    comparison: true,
    performance: false,
    detailed: false
  });

  const [selectedCompressors, setSelectedCompressors] = useState<string[]>(
    benchmarkResult.compressor_names
  );

  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'compression_rate',
    direction: 'desc'
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleSort = (field: SortConfig['field']) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  // Prepare comparison data
  const comparisonData: ComparisonChartData[] = useMemo(() => {
    return benchmarkResult.compressor_names
      .filter(name => selectedCompressors.includes(name))
      .map(compressor => {
        const stats = benchmarkResult.summary_stats[compressor];
        const results = benchmarkResult.compressor_results[compressor] || [];

        const avgQuality = results.reduce((sum, r) =>
          sum + (r.llm_judge_score || 0), 0) / Math.max(results.length, 1);

        const avgTime = results.reduce((sum, r) =>
          sum + r.compression_time_seconds, 0) / Math.max(results.length, 1);

        return {
          compressor,
          compression_rate: (stats?.overall_compression_rate || 0) * 100,
          quality_score: avgQuality * 100,
          token_savings: stats?.total_token_savings || 0,
          processing_time: avgTime
        };
      })
      .sort((a, b) => {
        const aVal = a[sortConfig.field];
        const bVal = b[sortConfig.field];
        const multiplier = sortConfig.direction === 'asc' ? 1 : -1;
        return (aVal - bVal) * multiplier;
      });
  }, [benchmarkResult, selectedCompressors, sortConfig]);

  // Performance over time data
  const performanceData = useMemo(() => {
    const allResults = Object.values(benchmarkResult.compressor_results).flat();
    return allResults
      .filter(r => selectedCompressors.includes(r.compressor_name))
      .map((result, index) => ({
        index,
        compressor: result.compressor_name,
        compression_rate: result.compression_rate * 100,
        processing_time: result.compression_time_seconds,
        token_savings: result.token_savings
      }));
  }, [benchmarkResult, selectedCompressors]);

  const getSortIcon = (field: SortConfig['field']) => {
    if (sortConfig.field !== field) return null;
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="metrics-display">
      <div className="metrics-header">
        <h1>Compression Benchmark Results</h1>
        <div className="benchmark-info">
          <div className="info-item">
            <Target className="info-icon" />
            <span>Benchmark ID: {benchmarkResult.benchmark_id}</span>
          </div>
          <div className="info-item">
            <Clock className="info-icon" />
            <span>Duration: {benchmarkResult.benchmark_time_seconds.toFixed(1)}s</span>
          </div>
          <div className="info-item">
            <TrendingUp className="info-icon" />
            <span>Conversations: {benchmarkResult.total_conversations}</span>
          </div>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="filter-controls">
        <div className="filter-section">
          <Filter className="filter-icon" />
          <span>Compressors:</span>
          <div className="checkbox-group">
            {benchmarkResult.compressor_names.map(compressor => (
              <label key={compressor} className="checkbox-label">
                <input
                  type="checkbox"
                  checked={selectedCompressors.includes(compressor)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedCompressors(prev => [...prev, compressor]);
                    } else {
                      setSelectedCompressors(prev => prev.filter(c => c !== compressor));
                    }
                  }}
                />
                {compressor}
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Overview Section */}
      <div className="metrics-section">
        <div
          className="section-header"
          onClick={() => toggleSection('overview')}
        >
          {expandedSections.overview ? <ChevronDown /> : <ChevronRight />}
          <h2>Overview</h2>
        </div>
        {expandedSections.overview && (
          <div className="section-content">
            <div className="overview-grid">
              <div className="overview-card">
                <h3>Dataset Info</h3>
                <div className="card-content">
                  <div className="stat-row">
                    <span>Total Conversations:</span>
                    <span>{benchmarkResult.dataset_info.total_conversations}</span>
                  </div>
                  <div className="stat-row">
                    <span>Total Exchanges:</span>
                    <span>{benchmarkResult.dataset_info.total_exchanges}</span>
                  </div>
                  <div className="stat-row">
                    <span>Compressors Tested:</span>
                    <span>{benchmarkResult.compressor_names.length}</span>
                  </div>
                </div>
              </div>

              <div className="overview-card">
                <h3>Best Performers</h3>
                <div className="card-content">
                  {comparisonData.length > 0 && (
                    <>
                      <div className="stat-row">
                        <span>Best Compression Rate:</span>
                        <span>{comparisonData[0]?.compressor} ({comparisonData[0]?.compression_rate.toFixed(1)}%)</span>
                      </div>
                      <div className="stat-row">
                        <span>Most Token Savings:</span>
                        <span>{comparisonData.reduce((best, curr) =>
                          curr.token_savings > best.token_savings ? curr : best
                        ).compressor}</span>
                      </div>
                      <div className="stat-row">
                        <span>Fastest Processing:</span>
                        <span>{comparisonData.reduce((best, curr) =>
                          curr.processing_time < best.processing_time ? curr : best
                        ).compressor}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Comparison Section */}
      <div className="metrics-section">
        <div
          className="section-header"
          onClick={() => toggleSection('comparison')}
        >
          {expandedSections.comparison ? <ChevronDown /> : <ChevronRight />}
          <h2>Compressor Comparison</h2>
        </div>
        {expandedSections.comparison && (
          <div className="section-content">
            <div className="chart-container">
              <h3>Compression Rate vs Quality Score</h3>
              <ResponsiveContainer width="100%" height={400}>
                <ScatterChart data={comparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="compression_rate"
                    name="Compression Rate"
                    unit="%"
                  />
                  <YAxis
                    dataKey="quality_score"
                    name="Quality Score"
                    unit="%"
                  />
                  <Tooltip
                    formatter={(value, name) => [`${value}%`, name]}
                    labelFormatter={(value) => `Compression Rate: ${value}%`}
                  />
                  <Scatter dataKey="quality_score" fill="#8884d8" />
                </ScatterChart>
              </ResponsiveContainer>
            </div>

            <div className="comparison-table">
              <table>
                <thead>
                  <tr>
                    <th>Compressor</th>
                    <th
                      onClick={() => handleSort('compression_rate')}
                      className="sortable"
                    >
                      Compression Rate {getSortIcon('compression_rate')}
                    </th>
                    <th
                      onClick={() => handleSort('quality_score')}
                      className="sortable"
                    >
                      Quality Score {getSortIcon('quality_score')}
                    </th>
                    <th
                      onClick={() => handleSort('token_savings')}
                      className="sortable"
                    >
                      Token Savings {getSortIcon('token_savings')}
                    </th>
                    <th
                      onClick={() => handleSort('processing_time')}
                      className="sortable"
                    >
                      Avg Time {getSortIcon('processing_time')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {comparisonData.map((item) => (
                    <tr key={item.compressor}>
                      <td className="compressor-name">{item.compressor}</td>
                      <td className="rate-cell">{item.compression_rate.toFixed(1)}%</td>
                      <td className="quality-cell">{item.quality_score?.toFixed(1) || 'N/A'}%</td>
                      <td className="savings-cell">{item.token_savings.toLocaleString()}</td>
                      <td className="time-cell">{item.processing_time.toFixed(2)}s</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* Performance Analysis Section */}
      <div className="metrics-section">
        <div
          className="section-header"
          onClick={() => toggleSection('performance')}
        >
          {expandedSections.performance ? <ChevronDown /> : <ChevronRight />}
          <h2>Performance Analysis</h2>
        </div>
        {expandedSections.performance && (
          <div className="section-content">
            <div className="chart-container">
              <h3>Compression Rate Distribution</h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={comparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="compressor" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}%`, 'Compression Rate']} />
                  <Bar dataKey="compression_rate" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            <div className="chart-container">
              <h3>Processing Time vs Token Savings</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="index" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="processing_time"
                    stroke="#8884d8"
                    name="Processing Time (s)"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="token_savings"
                    stroke="#82ca9d"
                    name="Token Savings"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>

      {/* Detailed Breakdown Section */}
      {showDetailedBreakdown && (
        <div className="metrics-section">
          <div
            className="section-header"
            onClick={() => toggleSection('detailed')}
          >
            {expandedSections.detailed ? <ChevronDown /> : <ChevronRight />}
            <h2>Detailed Breakdown</h2>
          </div>
          {expandedSections.detailed && (
            <div className="section-content">
              <div className="detailed-stats">
                {Object.entries(benchmarkResult.summary_stats)
                  .filter(([name]) => selectedCompressors.includes(name))
                  .map(([compressor, stats]) => (
                    <div key={compressor} className="detailed-card">
                      <h3>{compressor}</h3>
                      <div className="stats-grid">
                        <div className="stat-item">
                          <span className="stat-label">Total Conversations:</span>
                          <span className="stat-value">{stats.total_conversations}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Original Tokens:</span>
                          <span className="stat-value">{stats.total_original_tokens?.toLocaleString()}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Compressed Tokens:</span>
                          <span className="stat-value">{stats.total_compressed_tokens?.toLocaleString()}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Compression Rate:</span>
                          <span className="stat-value">{((stats.overall_compression_rate || 0) * 100).toFixed(1)}%</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Token Savings:</span>
                          <span className="stat-value savings">{stats.total_token_savings?.toLocaleString()}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Avg Time:</span>
                          <span className="stat-value">{stats.avg_compression_time_seconds?.toFixed(2)}s</span>
                        </div>
                      </div>

                      {stats.tool_statistics && (
                        <div className="tool-breakdown">
                          <h4>Tool Statistics</h4>
                          <div className="tool-stats-grid">
                            {Object.entries(stats.tool_statistics).map(([tool, toolStats]) => (
                              <div key={tool} className="tool-stat-item">
                                <span className="tool-name">{tool}</span>
                                <span className="tool-rate">{(((toolStats as any).compression_rate || 0) * 100).toFixed(1)}%</span>
                                <span className="tool-count">{(toolStats as any).count} uses</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MetricsDisplay;
