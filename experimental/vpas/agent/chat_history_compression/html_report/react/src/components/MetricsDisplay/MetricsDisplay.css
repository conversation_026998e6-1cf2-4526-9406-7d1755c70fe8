.metrics-display {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.metrics-header {
  margin-bottom: 30px;
}

.metrics-header h1 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: 600;
}

.benchmark-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 0.9rem;
}

.info-icon {
  width: 16px;
  height: 16px;
}

.filter-controls {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-icon {
  width: 20px;
  height: 20px;
  color: #6c757d;
}

.checkbox-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #495057;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.metrics-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background: #e9ecef;
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.section-content {
  padding: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #667eea;
}

.overview-card h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-row span:first-child {
  color: #6c757d;
  font-weight: 500;
}

.stat-row span:last-child {
  color: #2c3e50;
  font-weight: 600;
}

.chart-container {
  margin-bottom: 30px;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.comparison-table {
  overflow-x: auto;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.comparison-table th,
.comparison-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.comparison-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.comparison-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.comparison-table th.sortable:hover {
  background: #e9ecef;
}

.comparison-table tr:hover {
  background: #f8f9fa;
}

.compressor-name {
  font-weight: 600;
  color: #2c3e50;
}

.rate-cell {
  color: #17a2b8;
  font-weight: 600;
}

.quality-cell {
  color: #28a745;
  font-weight: 600;
}

.savings-cell {
  color: #ffc107;
  font-weight: 600;
}

.time-cell {
  color: #6c757d;
  font-weight: 500;
}

.detailed-stats {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.detailed-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border-left: 4px solid #667eea;
}

.detailed-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.stat-value.savings {
  color: #28a745;
}

.tool-breakdown {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.tool-breakdown h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.tool-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.tool-stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border-left: 3px solid #ffc658;
}

.tool-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.tool-rate {
  color: #17a2b8;
  font-weight: 600;
  font-size: 0.85rem;
}

.tool-count {
  color: #6c757d;
  font-size: 0.8rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .metrics-display {
    padding: 15px;
  }

  .metrics-header h1 {
    font-size: 2rem;
  }

  .benchmark-info {
    flex-direction: column;
    gap: 10px;
  }

  .filter-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .tool-stats-grid {
    grid-template-columns: 1fr;
  }

  .comparison-table {
    font-size: 0.85rem;
  }

  .comparison-table th,
  .comparison-table td {
    padding: 8px 10px;
  }
}
