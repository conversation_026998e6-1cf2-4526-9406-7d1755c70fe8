.exchange-comparison {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.comparison-header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.exchange-id {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 0.9rem;
}

.info-icon {
  width: 16px;
  height: 16px;
}

.comparison-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background: #e9ecef;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  flex: 1;
}

.token-count,
.tool-count {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.section-content {
  padding: 20px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-card.excellent {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-card.good {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.stat-card.fair {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-card.poor {
  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.stat-icon {
  width: 24px;
  height: 24px;
  opacity: 0.9;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
  margin-top: 4px;
}

.token-breakdown {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-item .label {
  color: #6c757d;
  font-weight: 500;
}

.breakdown-item .value {
  color: #2c3e50;
  font-weight: 600;
}

.breakdown-item .value.savings {
  color: #28a745;
  font-weight: 700;
}

.exchange-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
  border-left: 4px solid #6c757d;
}

.exchange-content.original {
  border-left-color: #dc3545;
}

.exchange-content.compressed {
  border-left-color: #28a745;
}

.exchange-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #2c3e50;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tool-compressions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tool-compression-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #ffc658;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tool-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.compression-method {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tool-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.tool-stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tool-stat .label {
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 500;
}

.tool-stat .value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1rem;
}

.tool-stat .value.rate {
  color: #17a2b8;
}

.tool-stat .value.savings {
  color: #28a745;
}

.preserved-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.preserved-info h5 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.info-key {
  color: #6c757d;
  font-size: 0.85rem;
  text-transform: capitalize;
}

.info-value {
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .exchange-comparison {
    padding: 15px;
  }

  .comparison-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .comparison-header h2 {
    font-size: 1.5rem;
  }

  .overview-stats {
    grid-template-columns: 1fr;
  }

  .tool-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .breakdown-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
