import React, { useState } from 'react';
import { ChevronDown, ChevronRight, FileText, Zap, TrendingDown, Info } from 'lucide-react';
import { ExchangeComparisonProps } from '../../types';
import './ExchangeComparison.css';

const ExchangeComparison: React.FC<ExchangeComparisonProps> = ({
  originalExchange,
  compressedExchange,
  compressionResult
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    overview: true,
    original: false,
    compressed: false,
    tools: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const formatExchangeContent = (exchange: any) => {
    if (!exchange) return 'No content available';

    let content = '';

    // Format request message
    if (exchange.request_message) {
      if (Array.isArray(exchange.request_message)) {
        exchange.request_message.forEach((node: any) => {
          if (node.type === 'TEXT' && node.text_node) {
            content += `User: ${node.text_node.content}\n\n`;
          } else if (node.type === 'TOOL_RESULT' && node.tool_result_node) {
            const toolContent = node.tool_result_node.content;
            const truncated = toolContent.length > 500 ?
              toolContent.substring(0, 500) + '...[truncated]' :
              toolContent;
            content += `Tool Result: ${truncated}\n\n`;
          }
        });
      } else if (typeof exchange.request_message === 'string') {
        content += `User: ${exchange.request_message}\n\n`;
      }
    }

    // Format response text
    if (exchange.response_text) {
      if (Array.isArray(exchange.response_text)) {
        exchange.response_text.forEach((node: any) => {
          if (node.type === 'RAW_RESPONSE') {
            content += `Agent: ${node.content}\n\n`;
          } else if (node.type === 'TOOL_USE' && node.tool_use) {
            content += `Tool Use: ${node.tool_use.name}(${JSON.stringify(node.tool_use.input)})\n\n`;
          }
        });
      } else if (typeof exchange.response_text === 'string') {
        content += `Agent: ${exchange.response_text}\n\n`;
      }
    }

    return content.trim() || 'No content available';
  };

  const getCompressionSummary = () => {
    const rate = compressionResult.compression_rate * 100;
    const savings = compressionResult.token_savings;
    const toolCount = compressionResult.tool_compressions.length;

    return {
      rate: rate.toFixed(1),
      savings,
      toolCount,
      status: rate > 50 ? 'excellent' : rate > 30 ? 'good' : rate > 10 ? 'fair' : 'poor'
    };
  };

  const summary = getCompressionSummary();

  return (
    <div className="exchange-comparison">
      <div className="comparison-header">
        <h2>Exchange Comparison</h2>
        <div className="exchange-id">
          <Info className="info-icon" />
          <span>Request ID: {compressionResult.request_id}</span>
        </div>
      </div>

      {/* Overview Section */}
      <div className="comparison-section">
        <div
          className="section-header"
          onClick={() => toggleSection('overview')}
        >
          {expandedSections.overview ? <ChevronDown /> : <ChevronRight />}
          <h3>Compression Overview</h3>
        </div>
        {expandedSections.overview && (
          <div className="section-content">
            <div className="overview-stats">
              <div className={`stat-card ${summary.status}`}>
                <TrendingDown className="stat-icon" />
                <div>
                  <div className="stat-value">{summary.rate}%</div>
                  <div className="stat-label">Compression Rate</div>
                </div>
              </div>
              <div className="stat-card">
                <FileText className="stat-icon" />
                <div>
                  <div className="stat-value">{summary.savings}</div>
                  <div className="stat-label">Tokens Saved</div>
                </div>
              </div>
              <div className="stat-card">
                <Zap className="stat-icon" />
                <div>
                  <div className="stat-value">{summary.toolCount}</div>
                  <div className="stat-label">Tools Compressed</div>
                </div>
              </div>
            </div>

            <div className="token-breakdown">
              <div className="breakdown-item">
                <span className="label">Original Tokens:</span>
                <span className="value">{compressionResult.original_tokens.toLocaleString()}</span>
              </div>
              <div className="breakdown-item">
                <span className="label">Compressed Tokens:</span>
                <span className="value">{compressionResult.compressed_tokens.toLocaleString()}</span>
              </div>
              <div className="breakdown-item">
                <span className="label">Tokens Saved:</span>
                <span className="value savings">{compressionResult.token_savings.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Original Exchange Section */}
      <div className="comparison-section">
        <div
          className="section-header"
          onClick={() => toggleSection('original')}
        >
          {expandedSections.original ? <ChevronDown /> : <ChevronRight />}
          <h3>Original Exchange</h3>
          <span className="token-count">({compressionResult.original_tokens} tokens)</span>
        </div>
        {expandedSections.original && (
          <div className="section-content">
            <div className="exchange-content original">
              <pre>{formatExchangeContent(originalExchange)}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Compressed Exchange Section */}
      <div className="comparison-section">
        <div
          className="section-header"
          onClick={() => toggleSection('compressed')}
        >
          {expandedSections.compressed ? <ChevronDown /> : <ChevronRight />}
          <h3>Compressed Exchange</h3>
          <span className="token-count">({compressionResult.compressed_tokens} tokens)</span>
        </div>
        {expandedSections.compressed && (
          <div className="section-content">
            <div className="exchange-content compressed">
              <pre>{formatExchangeContent(compressedExchange)}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Tool Compressions Section */}
      {compressionResult.tool_compressions.length > 0 && (
        <div className="comparison-section">
          <div
            className="section-header"
            onClick={() => toggleSection('tools')}
          >
            {expandedSections.tools ? <ChevronDown /> : <ChevronRight />}
            <h3>Tool Compressions</h3>
            <span className="tool-count">({compressionResult.tool_compressions.length} tools)</span>
          </div>
          {expandedSections.tools && (
            <div className="section-content">
              <div className="tool-compressions">
                {compressionResult.tool_compressions.map((toolComp, index) => (
                  <div key={index} className="tool-compression-card">
                    <div className="tool-header">
                      <h4>{toolComp.tool_name}</h4>
                      <span className="compression-method">{toolComp.compression_method}</span>
                    </div>

                    <div className="tool-stats">
                      <div className="tool-stat">
                        <span className="label">Original:</span>
                        <span className="value">{toolComp.original_tokens} tokens</span>
                      </div>
                      <div className="tool-stat">
                        <span className="label">Compressed:</span>
                        <span className="value">{toolComp.compressed_tokens} tokens</span>
                      </div>
                      <div className="tool-stat">
                        <span className="label">Rate:</span>
                        <span className="value rate">{(toolComp.compression_rate * 100).toFixed(1)}%</span>
                      </div>
                      <div className="tool-stat">
                        <span className="label">Saved:</span>
                        <span className="value savings">{toolComp.token_savings} tokens</span>
                      </div>
                    </div>

                    {toolComp.preserved_info && Object.keys(toolComp.preserved_info).length > 0 && (
                      <div className="preserved-info">
                        <h5>Preserved Information:</h5>
                        <div className="info-grid">
                          {Object.entries(toolComp.preserved_info).map(([key, value]) => (
                            <div key={key} className="info-item">
                              <span className="info-key">{key.replace(/_/g, ' ')}:</span>
                              <span className="info-value">
                                {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ExchangeComparison;
