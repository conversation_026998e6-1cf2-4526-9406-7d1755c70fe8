.compression-summary {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
}

.summary-header {
  margin-bottom: 30px;
}

.summary-header h1 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: 600;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 24px;
  height: 24px;
  opacity: 0.9;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 4px;
}

.summary-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background: #e9ecef;
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.section-content {
  padding: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.overview-item h3 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.overview-item p {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 500;
}

.recommendations {
  background: #e8f5e8;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.recommendations h3 {
  margin: 0 0 15px 0;
  color: #155724;
  font-size: 1.2rem;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  color: #155724;
  margin-bottom: 8px;
  line-height: 1.5;
}

.chart-container {
  margin-bottom: 30px;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.compressor-table {
  overflow-x: auto;
}

.compressor-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.compressor-table th,
.compressor-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.compressor-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.compressor-table tr:hover {
  background: #f8f9fa;
}

.tool-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.tool-stat-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #ffc658;
}

.tool-stat-card h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.tool-metrics {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tool-metrics span {
  color: #6c757d;
  font-size: 0.9rem;
}

.quality-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.quality-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #82ca9d;
}

.quality-card h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.quality-scores {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.score-item:last-child {
  border-bottom: none;
}

.score-item span:first-child {
  color: #6c757d;
  font-weight: 500;
}

.score-item span:last-child {
  color: #2c3e50;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .compression-summary {
    padding: 15px;
  }

  .summary-header h1 {
    font-size: 2rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .tool-stats {
    grid-template-columns: 1fr;
  }

  .quality-metrics {
    grid-template-columns: 1fr;
  }
}
