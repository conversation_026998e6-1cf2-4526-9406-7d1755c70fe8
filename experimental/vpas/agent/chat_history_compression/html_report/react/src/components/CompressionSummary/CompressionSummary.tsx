import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, <PERSON>, <PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';
import { ChevronDown, ChevronRight, TrendingUp, Zap, Target, Award } from 'lucide-react';
import { CompressionSummaryProps, ChartDataPoint, ComparisonChartData } from '../../types';
import './CompressionSummary.css';

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1'];

const CompressionSummary: React.FC<CompressionSummaryProps> = ({
  data,
  showQualityMetrics = true
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    overview: true,
    compressors: true,
    tools: false,
    quality: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Prepare data for charts
  const compressorData: ComparisonChartData[] = Object.entries(data.compressor_summaries).map(([name, stats]) => ({
    compressor: name,
    compression_rate: (stats.overall_compression_rate || 0) * 100,
    quality_score: data.quality_analysis[name]?.average_quality_score * 100 || 0,
    token_savings: stats.total_token_savings || 0,
    processing_time: stats.avg_compression_time_seconds || 0
  }));

  const toolData: ChartDataPoint[] = Object.entries(data.tool_summaries).map(([name, stats]) => ({
    name,
    value: stats.average_compression_rate * 100,
    label: `${stats.total_compressions} uses`
  }));

  const qualityDistribution: ChartDataPoint[] = showQualityMetrics && data.quality_analysis ?
    Object.entries(data.quality_analysis).flatMap(([compressor, analysis]) =>
      Object.entries(analysis.quality_distribution || {}).map(([quality, count]) => ({
        name: `${compressor} - ${quality}`,
        value: count as number
      }))
    ) : [];

  return (
    <div className="compression-summary">
      <div className="summary-header">
        <h1>Chat History Compression Analysis</h1>
        <div className="summary-stats">
          <div className="stat-card">
            <TrendingUp className="stat-icon" />
            <div>
              <div className="stat-value">{data.total_conversations}</div>
              <div className="stat-label">Conversations</div>
            </div>
          </div>
          <div className="stat-card">
            <Zap className="stat-icon" />
            <div>
              <div className="stat-value">{data.total_exchanges}</div>
              <div className="stat-label">Exchanges</div>
            </div>
          </div>
          <div className="stat-card">
            <Target className="stat-icon" />
            <div>
              <div className="stat-value">{Object.keys(data.compressor_summaries).length}</div>
              <div className="stat-label">Compressors</div>
            </div>
          </div>
          <div className="stat-card">
            <Award className="stat-icon" />
            <div>
              <div className="stat-value">{Object.keys(data.tool_summaries).length}</div>
              <div className="stat-label">Tools</div>
            </div>
          </div>
        </div>
      </div>

      {/* Overview Section */}
      <div className="summary-section">
        <div
          className="section-header"
          onClick={() => toggleSection('overview')}
        >
          {expandedSections.overview ? <ChevronDown /> : <ChevronRight />}
          <h2>Overview</h2>
        </div>
        {expandedSections.overview && (
          <div className="section-content">
            <div className="overview-grid">
              <div className="overview-item">
                <h3>Best Compressor (Rate)</h3>
                <p>{data.best_compressor_by_rate || 'N/A'}</p>
              </div>
              <div className="overview-item">
                <h3>Best Compressor (Quality)</h3>
                <p>{data.best_compressor_by_quality || 'N/A'}</p>
              </div>
              <div className="overview-item">
                <h3>Analysis ID</h3>
                <p>{data.analysis_id}</p>
              </div>
              <div className="overview-item">
                <h3>Timestamp</h3>
                <p>{new Date(data.timestamp).toLocaleString()}</p>
              </div>
            </div>

            <div className="recommendations">
              <h3>Recommendations</h3>
              <ul>
                {data.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Compressor Comparison Section */}
      <div className="summary-section">
        <div
          className="section-header"
          onClick={() => toggleSection('compressors')}
        >
          {expandedSections.compressors ? <ChevronDown /> : <ChevronRight />}
          <h2>Compressor Comparison</h2>
        </div>
        {expandedSections.compressors && (
          <div className="section-content">
            <div className="chart-container">
              <h3>Compression Rate by Compressor</h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={compressorData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="compressor" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [`${value}%`, name]} />
                  <Legend />
                  <Bar dataKey="compression_rate" fill="#8884d8" name="Compression Rate %" />
                  {showQualityMetrics && (
                    <Bar dataKey="quality_score" fill="#82ca9d" name="Quality Score %" />
                  )}
                </BarChart>
              </ResponsiveContainer>
            </div>

            <div className="compressor-table">
              <table>
                <thead>
                  <tr>
                    <th>Compressor</th>
                    <th>Compression Rate</th>
                    <th>Token Savings</th>
                    <th>Processing Time</th>
                    {showQualityMetrics && <th>Quality Score</th>}
                  </tr>
                </thead>
                <tbody>
                  {compressorData.map((item) => (
                    <tr key={item.compressor}>
                      <td>{item.compressor}</td>
                      <td>{item.compression_rate.toFixed(1)}%</td>
                      <td>{item.token_savings.toLocaleString()}</td>
                      <td>{item.processing_time.toFixed(2)}s</td>
                      {showQualityMetrics && <td>{item.quality_score?.toFixed(1) || 'N/A'}%</td>}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* Tool Performance Section */}
      <div className="summary-section">
        <div
          className="section-header"
          onClick={() => toggleSection('tools')}
        >
          {expandedSections.tools ? <ChevronDown /> : <ChevronRight />}
          <h2>Tool Performance</h2>
        </div>
        {expandedSections.tools && (
          <div className="section-content">
            <div className="chart-container">
              <h3>Compression Rate by Tool</h3>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={toolData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {toolData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}%`, 'Compression Rate']} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            <div className="tool-stats">
              {Object.entries(data.tool_summaries).map(([toolName, stats]) => (
                <div key={toolName} className="tool-stat-card">
                  <h4>{toolName}</h4>
                  <div className="tool-metrics">
                    <span>Rate: {(stats.average_compression_rate * 100).toFixed(1)}%</span>
                    <span>Uses: {stats.total_compressions}</span>
                    <span>Savings: {stats.total_token_savings.toLocaleString()}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Quality Analysis Section */}
      {showQualityMetrics && (
        <div className="summary-section">
          <div
            className="section-header"
            onClick={() => toggleSection('quality')}
          >
            {expandedSections.quality ? <ChevronDown /> : <ChevronRight />}
            <h2>Quality Analysis</h2>
          </div>
          {expandedSections.quality && (
            <div className="section-content">
              {qualityDistribution.length > 0 && (
                <div className="chart-container">
                  <h3>Quality Distribution</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={qualityDistribution}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}

              <div className="quality-metrics">
                {Object.entries(data.quality_analysis).map(([compressor, analysis]) => (
                  <div key={compressor} className="quality-card">
                    <h4>{compressor}</h4>
                    <div className="quality-scores">
                      <div className="score-item">
                        <span>Quality Score:</span>
                        <span>{(analysis.average_quality_score * 100).toFixed(1)}%</span>
                      </div>
                      <div className="score-item">
                        <span>Retention Score:</span>
                        <span>{(analysis.average_retention_score * 100).toFixed(1)}%</span>
                      </div>
                      <div className="score-item">
                        <span>Evaluated:</span>
                        <span>{analysis.total_evaluated}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CompressionSummary;
