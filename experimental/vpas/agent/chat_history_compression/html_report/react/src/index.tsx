// This file is kept for compatibility but main.tsx is the actual entry point
import { createRoot } from 'react-dom/client';
import CompressionSummary from './components/CompressionSummary/CompressionSummary';
import ExchangeComparison from './components/ExchangeComparison/ExchangeComparison';
import MetricsDisplay from './components/MetricsDisplay/MetricsDisplay';

// Export components for use in HTML reports
export {
  CompressionSummary,
  ExchangeComparison,
  MetricsDisplay
};

// Global functions for rendering components from Python
declare global {
  interface Window {
    CompressionReport: {
      renderSummary: (containerId: string, data: any, options?: any) => void;
      renderExchangeComparison: (containerId: string, data: any) => void;
      renderMetrics: (containerId: string, data: any, options?: any) => void;
    };
  }
}

// Render functions that can be called from Python-generated HTML
const renderSummary = (containerId: string, data: any, options: any = {}) => {
  const container = document.getElementById(containerId);
  if (container) {
    const root = createRoot(container);
    root.render(
      <CompressionSummary
        data={data}
        showQualityMetrics={options.showQualityMetrics !== false}
      />
    );
  }
};

const renderExchangeComparison = (containerId: string, data: any) => {
  const container = document.getElementById(containerId);
  if (container) {
    const root = createRoot(container);
    root.render(
      <ExchangeComparison
        originalExchange={data.originalExchange}
        compressedExchange={data.compressedExchange}
        compressionResult={data.compressionResult}
      />
    );
  }
};

const renderMetrics = (containerId: string, data: any, options: any = {}) => {
  const container = document.getElementById(containerId);
  if (container) {
    const root = createRoot(container);
    root.render(
      <MetricsDisplay
        benchmarkResult={data}
        showDetailedBreakdown={options.showDetailedBreakdown !== false}
      />
    );
  }
};

// Make functions available globally
window.CompressionReport = {
  renderSummary,
  renderExchangeComparison,
  renderMetrics
};

// Default export for module usage
export default {
  renderSummary,
  renderExchangeComparison,
  renderMetrics
};
