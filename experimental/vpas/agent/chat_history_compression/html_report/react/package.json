{"name": "chat-history-compression-report", "version": "1.0.0", "description": "React components for chat history compression analysis reports", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"diff": "^7.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "lucide-react": "^0.263.1", "react-json-view": "^1.21.3", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "json-viewer-js": "^1.0.8", "testing-library": "^0.0.2", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1"}, "keywords": ["react", "compression", "analysis", "reporting"], "author": "Augment Code", "license": "MIT"}