"""HTML report generator for chat history compression analysis.

This module generates interactive HTML reports using React components
to visualize compression results, metrics, and quality analysis.
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..eval_types import (
    CompressionAnalysisSummary,
    CompressionBenchmarkResult,
    ChatHistoryCompressionEvalResult,
    ExchangeCompressionResult,
)
from .react_report_generator import save_react_compression_html_report


class CompressionReportGenerator:
    """Generate interactive HTML reports for compression analysis."""

    def __init__(self, output_dir: str = "./reports"):
        """Initialize the report generator.

        Args:
            output_dir: Directory to save generated reports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def generate_summary_report(
        self,
        analysis_summary: CompressionAnalysisSummary,
        filename: Optional[str] = None,
        title: str = "Compression Analysis Summary",
    ) -> str:
        """Generate a summary report from analysis results.

        Args:
            analysis_summary: Analysis summary data
            filename: Optional custom filename
            title: Report title

        Returns:
            Path to the generated HTML file
        """
        filename = (
            filename or f"compression_summary_{analysis_summary.analysis_id}.html"
        )

        # Use the React report generator
        result_path = save_react_compression_html_report(
            output_dir=self.output_dir,
            analysis_summary=analysis_summary,
            report_type="summary",
            filename=filename,
        )

        if result_path is None:
            raise RuntimeError("Failed to generate React-based summary report")

        return result_path

    def generate_benchmark_report(
        self,
        benchmark_result: CompressionBenchmarkResult,
        filename: Optional[str] = None,
        title: str = "Compression Benchmark Results",
    ) -> str:
        """Generate a benchmark comparison report.

        Args:
            benchmark_result: Benchmark results data
            filename: Optional custom filename
            title: Report title

        Returns:
            Path to the generated HTML file
        """
        filename = (
            filename or f"compression_benchmark_{benchmark_result.benchmark_id}.html"
        )

        # Use the React report generator
        result_path = save_react_compression_html_report(
            output_dir=self.output_dir,
            benchmark_result=benchmark_result,
            report_type="benchmark",
            filename=filename,
        )

        if result_path is None:
            raise RuntimeError("Failed to generate React-based benchmark report")

        return result_path

    def generate_exchange_comparison_report(
        self,
        original_exchange: Any,
        compressed_exchange: Any,
        compression_result: ExchangeCompressionResult,
        filename: Optional[str] = None,
        title: str = "Exchange Comparison",
    ) -> str:
        """Generate a detailed exchange comparison report.

        Args:
            original_exchange: Original exchange data
            compressed_exchange: Compressed exchange data
            compression_result: Compression analysis result
            filename: Optional custom filename
            title: Report title

        Returns:
            Path to the generated HTML file
        """
        filename = (
            filename or f"exchange_comparison_{compression_result.request_id}.html"
        )

        # Use the React report generator
        result_path = save_react_compression_html_report(
            output_dir=self.output_dir,
            exchange_data=(original_exchange, compressed_exchange, compression_result),
            report_type="exchange",
            filename=filename,
        )

        if result_path is None:
            raise RuntimeError(
                "Failed to generate React-based exchange comparison report"
            )

        return result_path

    def generate_comprehensive_report(
        self,
        analysis_summary: CompressionAnalysisSummary,
        benchmark_result: Optional[CompressionBenchmarkResult] = None,
        sample_exchanges: Optional[List[tuple]] = None,
        filename: Optional[str] = None,
        title: str = "Comprehensive Compression Analysis",
    ) -> str:
        """Generate a comprehensive report with multiple sections.

        Args:
            analysis_summary: Analysis summary data
            benchmark_result: Optional benchmark results
            sample_exchanges: Optional list of (original, compressed, result) tuples
            filename: Optional custom filename
            title: Report title

        Returns:
            Path to the generated HTML file
        """
        filename = (
            filename or f"comprehensive_report_{analysis_summary.analysis_id}.html"
        )

        # Use the React report generator
        result_path = save_react_compression_html_report(
            output_dir=self.output_dir,
            analysis_summary=analysis_summary,
            benchmark_result=benchmark_result,
            sample_exchanges=sample_exchanges,
            report_type="comprehensive",
            filename=filename,
        )

        if result_path is None:
            raise RuntimeError("Failed to generate React-based comprehensive report")

        return result_path
