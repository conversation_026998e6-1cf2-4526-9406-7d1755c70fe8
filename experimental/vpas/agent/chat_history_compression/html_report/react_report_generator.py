"""React report generator for HTML reports.

This module provides functions for generating JSON data for the React app,
building the React app, and copying it to the output directory.
"""

import json
import os
import shutil
import subprocess
from dataclasses import asdict, is_dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..eval_types import (
    CompressionAnalysisSummary,
    CompressionBenchmarkResult,
    ChatHistoryCompressionEvalResult,
    ExchangeCompressionResult,
)

# Constants
CURRENT_DIR = Path(__file__).parent
REACT_DIR = CURRENT_DIR / "react"


# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, o):
        # Handle dataclasses
        if is_dataclass(o) and not isinstance(o, type):
            return asdict(o)
        # Handle any other non-serializable objects
        try:
            return super().default(o)
        except TypeError:
            return str(o)


def generate_compression_json_str(
    analysis_summary: Optional[CompressionAnalysisSummary] = None,
    benchmark_result: Optional[CompressionBenchmarkResult] = None,
    sample_exchanges: Optional[List[tuple]] = None,
    exchange_data: Optional[tuple] = None,
    report_type: str = "summary",
) -> str:
    """Generate JSON string for compression analysis data.

    Args:
        analysis_summary: Analysis summary data
        benchmark_result: Benchmark results data
        sample_exchanges: List of (original, compressed, result) tuples
        exchange_data: Single (original, compressed, result) tuple
        report_type: Type of report ('summary', 'benchmark', 'exchange', 'comprehensive')

    Returns:
        JSON string containing the data
    """

    data: Dict[str, Any] = {"report_type": report_type}

    if report_type == "comprehensive":
        # Comprehensive report with multiple sections
        comprehensive_data: Dict[str, Any] = {}

        if analysis_summary:
            comprehensive_data["analysis_summary"] = _serialize_analysis_summary(
                analysis_summary
            )

        if benchmark_result:
            comprehensive_data["benchmark_result"] = _serialize_benchmark_result(
                benchmark_result
            )

        if sample_exchanges:
            comprehensive_data["sample_exchanges"] = []
            for orig, comp, result in sample_exchanges[:5]:  # Limit to 5 samples
                comprehensive_data["sample_exchanges"].append(
                    {
                        "originalExchange": _serialize_exchange(orig),
                        "compressedExchange": _serialize_exchange(comp),
                        "compressionResult": _serialize_exchange_result(result),
                    }
                )

        data["comparison_summary"] = comprehensive_data

    elif report_type == "summary" and analysis_summary:
        data["analysis_summary"] = _serialize_analysis_summary(analysis_summary)

    elif report_type == "benchmark" and benchmark_result:
        data["benchmark_result"] = _serialize_benchmark_result(benchmark_result)

    elif report_type == "exchange" and exchange_data:
        orig, comp, result = exchange_data
        data["exchange_data"] = {
            "originalExchange": _serialize_exchange(orig),
            "compressedExchange": _serialize_exchange(comp),
            "compressionResult": _serialize_exchange_result(result),
        }

    # Use our custom JSON encoder to handle non-serializable objects
    try:
        return json.dumps(data, cls=CustomJSONEncoder, indent=2)
    except TypeError as e:
        # If the default serialization fails, use str() fallbacks for all objects
        print(f"JSON serialization failed: {e}")
        try:
            return json.dumps(data, default=lambda o: str(o), indent=2)
        except Exception:
            raise e


def build_react_app(data_path: Path) -> bool:
    """Build the React app with the provided data.

    Args:
        data_path: Path to the JSON data file

    Returns:
        True if the build was successful, False otherwise
    """
    try:
        print(f"Building React app with data from {data_path}")
        # Change to the React directory
        original_cwd = os.getcwd()
        os.chdir(REACT_DIR)

        # Install dependencies
        subprocess.run(["npm", "install", "--legacy-peer-deps"], check=True)

        # Build the React app
        subprocess.run(["npm", "run", "build"], check=True)

        # Copy the data file to the dist directory
        dist_dir = REACT_DIR / "dist"
        if dist_dir.exists():
            shutil.copy(data_path, dist_dir / "data.json")
        else:
            print("Warning: dist directory not found after build")

        return True
    except subprocess.CalledProcessError as e:
        print(f"Error building React app: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error building React app: {e}")
        return False
    finally:
        # Change back to the original directory
        os.chdir(original_cwd)


def save_react_compression_html_report(
    output_dir: Path,
    analysis_summary: Optional[CompressionAnalysisSummary] = None,
    benchmark_result: Optional[CompressionBenchmarkResult] = None,
    sample_exchanges: Optional[List[tuple]] = None,
    exchange_data: Optional[tuple] = None,
    report_type: str = "summary",
    filename: str = "index.html",
) -> Optional[str]:
    """Generate a React compression HTML report and save it to the output directory.

    Args:
        output_dir: The directory where the report should be saved
        analysis_summary: Analysis summary data
        benchmark_result: Benchmark results data
        sample_exchanges: List of (original, compressed, result) tuples
        exchange_data: Single (original, compressed, result) tuple
        report_type: Type of report to generate
        filename: Name of the HTML file

    Returns:
        The path to the HTML report file, or None if the build failed
    """
    print(f"Generating React compression report in {output_dir}")
    # Create the output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Generate the JSON data
    json_str = generate_compression_json_str(
        analysis_summary=analysis_summary,
        benchmark_result=benchmark_result,
        sample_exchanges=sample_exchanges,
        exchange_data=exchange_data,
        report_type=report_type,
    )

    # Save the JSON data to a temporary file
    temp_data_path = CURRENT_DIR / "temp_data.json"
    with open(temp_data_path, "w") as f:
        f.write(json_str)

    # Build the React app
    build_success = build_react_app(temp_data_path)

    # Clean up the temporary file
    if temp_data_path.exists():
        temp_data_path.unlink()

    if not build_success:
        print("Failed to build React app")
        return None

    # Copy the built React app to the output directory
    react_build_dir = REACT_DIR / "dist"

    if not react_build_dir.exists():
        print("React build directory not found")
        return None

    # Copy all files from the build directory to the output directory
    for item in react_build_dir.glob("*"):
        if item.is_file():
            shutil.copy(item, output_dir / item.name)
        elif item.is_dir():
            shutil.copytree(item, output_dir / item.name, dirs_exist_ok=True)

    # Return the path to the index.html file
    index_path = output_dir / filename
    if index_path.exists():
        return str(index_path)
    else:
        print(f"{filename} not found in the build output")
        return None


def _serialize_analysis_summary(summary: CompressionAnalysisSummary) -> Dict[str, Any]:
    """Convert analysis summary to JSON-serializable format."""
    return {
        "analysis_id": summary.analysis_id,
        "timestamp": summary.timestamp,
        "total_conversations": summary.total_conversations,
        "total_exchanges": summary.total_exchanges,
        "compressor_summaries": summary.compressor_summaries,
        "tool_summaries": {
            name: {
                "tool_name": stats.tool_name,
                "total_compressions": stats.total_compressions,
                "total_original_tokens": stats.total_original_tokens,
                "total_compressed_tokens": stats.total_compressed_tokens,
                "average_compression_rate": stats.average_compression_rate,
                "method_distribution": stats.method_distribution,
                "quality_scores": stats.quality_scores,
                "total_token_savings": stats.total_token_savings,
                "average_quality_score": stats.average_quality_score,
            }
            for name, stats in summary.tool_summaries.items()
        },
        "quality_analysis": summary.quality_analysis,
        "recommendations": summary.recommendations,
        "best_compressor_by_rate": summary.best_compressor_by_rate,
        "best_compressor_by_quality": summary.best_compressor_by_quality,
    }


def _serialize_benchmark_result(
    benchmark: CompressionBenchmarkResult,
) -> Dict[str, Any]:
    """Convert benchmark result to JSON-serializable format."""
    return {
        "benchmark_id": benchmark.benchmark_id,
        "timestamp": benchmark.timestamp,
        "dataset_info": benchmark.dataset_info,
        "compressor_results": {
            name: [_serialize_eval_result(result) for result in results]
            for name, results in benchmark.compressor_results.items()
        },
        "summary_stats": benchmark.summary_stats,
        "benchmark_time_seconds": benchmark.benchmark_time_seconds,
        "compressor_names": benchmark.compressor_names,
        "total_conversations": benchmark.total_conversations,
    }


def _serialize_eval_result(result: ChatHistoryCompressionEvalResult) -> Dict[str, Any]:
    """Convert evaluation result to JSON-serializable format."""
    return {
        "conversation_id": result.conversation_id,
        "timestamp": result.timestamp,
        "compressor_name": result.compressor_name,
        "exchange_results": [
            _serialize_exchange_result(er) for er in result.exchange_results
        ],
        "total_original_tokens": result.total_original_tokens,
        "total_compressed_tokens": result.total_compressed_tokens,
        "compression_rate": result.compression_rate,
        "tool_compression_stats": result.tool_compression_stats,
        "llm_judge_score": result.llm_judge_score,
        "llm_judge_feedback": result.llm_judge_feedback,
        "information_retention_score": result.information_retention_score,
        "compression_time_seconds": result.compression_time_seconds,
        "evaluation_time_seconds": result.evaluation_time_seconds,
        "token_savings": result.token_savings,
        "exchanges_count": result.exchanges_count,
        "tools_compressed_count": result.tools_compressed_count,
    }


def _serialize_exchange_result(result: ExchangeCompressionResult) -> Dict[str, Any]:
    """Convert exchange result to JSON-serializable format."""
    return {
        "request_id": result.request_id,
        "tool_compressions": [
            {
                "tool_name": tc.tool_name,
                "tool_use_id": tc.tool_use_id,
                "original_tokens": tc.original_tokens,
                "compressed_tokens": tc.compressed_tokens,
                "compression_method": tc.compression_method,
                "preserved_info": tc.preserved_info,
                "compression_rate": tc.compression_rate,
                "token_savings": tc.token_savings,
            }
            for tc in result.tool_compressions
        ],
        "original_tokens": result.original_tokens,
        "compressed_tokens": result.compressed_tokens,
        "compression_rate": result.compression_rate,
        "token_savings": result.token_savings,
    }


def _serialize_exchange(exchange: Any) -> Dict[str, Any]:
    """Convert exchange object to JSON-serializable format."""
    if not exchange:
        return {}

    # This is a simplified serialization - in practice, you'd want to
    # handle the full Exchange object structure
    return {
        "request_id": getattr(exchange, "request_id", "unknown"),
        "request_message": str(getattr(exchange, "request_message", "")),
        "response_text": str(getattr(exchange, "response_text", "")),
    }
