"""Complete evaluation pipeline for chat history compression.

This module provides a comprehensive evaluation pipeline that combines
compression metrics calculation with LLM-based quality assessment to
provide detailed analysis of compression effectiveness.
"""

import time
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

from base.prompt_format.common import Exchange
from .chat_history_compression import Chat<PERSON><PERSON>oryCompressor
from .metrics import CompressionMetrics
from .llm_judge import CompressionJudge
from .eval_types import (
    ChatHistoryCompressionEvalResult,
    CompressionBenchmarkResult,
    CompressionAnalysisSummary,
    ToolCompressionStats,
)
from .utils import register_tool_uses


class CompressionEvaluationPipeline:
    """Complete pipeline for evaluating compression strategies."""

    def __init__(
        self,
        metrics_calculator: Optional[CompressionMetrics] = None,
        quality_judge: Optional[CompressionJudge] = None,
        enable_quality_evaluation: bool = True,
        max_workers: int = 5,
    ):
        """Initialize the evaluation pipeline.

        Args:
            metrics_calculator: Optional metrics calculator. Creates default if None.
            quality_judge: Optional LLM judge. Creates default if None.
            enable_quality_evaluation: Whether to run LLM-based quality evaluation.
            max_workers: Maximum number of worker threads for parallel evaluation.
        """
        self.metrics = metrics_calculator or CompressionMetrics()
        self.judge = (
            quality_judge or CompressionJudge() if enable_quality_evaluation else None
        )
        self.enable_quality_evaluation = enable_quality_evaluation
        self.max_workers = max_workers

    def evaluate_single_conversation(
        self,
        original_exchanges: List[Exchange],
        compressed_exchanges: List[Exchange],
        compressor_name: str,
        conversation_id: str,
        include_quality_eval: bool = None,
    ) -> ChatHistoryCompressionEvalResult:
        """Evaluate compression for a single conversation.

        Args:
            original_exchanges: Original conversation exchanges
            compressed_exchanges: Compressed conversation exchanges
            compressor_name: Name of the compression strategy
            conversation_id: Unique identifier for the conversation
            include_quality_eval: Override for quality evaluation. Uses instance default if None.

        Returns:
            Complete evaluation result
        """
        start_time = time.time()

        # Calculate compression metrics
        eval_result = self.metrics.evaluate_compression(
            original_exchanges, compressed_exchanges, compressor_name, conversation_id
        )

        # Add quality evaluation if enabled
        if (
            include_quality_eval
            if include_quality_eval is not None
            else self.enable_quality_evaluation
        ) and self.judge:
            try:
                quality_metrics = self.judge.evaluate_compression(
                    original_exchanges, compressed_exchanges
                )

                eval_result.llm_judge_score = quality_metrics.overall_quality_score
                eval_result.llm_judge_feedback = quality_metrics.feedback
                eval_result.information_retention_score = (
                    quality_metrics.information_retention_score
                )

            except Exception as e:
                print(f"Quality evaluation failed for {conversation_id}: {e}")
                eval_result.llm_judge_feedback = f"Quality evaluation failed: {str(e)}"

        eval_result.evaluation_time_seconds = time.time() - start_time
        return eval_result

    def evaluate_multiple_conversations(
        self,
        conversation_data: List[Tuple[List[Exchange], List[Exchange], str]],
        compressor_name: str,
        include_quality_eval: bool = None,
    ) -> List[ChatHistoryCompressionEvalResult]:
        """Evaluate compression for multiple conversations in parallel.

        Args:
            conversation_data: List of (original, compressed, conversation_id) tuples
            compressor_name: Name of the compression strategy
            include_quality_eval: Override for quality evaluation

        Returns:
            List of evaluation results
        """
        results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all evaluation tasks
            futures = []
            for original, compressed, conv_id in conversation_data:
                future = executor.submit(
                    self.evaluate_single_conversation,
                    original,
                    compressed,
                    compressor_name,
                    conv_id,
                    include_quality_eval,
                )
                futures.append((future, conv_id))

            # Collect results with progress bar
            for future, conv_id in tqdm(futures, desc=f"Evaluating {compressor_name}"):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"Failed to evaluate conversation {conv_id}: {e}")

        return results

    def benchmark_compressors(
        self,
        compressors: List[ChatHistoryCompressor],
        test_conversations: List[List[Exchange]],
        conversation_ids: List[str],
        benchmark_id: str = None,
    ) -> CompressionBenchmarkResult:
        """Benchmark multiple compression strategies on the same dataset.

        Args:
            compressors: List of compression strategies to benchmark
            test_conversations: List of conversation exchanges to test on
            conversation_ids: List of conversation identifiers
            benchmark_id: Optional identifier for the benchmark run

        Returns:
            Complete benchmark results
        """
        start_time = time.time()
        benchmark_id = benchmark_id or f"benchmark_{int(time.time())}"

        compressor_results = {}
        summary_stats = {}

        for compressor in compressors:
            print(f"\nBenchmarking {compressor.get_name()}...")

            # Compress all conversations
            conversation_data = []
            for original, conv_id in zip(test_conversations, conversation_ids):
                # Register tool uses for compression
                register_tool_uses(original)

                # Compress the conversation
                compressed = compressor.compress(original)
                conversation_data.append((original, compressed, conv_id))

            # Evaluate all compressions
            eval_results = self.evaluate_multiple_conversations(
                conversation_data, compressor.get_name()
            )

            compressor_results[compressor.get_name()] = eval_results

            # Calculate summary statistics
            summary_stats[compressor.get_name()] = self.metrics.calculate_summary_stats(
                eval_results
            )

        # Create benchmark result
        benchmark_result = CompressionBenchmarkResult(
            benchmark_id=benchmark_id,
            timestamp=time.time(),
            dataset_info={
                "total_conversations": len(test_conversations),
                "total_exchanges": sum(len(conv) for conv in test_conversations),
                "conversation_ids": conversation_ids,
            },
            compressor_results=compressor_results,
            summary_stats=summary_stats,
            benchmark_time_seconds=time.time() - start_time,
        )

        return benchmark_result

    def analyze_compression_results(
        self,
        eval_results: List[ChatHistoryCompressionEvalResult],
        analysis_id: str = None,
    ) -> CompressionAnalysisSummary:
        """Perform detailed analysis of compression results.

        Args:
            eval_results: List of evaluation results to analyze
            analysis_id: Optional identifier for the analysis

        Returns:
            Comprehensive analysis summary
        """
        analysis_id = analysis_id or f"analysis_{int(time.time())}"

        # Group results by compressor
        compressor_groups = {}
        for result in eval_results:
            compressor = result.compressor_name
            if compressor not in compressor_groups:
                compressor_groups[compressor] = []
            compressor_groups[compressor].append(result)

        # Calculate compressor summaries
        compressor_summaries = {}
        for compressor, results in compressor_groups.items():
            compressor_summaries[compressor] = self.metrics.calculate_summary_stats(
                results
            )

        # Analyze tool-specific performance
        tool_summaries = self._analyze_tool_performance(eval_results)

        # Analyze quality metrics if available
        quality_analysis = self._analyze_quality_metrics(eval_results)

        # Generate recommendations
        recommendations = self._generate_recommendations(
            compressor_summaries, quality_analysis
        )

        return CompressionAnalysisSummary(
            analysis_id=analysis_id,
            timestamp=time.time(),
            total_conversations=len(set(r.conversation_id for r in eval_results)),
            total_exchanges=sum(r.exchanges_count for r in eval_results),
            compressor_summaries=compressor_summaries,
            tool_summaries=tool_summaries,
            quality_analysis=quality_analysis,
            recommendations=recommendations,
        )

    def _analyze_tool_performance(
        self, eval_results: List[ChatHistoryCompressionEvalResult]
    ) -> Dict[str, ToolCompressionStats]:
        """Analyze performance by tool type.

        Args:
            eval_results: List of evaluation results

        Returns:
            Dictionary mapping tool names to their statistics
        """
        tool_data = {}

        for result in eval_results:
            for tool_name, stats in result.tool_compression_stats.items():
                if tool_name not in tool_data:
                    tool_data[tool_name] = {
                        "total_compressions": 0,
                        "total_original_tokens": 0,
                        "total_compressed_tokens": 0,
                        "method_counts": {},
                        "quality_scores": [],
                    }

                data = tool_data[tool_name]
                data["total_compressions"] += stats["count"]
                data["total_original_tokens"] += stats["total_original_tokens"]
                data["total_compressed_tokens"] += stats["total_compressed_tokens"]

                # Track method distribution
                for method in ["placeholder_method_count", "llm_method_count"]:
                    if method in stats:
                        method_name = method.replace("_method_count", "")
                        data["method_counts"][method_name] = (
                            data["method_counts"].get(method_name, 0) + stats[method]
                        )

        # Convert to ToolCompressionStats objects
        tool_summaries = {}
        for tool_name, data in tool_data.items():
            compression_rate = (
                (data["total_original_tokens"] - data["total_compressed_tokens"])
                / data["total_original_tokens"]
                if data["total_original_tokens"] > 0
                else 0
            )

            tool_summaries[tool_name] = ToolCompressionStats(
                tool_name=tool_name,
                total_compressions=data["total_compressions"],
                total_original_tokens=data["total_original_tokens"],
                total_compressed_tokens=data["total_compressed_tokens"],
                average_compression_rate=compression_rate,
                method_distribution=data["method_counts"],
                quality_scores=data["quality_scores"],
            )

        return tool_summaries

    def _analyze_quality_metrics(
        self, eval_results: List[ChatHistoryCompressionEvalResult]
    ) -> Dict[str, dict]:
        """Analyze quality metrics by compressor.

        Args:
            eval_results: List of evaluation results

        Returns:
            Quality analysis by compressor
        """
        if not self.judge:
            return {}

        quality_analysis = {}

        # Group by compressor
        compressor_groups = {}
        for result in eval_results:
            compressor = result.compressor_name
            if compressor not in compressor_groups:
                compressor_groups[compressor] = []
            compressor_groups[compressor].append(result)

        # Analyze each compressor's quality
        for compressor, results in compressor_groups.items():
            quality_scores = [
                r.llm_judge_score for r in results if r.llm_judge_score is not None
            ]
            retention_scores = [
                r.information_retention_score
                for r in results
                if r.information_retention_score is not None
            ]

            if quality_scores:
                quality_analysis[compressor] = {
                    "average_quality_score": sum(quality_scores) / len(quality_scores),
                    "average_retention_score": sum(retention_scores)
                    / len(retention_scores)
                    if retention_scores
                    else 0,
                    "quality_distribution": {
                        "excellent": sum(1 for s in quality_scores if s >= 0.8),
                        "good": sum(1 for s in quality_scores if 0.6 <= s < 0.8),
                        "fair": sum(1 for s in quality_scores if 0.4 <= s < 0.6),
                        "poor": sum(1 for s in quality_scores if s < 0.4),
                    },
                    "total_evaluated": len(quality_scores),
                }

        return quality_analysis

    def _generate_recommendations(
        self, compressor_summaries: Dict[str, dict], quality_analysis: Dict[str, dict]
    ) -> List[str]:
        """Generate recommendations based on analysis results.

        Args:
            compressor_summaries: Summary statistics by compressor
            quality_analysis: Quality analysis by compressor

        Returns:
            List of recommendation strings
        """
        recommendations = []

        if not compressor_summaries:
            return ["No compression data available for analysis."]

        # Find best compressor by compression rate
        best_rate_compressor = max(
            compressor_summaries.items(),
            key=lambda x: x[1].get("overall_compression_rate", 0),
        )

        recommendations.append(
            f"Best compression rate: {best_rate_compressor[0]} "
            f"({best_rate_compressor[1].get('overall_compression_rate', 0):.1%})"
        )

        # Find best compressor by quality if available
        if quality_analysis:
            best_quality_compressor = max(
                quality_analysis.items(),
                key=lambda x: x[1].get("average_quality_score", 0),
            )

            recommendations.append(
                f"Best quality score: {best_quality_compressor[0]} "
                f"({best_quality_compressor[1].get('average_quality_score', 0):.2f})"
            )

        # Tool-specific recommendations
        for compressor, stats in compressor_summaries.items():
            tool_stats = stats.get("tool_statistics", {})
            if tool_stats:
                best_tool = max(
                    tool_stats.items(), key=lambda x: x[1].get("compression_rate", 0)
                )
                recommendations.append(
                    f"{compressor} works best on {best_tool[0]} "
                    f"({best_tool[1].get('compression_rate', 0):.1%} compression)"
                )

        return recommendations
