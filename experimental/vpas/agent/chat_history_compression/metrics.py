"""Compression metrics calculation module.

This module provides tools for evaluating compression quality and calculating
detailed metrics about token usage, compression rates, and tool-specific statistics.
"""

import time
from typing import List, Dict, Tuple
from datetime import datetime
from base.prompt_format_chat.lib.token_counter_claude import Claude<PERSON><PERSON>Counter
from base.prompt_format.common import Exchange, ChatRequestNodeType, ChatResultNodeType
from .chat_history_compression import CompressionStats
from .utils import is_tool_output_node, is_tool_use_node, find_tool_name_for_result
from .eval_types import (
    ToolCompressionResult,
    ExchangeCompressionResult,
    ChatHistoryCompressionEvalResult,
)


class CompressionMetrics:
    """Calculator for compression metrics and evaluation results."""

    def __init__(self):
        self.token_counter = ClaudeTokenCounter()

    def evaluate_compression(
        self,
        original: List[Exchange],
        compressed: List[Exchange],
        compressor_name: str,
        conversation_id: str,
    ) -> ChatHistoryCompressionEvalResult:
        """Evaluate compression results and generate comprehensive metrics.

        Args:
            original: Original list of exchanges
            compressed: Compressed list of exchanges
            compressor_name: Name of the compression strategy used
            conversation_id: Unique identifier for the conversation

        Returns:
            Complete evaluation result with all metrics
        """
        start_time = time.time()

        # Build exchange-level results
        exchange_results = []
        for orig, comp in zip(original, compressed):
            exchange_result = self._evaluate_exchange(orig, comp)
            exchange_results.append(exchange_result)

        # Calculate overall metrics
        total_original = sum(r.original_tokens for r in exchange_results)
        total_compressed = sum(r.compressed_tokens for r in exchange_results)
        compression_rate = (
            (total_original - total_compressed) / total_original
            if total_original > 0
            else 0
        )

        # Calculate tool-specific stats
        tool_stats = self._calculate_tool_stats(exchange_results)

        eval_result = ChatHistoryCompressionEvalResult(
            conversation_id=conversation_id,
            timestamp=datetime.now(),
            compressor_name=compressor_name,
            exchange_results=exchange_results,
            total_original_tokens=total_original,
            total_compressed_tokens=total_compressed,
            compression_rate=compression_rate,
            tool_compression_stats=tool_stats,
            compression_time_seconds=time.time() - start_time,
        )

        return eval_result

    def _evaluate_exchange(
        self, original: Exchange, compressed: Exchange
    ) -> ExchangeCompressionResult:
        """Evaluate compression for a single exchange.

        Args:
            original: Original exchange
            compressed: Compressed exchange

        Returns:
            Exchange-level compression result
        """
        # Count tokens in original and compressed exchanges
        original_tokens = self._count_exchange_tokens(original)
        compressed_tokens = self._count_exchange_tokens(compressed)

        # Analyze tool compressions
        tool_compressions = self._analyze_tool_compressions(original, compressed)

        return ExchangeCompressionResult(
            request_id=original.request_id or "unknown",
            original_exchange=original,
            compressed_exchange=compressed,
            tool_compressions=tool_compressions,
            original_tokens=original_tokens,
            compressed_tokens=compressed_tokens,
        )

    def _count_exchange_tokens(self, exchange: Exchange) -> int:
        """Count total tokens in an exchange.

        Args:
            exchange: Exchange to count tokens for

        Returns:
            Total token count
        """
        total = 0

        # Count request nodes
        if isinstance(exchange.request_message, list):
            for node in exchange.request_message:
                if node.type == ChatRequestNodeType.TEXT and node.text_node:
                    total += self.token_counter.count_tokens(node.text_node.content)
                elif (
                    node.type == ChatRequestNodeType.TOOL_RESULT
                    and node.tool_result_node
                ):
                    total += self.token_counter.count_tokens(
                        node.tool_result_node.content
                    )
        elif isinstance(exchange.request_message, str):
            total += self.token_counter.count_tokens(exchange.request_message)

        # Count response nodes
        if isinstance(exchange.response_text, list):
            for node in exchange.response_text:
                if node.type == ChatResultNodeType.RAW_RESPONSE:
                    total += self.token_counter.count_tokens(node.content)
                elif node.type == ChatResultNodeType.TOOL_USE and node.tool_use:
                    # Count tool name and input
                    tool_text = f"{node.tool_use.name} {node.tool_use.input}"
                    total += self.token_counter.count_tokens(tool_text)
        elif isinstance(exchange.response_text, str):
            total += self.token_counter.count_tokens(exchange.response_text)

        return total

    def _analyze_tool_compressions(
        self, original: Exchange, compressed: Exchange
    ) -> List[ToolCompressionResult]:
        """Analyze tool-specific compression results.

        Args:
            original: Original exchange
            compressed: Compressed exchange

        Returns:
            List of tool compression results
        """
        tool_compressions = []

        # Compare tool results between original and compressed
        if isinstance(original.request_message, list) and isinstance(
            compressed.request_message, list
        ):
            orig_tool_nodes = [
                node
                for node in original.request_message
                if is_tool_output_node(node) and node.tool_result_node
            ]
            comp_tool_nodes = [
                node
                for node in compressed.request_message
                if is_tool_output_node(node) and node.tool_result_node
            ]

            # Match nodes by tool_use_id
            for orig_node in orig_tool_nodes:
                tool_use_id = orig_node.tool_result_node.tool_use_id
                tool_name = find_tool_name_for_result(tool_use_id)

                # Find corresponding compressed node
                comp_node = None
                for cn in comp_tool_nodes:
                    if cn.tool_result_node.tool_use_id == tool_use_id:
                        comp_node = cn
                        break

                if comp_node and tool_name:
                    # Calculate compression metrics for this tool
                    orig_content = orig_node.tool_result_node.content
                    comp_content = comp_node.tool_result_node.content

                    orig_tokens = self.token_counter.count_tokens(orig_content)
                    comp_tokens = self.token_counter.count_tokens(comp_content)

                    # Determine compression method
                    compression_method = (
                        "placeholder"
                        if "[Tool output compressed:" in comp_content
                        else "llm"
                    )

                    # Extract preserved information
                    preserved_info = self._extract_preserved_info(
                        orig_content, comp_content
                    )

                    tool_compressions.append(
                        ToolCompressionResult(
                            tool_name=tool_name,
                            tool_use_id=tool_use_id,
                            original_tokens=orig_tokens,
                            compressed_tokens=comp_tokens,
                            compression_method=compression_method,
                            preserved_info=preserved_info,
                        )
                    )

        return tool_compressions

    def _extract_preserved_info(self, original: str, compressed: str) -> Dict[str, any]:
        """Extract information about what was preserved in compression.

        Args:
            original: Original content
            compressed: Compressed content

        Returns:
            Dictionary with preserved information analysis
        """
        preserved_info = {
            "original_length": len(original),
            "compressed_length": len(compressed),
            "compression_ratio": len(compressed) / len(original) if original else 0,
            "has_error_info": "error" in compressed.lower() or "ERROR" in compressed,
            "has_file_info": any(
                keyword in compressed
                for keyword in ["file", "path", ".py", ".js", ".ts"]
            ),
            "has_line_info": "line" in compressed.lower()
            or "lines" in compressed.lower(),
            "preserved_structure": "\n" in compressed or "```" in compressed,
        }

        return preserved_info

    def _calculate_tool_stats(
        self, exchange_results: List[ExchangeCompressionResult]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate tool-specific compression statistics.

        Args:
            exchange_results: List of exchange compression results

        Returns:
            Dictionary mapping tool names to their statistics
        """
        tool_stats = {}

        # Collect all tool compressions
        all_tool_compressions = []
        for exchange_result in exchange_results:
            all_tool_compressions.extend(exchange_result.tool_compressions)

        # Group by tool name
        tool_groups = {}
        for tool_comp in all_tool_compressions:
            tool_name = tool_comp.tool_name
            if tool_name not in tool_groups:
                tool_groups[tool_name] = []
            tool_groups[tool_name].append(tool_comp)

        # Calculate stats for each tool
        for tool_name, compressions in tool_groups.items():
            total_original = sum(tc.original_tokens for tc in compressions)
            total_compressed = sum(tc.compressed_tokens for tc in compressions)

            compression_rate = (
                (total_original - total_compressed) / total_original
                if total_original > 0
                else 0
            )

            avg_original = total_original / len(compressions) if compressions else 0
            avg_compressed = total_compressed / len(compressions) if compressions else 0

            # Calculate method distribution
            method_counts = {}
            for tc in compressions:
                method = tc.compression_method
                method_counts[method] = method_counts.get(method, 0) + 1

            tool_stats[tool_name] = {
                "count": len(compressions),
                "total_original_tokens": total_original,
                "total_compressed_tokens": total_compressed,
                "compression_rate": compression_rate,
                "avg_original_tokens": avg_original,
                "avg_compressed_tokens": avg_compressed,
                "token_savings": total_original - total_compressed,
                "placeholder_method_count": method_counts.get("placeholder", 0),
                "llm_method_count": method_counts.get("llm", 0),
            }

        return tool_stats

    def calculate_summary_stats(
        self, eval_results: List[ChatHistoryCompressionEvalResult]
    ) -> Dict[str, any]:
        """Calculate summary statistics across multiple evaluations.

        Args:
            eval_results: List of evaluation results

        Returns:
            Dictionary with summary statistics
        """
        if not eval_results:
            return {}

        total_conversations = len(eval_results)
        total_original_tokens = sum(r.total_original_tokens for r in eval_results)
        total_compressed_tokens = sum(r.total_compressed_tokens for r in eval_results)

        overall_compression_rate = (
            (total_original_tokens - total_compressed_tokens) / total_original_tokens
            if total_original_tokens > 0
            else 0
        )

        # Calculate average compression time
        avg_compression_time = (
            sum(r.compression_time_seconds for r in eval_results) / total_conversations
        )

        # Aggregate tool statistics
        aggregated_tool_stats = {}
        for eval_result in eval_results:
            for tool_name, stats in eval_result.tool_compression_stats.items():
                if tool_name not in aggregated_tool_stats:
                    aggregated_tool_stats[tool_name] = {
                        "count": 0,
                        "total_original_tokens": 0,
                        "total_compressed_tokens": 0,
                        "total_savings": 0,
                    }

                agg_stats = aggregated_tool_stats[tool_name]
                agg_stats["count"] += stats["count"]
                agg_stats["total_original_tokens"] += stats["total_original_tokens"]
                agg_stats["total_compressed_tokens"] += stats["total_compressed_tokens"]
                agg_stats["total_savings"] += stats["token_savings"]

        # Calculate final rates for aggregated tools
        for tool_name, stats in aggregated_tool_stats.items():
            if stats["total_original_tokens"] > 0:
                stats["compression_rate"] = (
                    stats["total_savings"] / stats["total_original_tokens"]
                )
            else:
                stats["compression_rate"] = 0

        return {
            "total_conversations": total_conversations,
            "total_original_tokens": total_original_tokens,
            "total_compressed_tokens": total_compressed_tokens,
            "overall_compression_rate": overall_compression_rate,
            "total_token_savings": total_original_tokens - total_compressed_tokens,
            "avg_compression_time_seconds": avg_compression_time,
            "tool_statistics": aggregated_tool_stats,
        }
