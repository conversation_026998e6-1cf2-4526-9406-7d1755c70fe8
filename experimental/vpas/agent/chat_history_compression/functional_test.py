#!/usr/bin/env python3
"""Functional test script that tests core functionality without external dependencies."""

import sys
import os
from pathlib import Path

# Add the repository root to Python path
sys.path.insert(0, "/home/<USER>/augment")


def test_configuration_system():
    """Test the configuration system functionality."""
    print("Testing Configuration System...")

    try:
        from experimental.vpas.agent.chat_history_compression.config import (
            CompressionSystemConfig,
            CompressionConfig,
            EvaluationConfig,
        )

        # Test default configuration creation
        config = CompressionSystemConfig()
        assert config is not None
        print("✓ Default configuration created")

        # Test configuration validation
        issues = config.validate()
        assert isinstance(issues, list)
        print(f"✓ Configuration validation works (found {len(issues)} issues)")

        # Test configuration serialization
        config_dict = config.to_dict()
        assert isinstance(config_dict, dict)
        assert "compression" in config_dict
        assert "evaluation" in config_dict
        print("✓ Configuration serialization works")

        # Test configuration deserialization
        new_config = CompressionSystemConfig.from_dict(config_dict)
        assert new_config is not None
        print("✓ Configuration deserialization works")

        # Test specific settings
        assert config.compression.compressible_tools == [
            "view",
            "codebase-retrieval",
            "launch-process",
            "str-replace-editor",
            "save-file",
            "grep-search",
        ]
        print("✓ Default compressible tools configured correctly")

        return True

    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_utility_functions():
    """Test utility functions that don't require external dependencies."""
    print("\nTesting Utility Functions...")

    try:
        # Import utils module directly
        sys.path.insert(
            0, "/home/<USER>/augment/experimental/vpas/agent/chat_history_compression"
        )
        import utils

        # Test should_compress_tool function
        assert utils.should_compress_tool("view")
        assert utils.should_compress_tool("codebase-retrieval")
        assert utils.should_compress_tool("str-replace-editor")
        assert not utils.should_compress_tool("unknown-tool")
        assert not utils.should_compress_tool("linear")
        print("✓ should_compress_tool function works correctly")

        # Test tool registry (basic functionality)
        utils._tool_use_registry.clear()
        assert len(utils._tool_use_registry) == 0
        print("✓ Tool registry can be cleared")

        return True

    except Exception as e:
        print(f"✗ Utility functions test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_data_structures():
    """Test the evaluation data structures."""
    print("\nTesting Data Structures...")

    try:
        from experimental.vpas.agent.chat_history_compression.eval_types import (
            ToolCompressionResult,
            CompressionQualityMetrics,
            CompressionAnalysisSummary,
        )

        # Test ToolCompressionResult
        tool_result = ToolCompressionResult(
            tool_name="view",
            tool_use_id="test_123",
            original_tokens=100,
            compressed_tokens=50,
            compression_method="placeholder",
            preserved_info={"has_error_info": False},
        )

        assert tool_result.compression_rate == 0.5
        assert tool_result.token_savings == 50
        print("✓ ToolCompressionResult works correctly")

        # Test CompressionQualityMetrics
        quality_metrics = CompressionQualityMetrics(
            information_retention_score=0.8,
            context_preservation_score=0.9,
            tool_output_relevance_score=0.7,
            overall_quality_score=0.8,
            critical_losses=[],
            feedback="Good compression quality",
        )

        assert quality_metrics.average_score == 0.8
        print("✓ CompressionQualityMetrics works correctly")

        return True

    except Exception as e:
        print(f"✗ Data structures test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_placeholder_compressor_basic():
    """Test PlaceholderCompressor basic functionality."""
    print("\nTesting PlaceholderCompressor...")

    try:
        from experimental.vpas.agent.chat_history_compression.placeholder_compressor import (
            PlaceholderCompressor,
        )

        # Create compressor instance
        compressor = PlaceholderCompressor()
        assert compressor is not None
        print("✓ PlaceholderCompressor instance created")

        # Test get_name method
        name = compressor.get_name()
        assert name == "PlaceholderCompressor"
        print("✓ get_name method works")

        # Test compress with empty list
        result = compressor.compress([])
        assert result == []
        print("✓ Empty list compression works")

        # Test get_stats after empty compression
        stats = compressor.get_stats()
        assert stats is not None
        assert stats.original_tokens == 0
        assert stats.compressed_tokens == 0
        print("✓ Statistics tracking works")

        return True

    except Exception as e:
        print(f"✗ PlaceholderCompressor test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_html_report_generator():
    """Test HTML report generator basic functionality."""
    print("\nTesting HTML Report Generator...")

    try:
        from experimental.vpas.agent.chat_history_compression.html_report.report_generator import (
            CompressionReportGenerator,
        )
        import tempfile

        # Create report generator with temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            generator = CompressionReportGenerator(output_dir=temp_dir)
            assert generator is not None
            print("✓ CompressionReportGenerator instance created")

            # Check that output directory was created
            assert Path(temp_dir).exists()
            print("✓ Output directory handling works")

        return True

    except Exception as e:
        print(f"✗ HTML Report Generator test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_file_completeness():
    """Test that all major components can be imported."""
    print("\nTesting Component Imports...")

    components = [
        (
            "ChatHistoryCompressor",
            "experimental.vpas.agent.chat_history_compression.chat_history_compression",
        ),
        (
            "PlaceholderCompressor",
            "experimental.vpas.agent.chat_history_compression.placeholder_compressor",
        ),
        (
            "CompressionMetrics",
            "experimental.vpas.agent.chat_history_compression.metrics",
        ),
        (
            "CompressionSystemConfig",
            "experimental.vpas.agent.chat_history_compression.config",
        ),
        (
            "CompressionReportGenerator",
            "experimental.vpas.agent.chat_history_compression.html_report.report_generator",
        ),
    ]

    imported_count = 0

    for component_name, module_path in components:
        try:
            module = __import__(module_path, fromlist=[component_name])
            component = getattr(module, component_name)
            assert component is not None
            print(f"✓ {component_name} imported successfully")
            imported_count += 1
        except Exception as e:
            print(f"✗ {component_name} import failed: {e}")

    print(f"✓ {imported_count}/{len(components)} components imported successfully")
    return imported_count == len(components)


def main():
    """Run all functional tests."""
    print("=" * 70)
    print("CHAT HISTORY COMPRESSION FUNCTIONAL TESTS")
    print("=" * 70)

    tests = [
        test_configuration_system,
        test_utility_functions,
        test_data_structures,
        test_placeholder_compressor_basic,
        test_html_report_generator,
        test_file_completeness,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✅ {test.__name__} PASSED\n")
            else:
                failed += 1
                print(f"❌ {test.__name__} FAILED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED with exception: {e}\n")

    print("=" * 70)
    print(f"FUNCTIONAL TEST RESULTS: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 ALL FUNCTIONAL TESTS PASSED!")
        print("\nThe chat history compression system is ready for use!")
        print("\nNext steps:")
        print("1. Install required dependencies (pytest, etc.) for full test suite")
        print("2. Configure access to base.prompt_format.common module")
        print("3. Run integration tests with real data")
        print("4. Build React components for interactive reports")
    else:
        print("⚠️  Some functional tests failed. Please review the errors above.")

    print("=" * 70)

    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
