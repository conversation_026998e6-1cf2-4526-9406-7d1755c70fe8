#!/usr/bin/env python3

import sys

sys.path.append(".")

from experimental.vpas.agent.chat_history_compression.placeholder_compressor import (
    PlaceholderCompressor,
)
from experimental.vpas.agent.chat_history_compression.tests.test_utils import (
    create_test_exchange_with_tool_use,
    create_test_exchange_with_tool_result,
)


def test_compression():
    # Create test data
    tool_use_exchange = create_test_exchange_with_tool_use(
        "view", {"path": "test.py", "type": "file"}, "tool_123"
    )
    view_content = "Here's the result of running `cat -n` on test.py:\n     1\tdef hello():\n     2\t    print('Hello, world!')\n"
    tool_result_exchange = create_test_exchange_with_tool_result(
        "view", view_content, "tool_123"
    )

    exchanges = [tool_use_exchange, tool_result_exchange]
    compressor = PlaceholderCompressor()

    compressed = compressor.compress(exchanges)
    compressed_result = compressed[1]

    for node in compressed_result.request_message:
        if hasattr(node, "tool_result_node") and node.tool_result_node:
            content = node.tool_result_node.content
            print(f"Original length: {len(view_content)}")
            print(f"Compressed length: {len(content)}")
            print(f"Original: {repr(view_content)}")
            print(f"Compressed: {repr(content)}")
            print(f"Compression successful: {len(content) < len(view_content)}")


if __name__ == "__main__":
    test_compression()
