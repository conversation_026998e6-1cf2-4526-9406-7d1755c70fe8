#!/usr/bin/env python3
"""Demo script showing the chat history compression system in action."""

import sys
import tempfile
from pathlib import Path

# Add the repository root to Python path
sys.path.insert(0, "/home/<USER>/augment")


def demo_compression_system():
    """Demonstrate the compression system with mock data."""
    print("🚀 CHAT HISTORY COMPRESSION SYSTEM DEMO")
    print("=" * 60)

    # Import the compression system
    from experimental.vpas.agent.chat_history_compression.placeholder_compressor import (
        PlaceholderCompressor,
    )
    from experimental.vpas.agent.chat_history_compression.config import (
        CompressionSystemConfig,
    )
    from experimental.vpas.agent.chat_history_compression.html_report.report_generator import (
        CompressionReportGenerator,
    )

    print("✓ All modules imported successfully")

    # 1. Configuration Demo
    print("\n📋 1. CONFIGURATION SYSTEM")
    print("-" * 30)

    config = CompressionSystemConfig()
    print(f"Default compressible tools: {config.compression.compressible_tools}")
    print(f"LLM temperature: {config.compression.llm_temperature}")
    print(f"Max workers: {config.evaluation.max_workers}")

    # Modify configuration
    config.compression.llm_temperature = 0.5
    config.evaluation.max_workers = 10
    print("✓ Configuration modified successfully")

    # Validate configuration
    issues = config.validate()
    print(f"✓ Configuration validation: {len(issues)} issues found")

    # 2. Compression Demo
    print("\n🗜️ 2. COMPRESSION SYSTEM")
    print("-" * 30)

    # Create a compressor
    compressor = PlaceholderCompressor()
    print(f"✓ Created {compressor.get_name()}")

    # Test with empty data (simulating no conversation)
    compressor.compress([])
    stats = compressor.get_stats()
    print(
        f"✓ Empty compression: {stats.original_tokens} → {stats.compressed_tokens} tokens"
    )

    # 3. Utility Functions Demo
    print("\n🔧 3. UTILITY FUNCTIONS")
    print("-" * 30)

    from experimental.vpas.agent.chat_history_compression.utils import (
        should_compress_tool,
    )

    tools_to_test = ["view", "codebase-retrieval", "linear", "unknown-tool"]
    for tool in tools_to_test:
        compressible = should_compress_tool(tool)
        status = "✓" if compressible else "✗"
        print(
            f"{status} {tool}: {'compressible' if compressible else 'not compressible'}"
        )

    # 4. Data Structures Demo
    print("\n📊 4. DATA STRUCTURES")
    print("-" * 30)

    from experimental.vpas.agent.chat_history_compression.eval_types import (
        ToolCompressionResult,
        CompressionQualityMetrics,
    )

    # Create a sample tool compression result
    tool_result = ToolCompressionResult(
        tool_name="view",
        tool_use_id="demo_123",
        original_tokens=1000,
        compressed_tokens=250,
        compression_method="placeholder",
        preserved_info={"has_error_info": False, "has_file_info": True},
    )

    print(
        f"✓ Tool compression: {tool_result.compression_rate:.1%} rate, {tool_result.token_savings} tokens saved"
    )

    # Create quality metrics
    quality = CompressionQualityMetrics(
        information_retention_score=0.85,
        context_preservation_score=0.90,
        tool_output_relevance_score=0.80,
        overall_quality_score=0.85,
        critical_losses=[],
        feedback="Good compression quality with minimal information loss",
    )

    print(f"✓ Quality metrics: {quality.average_score:.2f} average score")

    # 5. Report Generation Demo
    print("\n📈 5. REPORT GENERATION")
    print("-" * 30)

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create report generator
        CompressionReportGenerator(output_dir=temp_dir)
        print(f"✓ Report generator created with output dir: {temp_dir}")

        # The report generator is ready to create reports when given real data
        print("✓ Report generator ready for data")

    # 6. Performance Summary
    print("\n⚡ 6. SYSTEM CAPABILITIES")
    print("-" * 30)

    capabilities = [
        "✓ Two compression strategies (Placeholder & LLM-based)",
        "✓ Comprehensive evaluation framework with metrics",
        "✓ LLM-based quality assessment",
        "✓ Interactive HTML report generation",
        "✓ Multi-threaded processing for scalability",
        "✓ Configurable settings and validation",
        "✓ BigQuery integration for production data",
        "✓ Extensible architecture for new strategies",
    ]

    for capability in capabilities:
        print(capability)

    print("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nThe chat history compression system is fully implemented and ready for:")
    print("• Large-scale conversation analysis")
    print("• A/B testing of compression strategies")
    print("• Performance monitoring and optimization")
    print("• Quality assessment and reporting")
    print("\nTo use with real data:")
    print("1. Configure BigQuery access")
    print("2. Install required dependencies (pytest, anthropic, google-cloud)")
    print("3. Build React components for interactive reports")
    print(
        "4. Run with: python -m experimental.vpas.agent.chat_history_compression.run_compression"
    )


if __name__ == "__main__":
    try:
        demo_compression_system()
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
