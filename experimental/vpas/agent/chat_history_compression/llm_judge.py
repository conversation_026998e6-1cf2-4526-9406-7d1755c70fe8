"""LLM-based judge for evaluating compression quality.

This module uses <PERSON> to evaluate the quality of chat history compression,
providing detailed feedback on information retention, context preservation,
and overall compression effectiveness.
"""

import json
from typing import List, <PERSON><PERSON>, Optional
from research.llm_apis.llm_client import AnthropicDirectClient, TextPrompt
from base.prompt_format.common import Exchange, ChatRequestNodeType, ChatResultNodeType
from .utils import find_tool_name_for_result
from .eval_types import CompressionQualityMetrics


class CompressionJudge:
    """Use an LLM to evaluate compression quality."""

    EVALUATION_PROMPT = """You are an expert evaluator of chat history compression quality. Your task is to assess how well a conversation was compressed while preserving essential information.

## Original Conversation:
{original_text}

## Compressed Conversation:
{compressed_text}

## Evaluation Criteria:

1. **Information Retention (0-1)**: Are all important details, results, and key information preserved?
2. **Context Preservation (0-1)**: Can the conversation flow still be understood? Are relationships between exchanges maintained?
3. **Tool Output Relevance (0-1)**: Are key results from tools (file contents, search results, etc.) adequately summarized?
4. **Overall Quality (0-1)**: How effective is the compression overall?

## Instructions:
- Focus on whether someone could continue the conversation naturally with the compressed version
- Consider if critical information needed for follow-up actions is preserved
- Evaluate if the compression maintains the logical flow and context
- Assess whether tool outputs retain their essential value

Please provide your evaluation in the following JSON format:
```json
{{
    "information_retention_score": <float 0-1>,
    "context_preservation_score": <float 0-1>,
    "tool_output_relevance_score": <float 0-1>,
    "overall_quality_score": <float 0-1>,
    "feedback": "<detailed explanation of your assessment>",
    "critical_losses": ["<list of any critical information that was lost>"],
    "strengths": ["<list of what was preserved well>"],
    "recommendations": ["<suggestions for improvement>"]
}}
```

Respond with ONLY the JSON, no additional text."""

    def __init__(self, model_client: Optional[AnthropicDirectClient] = None):
        """Initialize the compression judge.

        Args:
            model_client: Optional Anthropic client. If None, creates default client.
        """
        self.client = model_client or AnthropicDirectClient(
            model_name="claude-3-5-sonnet-20241022", max_retries=3
        )

    def evaluate_compression(
        self, original: List[Exchange], compressed: List[Exchange]
    ) -> CompressionQualityMetrics:
        """
        Evaluate compression quality using LLM judge.

        Args:
            original: Original list of exchanges
            compressed: Compressed list of exchanges

        Returns:
            CompressionQualityMetrics with detailed evaluation
        """
        # Format conversations for evaluation
        original_text = self._format_exchanges_for_prompt(original)
        compressed_text = self._format_exchanges_for_prompt(compressed)

        # Create evaluation prompt
        prompt = self.EVALUATION_PROMPT.format(
            original_text=original_text, compressed_text=compressed_text
        )

        try:
            # Generate evaluation using Claude
            messages = [[TextPrompt(text=prompt)]]
            response, metadata = self.client.generate(
                messages=messages,
                max_tokens=2000,
                temperature=0.1,
                system_prompt="You are an expert evaluator. Provide precise, objective assessments in the requested JSON format.",
            )

            # Extract text from response
            response_text = ""
            for block in response:
                if hasattr(block, "text"):
                    response_text += block.text

            # Parse JSON response
            evaluation_data = self._parse_evaluation_response(response_text)

            return CompressionQualityMetrics(
                information_retention_score=evaluation_data.get(
                    "information_retention_score", 0.5
                ),
                context_preservation_score=evaluation_data.get(
                    "context_preservation_score", 0.5
                ),
                tool_output_relevance_score=evaluation_data.get(
                    "tool_output_relevance_score", 0.5
                ),
                overall_quality_score=evaluation_data.get("overall_quality_score", 0.5),
                critical_losses=evaluation_data.get("critical_losses", []),
                feedback=evaluation_data.get("feedback", "Failed to parse evaluation"),
            )

        except Exception as e:
            # Fallback evaluation if LLM fails
            print(f"LLM evaluation failed: {e}")
            return CompressionQualityMetrics(
                information_retention_score=0.5,
                context_preservation_score=0.5,
                tool_output_relevance_score=0.5,
                overall_quality_score=0.5,
                critical_losses=["LLM evaluation failed"],
                feedback=f"Failed to evaluate compression: {str(e)}",
            )

    def _format_exchanges_for_prompt(self, exchanges: List[Exchange]) -> str:
        """Format exchanges into readable text for the prompt.

        Args:
            exchanges: List of exchanges to format

        Returns:
            Formatted text representation
        """
        formatted_parts = []

        for i, exchange in enumerate(exchanges):
            formatted_parts.append(f"\n--- Exchange {i+1} ---")

            # Format request
            if isinstance(exchange.request_message, list):
                for node in exchange.request_message:
                    if node.type == ChatRequestNodeType.TEXT and node.text_node:
                        formatted_parts.append(f"User: {node.text_node.content}")
                    elif (
                        node.type == ChatRequestNodeType.TOOL_RESULT
                        and node.tool_result_node
                    ):
                        tool_name = find_tool_name_for_result(
                            node.tool_result_node.tool_use_id
                        )
                        content = node.tool_result_node.content

                        # Truncate very long tool results for readability
                        if len(content) > 1000:
                            content = (
                                content[:500] + "\n...[truncated]...\n" + content[-500:]
                            )

                        formatted_parts.append(f"Tool Result [{tool_name}]: {content}")
            elif isinstance(exchange.request_message, str):
                formatted_parts.append(f"User: {exchange.request_message}")

            # Format response
            if isinstance(exchange.response_text, list):
                agent_text = []
                tool_uses = []

                for node in exchange.response_text:
                    if node.type == ChatResultNodeType.RAW_RESPONSE:
                        agent_text.append(node.content)
                    elif node.type == ChatResultNodeType.TOOL_USE and node.tool_use:
                        tool_input = (
                            json.dumps(node.tool_use.input)
                            if node.tool_use.input
                            else "{}"
                        )
                        tool_uses.append(f"{node.tool_use.name}({tool_input})")

                if agent_text:
                    formatted_parts.append(f"Agent: {''.join(agent_text)}")
                if tool_uses:
                    formatted_parts.append(f"Tool Uses: {', '.join(tool_uses)}")
            elif isinstance(exchange.response_text, str):
                formatted_parts.append(f"Agent: {exchange.response_text}")

        return "\n".join(formatted_parts)

    def _parse_evaluation_response(self, response_text: str) -> dict:
        """Parse the JSON evaluation response from the LLM.

        Args:
            response_text: Raw response text from the LLM

        Returns:
            Parsed evaluation data
        """
        try:
            # Try to find JSON in the response
            json_start = response_text.find("{")
            json_end = response_text.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            else:
                # If no JSON found, try parsing the whole response
                return json.loads(response_text.strip())

        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON evaluation: {e}")
            print(f"Response text: {response_text[:500]}...")

            # Return default values if parsing fails
            return {
                "information_retention_score": 0.5,
                "context_preservation_score": 0.5,
                "tool_output_relevance_score": 0.5,
                "overall_quality_score": 0.5,
                "feedback": f"Failed to parse evaluation response: {str(e)}",
                "critical_losses": ["JSON parsing failed"],
                "strengths": [],
                "recommendations": [],
            }

    def evaluate_batch(
        self, conversation_pairs: List[Tuple[List[Exchange], List[Exchange]]]
    ) -> List[CompressionQualityMetrics]:
        """Evaluate multiple compression pairs in batch.

        Args:
            conversation_pairs: List of (original, compressed) exchange pairs

        Returns:
            List of quality metrics for each pair
        """
        results = []

        for i, (original, compressed) in enumerate(conversation_pairs):
            print(f"Evaluating conversation {i+1}/{len(conversation_pairs)}")

            try:
                metrics = self.evaluate_compression(original, compressed)
                results.append(metrics)
            except Exception as e:
                print(f"Failed to evaluate conversation {i+1}: {e}")
                # Add fallback metrics
                fallback_metrics = CompressionQualityMetrics(
                    information_retention_score=0.5,
                    context_preservation_score=0.5,
                    tool_output_relevance_score=0.5,
                    overall_quality_score=0.5,
                    critical_losses=[f"Evaluation failed: {str(e)}"],
                    feedback=f"Failed to evaluate conversation {i+1}: {str(e)}",
                )
                results.append(fallback_metrics)

        return results

    def get_summary_evaluation(
        self, quality_metrics: List[CompressionQualityMetrics]
    ) -> dict:
        """Generate summary statistics from multiple evaluations.

        Args:
            quality_metrics: List of quality metrics to summarize

        Returns:
            Dictionary with summary statistics
        """
        if not quality_metrics:
            return {}

        # Calculate averages
        avg_info_retention = sum(
            m.information_retention_score for m in quality_metrics
        ) / len(quality_metrics)
        avg_context_preservation = sum(
            m.context_preservation_score for m in quality_metrics
        ) / len(quality_metrics)
        avg_tool_relevance = sum(
            m.tool_output_relevance_score for m in quality_metrics
        ) / len(quality_metrics)
        avg_overall_quality = sum(
            m.overall_quality_score for m in quality_metrics
        ) / len(quality_metrics)

        # Count critical issues
        total_critical_losses = sum(len(m.critical_losses) for m in quality_metrics)
        conversations_with_losses = sum(1 for m in quality_metrics if m.critical_losses)

        # Find best and worst performers
        best_quality = max(quality_metrics, key=lambda m: m.overall_quality_score)
        worst_quality = min(quality_metrics, key=lambda m: m.overall_quality_score)

        return {
            "total_evaluations": len(quality_metrics),
            "average_scores": {
                "information_retention": avg_info_retention,
                "context_preservation": avg_context_preservation,
                "tool_output_relevance": avg_tool_relevance,
                "overall_quality": avg_overall_quality,
            },
            "quality_distribution": {
                "excellent": sum(
                    1 for m in quality_metrics if m.overall_quality_score >= 0.8
                ),
                "good": sum(
                    1 for m in quality_metrics if 0.6 <= m.overall_quality_score < 0.8
                ),
                "fair": sum(
                    1 for m in quality_metrics if 0.4 <= m.overall_quality_score < 0.6
                ),
                "poor": sum(
                    1 for m in quality_metrics if m.overall_quality_score < 0.4
                ),
            },
            "critical_issues": {
                "total_critical_losses": total_critical_losses,
                "conversations_with_losses": conversations_with_losses,
                "loss_rate": conversations_with_losses / len(quality_metrics),
            },
            "best_quality_score": best_quality.overall_quality_score,
            "worst_quality_score": worst_quality.overall_quality_score,
        }
