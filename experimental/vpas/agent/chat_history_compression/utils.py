"""Utility functions for chat history compression.

This module provides helper functions for working with Exchange objects,
node types, and tool registries.
"""

from typing import Dict, Optional, List, Tuple, Any
from base.prompt_format.common import (
    ChatRequestNode,
    ChatResultNode,
    Exchange,
    ChatRequestNodeType,
    ChatResultNodeType,
)

# Global mapping to track tool uses across exchanges
_tool_use_registry: Dict[str, Tuple[str, Exchange]] = {}


def register_tool_uses(exchanges: List[Exchange]) -> None:
    """Build a registry of tool_use_id -> (tool_name, exchange) mappings.

    Args:
        exchanges: List of Exchange objects to process
    """
    global _tool_use_registry
    _tool_use_registry.clear()

    for exchange in exchanges:
        for node in exchange.response_nodes:
            if is_tool_use_node(node) and node.tool_use:
                _tool_use_registry[node.tool_use.tool_use_id] = (
                    node.tool_use.name,
                    exchange,
                )


def is_tool_output_node(node: ChatRequestNode) -> bool:
    """Check if a node is a tool result node.

    Args:
        node: ChatRequestNode to check

    Returns:
        True if the node is a tool result node
    """
    return node.type == ChatRequestNodeType.TOOL_RESULT


def is_tool_use_node(node: ChatResultNode) -> bool:
    """Check if a node is a tool use node.

    Args:
        node: ChatResultNode to check

    Returns:
        True if the node is a tool use node
    """
    return node.type == ChatResultNodeType.TOOL_USE


def get_tool_name_from_use(node: ChatResultNode) -> str:
    """Extract tool name from a tool use node.

    Args:
        node: ChatResultNode with tool use

    Returns:
        Tool name or empty string if not found
    """
    if node.tool_use:
        return node.tool_use.name
    return ""


def find_tool_name_for_result(tool_use_id: str) -> Optional[str]:
    """Find the tool name for a given tool_use_id.

    Args:
        tool_use_id: The tool use ID to look up

    Returns:
        Tool name if found, None otherwise
    """
    if tool_use_id in _tool_use_registry:
        return _tool_use_registry[tool_use_id][0]
    return None


def should_compress_tool(tool_name: str) -> bool:
    """Check if a tool's output should be compressed.

    Args:
        tool_name: Name of the tool

    Returns:
        True if the tool output should be compressed
    """
    compressible_tools = {
        "view",
        "codebase-retrieval",
        "launch-process",
        "str-replace-editor",
        "save-file",
        "grep-search",
    }
    return tool_name in compressible_tools


def extract_tool_content_summary(node: ChatRequestNode) -> Dict[str, Any]:
    """Extract key information from tool output for summary.

    Args:
        node: ChatRequestNode with tool result

    Returns:
        Dictionary with summary information
    """
    if not node.tool_result_node:
        return {}

    content = node.tool_result_node.content
    tool_name = find_tool_name_for_result(node.tool_result_node.tool_use_id)

    summary = {
        "tool_use_id": node.tool_result_node.tool_use_id,
        "tool_name": tool_name,
        "is_error": node.tool_result_node.is_error,
        "content_length": len(content),
        "line_count": content.count("\n") + 1 if content else 0,
    }

    # Tool-specific extraction
    if tool_name == "view":
        # Extract file path and range from content
        lines = content.split("\n")
        if lines and "cat -n" in lines[0]:
            summary["file_info"] = lines[0]
    elif tool_name == "str-replace-editor":
        # Extract operation type and file
        if "Replacement successful" in content:
            summary["operation"] = "str_replace"
            summary["success"] = True
        elif "File saved" in content:
            summary["operation"] = "save_file"
            summary["success"] = True
    elif tool_name == "codebase-retrieval":
        # Extract number of code sections
        if "code sections were retrieved" in content:
            summary["sections_retrieved"] = True
    elif tool_name == "grep-search":
        # Extract match count
        lines = content.split("\n")
        match_count = len(
            [line for line in lines if line.strip() and not line.startswith("--")]
        )
        summary["match_count"] = match_count

    return summary


def get_exchange_tool_uses(exchange: Exchange) -> List[Tuple[str, str]]:
    """Get all tool uses in an exchange.

    Args:
        exchange: Exchange to analyze

    Returns:
        List of (tool_name, tool_use_id) tuples
    """
    tool_uses = []
    for node in exchange.response_nodes:
        if is_tool_use_node(node) and node.tool_use:
            tool_uses.append((node.tool_use.name, node.tool_use.tool_use_id))
    return tool_uses


def get_exchange_tool_results(exchange: Exchange) -> List[Tuple[str, str]]:
    """Get all tool results in an exchange.

    Args:
        exchange: Exchange to analyze

    Returns:
        List of (tool_name, tool_use_id) tuples
    """
    tool_results = []
    for node in exchange.request_nodes:
        if is_tool_output_node(node) and node.tool_result_node:
            tool_name = find_tool_name_for_result(node.tool_result_node.tool_use_id)
            if tool_name:
                tool_results.append((tool_name, node.tool_result_node.tool_use_id))
    return tool_results
