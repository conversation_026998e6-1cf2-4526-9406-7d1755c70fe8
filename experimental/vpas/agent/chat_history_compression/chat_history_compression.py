"""Chat History Compressor Interface

This module defines the base interface for all chat history compression strategies.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from base.prompt_format.common import Exchange


@dataclass
class CompressionStats:
    """Statistics about the compression operation."""

    original_tokens: int
    compressed_tokens: int
    compression_rate: float
    exchanges_compressed: int
    tool_outputs_compressed: Dict[str, int]  # tool_name -> count


class ChatHistoryCompressor(ABC):
    """Base class for all chat history compression strategies."""

    def __init__(self):
        self._last_stats: Optional[CompressionStats] = None

    @abstractmethod
    def compress(self, chat_history: List[Exchange]) -> List[Exchange]:
        """
        Compress the chat history.

        Args:
            chat_history: List of Exchange objects representing the conversation

        Returns:
            Compressed list of Exchange objects
        """
        pass

    def get_name(self) -> str:
        """Return the name of the compression strategy."""
        return self.__class__.__name__

    def get_stats(self) -> Optional[CompressionStats]:
        """Return statistics about the last compression operation."""
        return self._last_stats

    def _update_stats(self, stats: CompressionStats) -> None:
        """Update the internal statistics."""
        self._last_stats = stats
