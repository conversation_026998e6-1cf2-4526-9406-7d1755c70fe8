# Chat History Compression Implementation Plan

## Overview

This document outlines the detailed implementation plan for compressing Agent chat history to reduce token usage while preserving important information for conversation continuity.

## Architecture Overview

### Core Components

1. **ChatHistoryCompressor Interface**
   - Base abstract class defining the compression contract
   - Input: list of Exchange objects
   - Output: compressed list of Exchange objects

2. **Compression Strategies**
   - PlaceholderCompressor: Replace tool outputs with placeholders
   - GeminiFlashCompressor: Use Gemini Flash LLM for intelligent summarization

3. **Evaluation Framework**
   - LLM judge to evaluate compression quality
   - Metrics calculation (compression rate, information retention)
   - ChatHistoryCompressionEvalResult for storing results

4. **React Report Generation**
   - HTML report with compression statistics
   - Visual comparison of original vs compressed history
   - Interactive exploration of compression results

## Data Structures

### Exchange Structure
Based on the codebase research, an Exchange contains:
```python
@dataclass
class Exchange:
    request_message: str  # User message (deprecated if request_nodes present)
    response_text: str    # Agent response (deprecated if response_nodes present)
    request_id: str       # Unique identifier
    request_nodes: list[ChatRequestNode]  # Structured request content
    response_nodes: list[ChatResultNode]  # Structured response content
```

### Node Types
- **ChatRequestNode**: Can be TEXT, TOOL_RESULT, IMAGE, IDE_STATE, EDIT_EVENTS
- **ChatResultNode**: Can be RAW_RESPONSE, TOOL_USE, AGENT_MEMORY

### Tool-specific Structures
- **ChatResultToolUse**: Contains tool_use_id, tool_name, input_json
- **ChatRequestToolResult**: Contains tool_use_id, content, is_error, request_id

## Implementation Details

### Phase 1: Core Infrastructure

#### 1.1 ChatHistoryCompressor Interface
```python
# experimental/vpas/agent/chat_history_compression/chat_history_compressor.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from dataclasses import dataclass
from base.prompt_format.common import Exchange

@dataclass
class CompressionStats:
    """Statistics about the compression operation."""
    original_tokens: int
    compressed_tokens: int
    compression_rate: float
    exchanges_compressed: int
    tool_outputs_compressed: Dict[str, int]  # tool_name -> count

class ChatHistoryCompressor(ABC):
    """Base class for all chat history compression strategies."""

    @abstractmethod
    def compress(self, chat_history: List[Exchange]) -> List[Exchange]:
        """
        Compress the chat history.

        Args:
            chat_history: List of Exchange objects representing the conversation

        Returns:
            Compressed list of Exchange objects
        """
        pass

    def get_name(self) -> str:
        """Return the name of the compression strategy."""
        return self.__class__.__name__

    def get_stats(self) -> CompressionStats:
        """Return statistics about the last compression operation."""
        return getattr(self, '_last_stats', None)
```

#### 1.2 Utility Functions
```python
# experimental/vpas/agent/chat_history_compression/utils.py
from typing import Dict, Optional, List, Tuple
from base.prompt_format.common import (
    ChatRequestNode, ChatResultNode, Exchange,
    ChatRequestNodeType, ChatResultNodeType
)

# Global mapping to track tool uses across exchanges
_tool_use_registry: Dict[str, Tuple[str, Exchange]] = {}

def register_tool_uses(exchanges: List[Exchange]) -> None:
    """Build a registry of tool_use_id -> (tool_name, exchange) mappings."""
    global _tool_use_registry
    _tool_use_registry.clear()

    for exchange in exchanges:
        if exchange.response_nodes:
            for node in exchange.response_nodes:
                if is_tool_use_node(node) and node.tool_use:
                    _tool_use_registry[node.tool_use.tool_use_id] = (
                        node.tool_use.tool_name, exchange
                    )

def is_tool_output_node(node: ChatRequestNode) -> bool:
    """Check if a node is a tool result node."""
    return node.type == ChatRequestNodeType.TOOL_RESULT

def is_tool_use_node(node: ChatResultNode) -> bool:
    """Check if a node is a tool use node."""
    return node.type == ChatResultNodeType.TOOL_USE

def get_tool_name_from_use(node: ChatResultNode) -> str:
    """Extract tool name from a tool use node."""
    if node.tool_use:
        return node.tool_use.tool_name
    return ""

def find_tool_name_for_result(tool_use_id: str) -> Optional[str]:
    """Find the tool name for a given tool_use_id."""
    if tool_use_id in _tool_use_registry:
        return _tool_use_registry[tool_use_id][0]
    return None

def should_compress_tool(tool_name: str) -> bool:
    """Check if a tool's output should be compressed."""
    compressible_tools = {
        "view", "codebase-retrieval", "launch-process",
        "str-replace-editor", "save-file"
    }
    return tool_name in compressible_tools

def extract_tool_content_summary(node: ChatRequestNode) -> Dict[str, Any]:
    """Extract key information from tool output for summary."""
    if not node.tool_result_node:
        return {}

    content = node.tool_result_node.content
    tool_name = find_tool_name_for_result(node.tool_result_node.tool_use_id)

    summary = {
        "tool_use_id": node.tool_result_node.tool_use_id,
        "tool_name": tool_name,
        "is_error": node.tool_result_node.is_error,
        "content_length": len(content),
        "line_count": content.count('\n') + 1 if content else 0
    }

    # Tool-specific extraction
    if tool_name == "view":
        # Extract file path and range from content
        lines = content.split('\n')
        if lines and "cat -n" in lines[0]:
            summary["file_info"] = lines[0]
    elif tool_name == "str-replace-editor":
        # Extract operation type and file
        if "Replacement successful" in content:
            summary["operation"] = "str_replace"
            summary["success"] = True

    return summary
```

### Phase 2: Compression Strategies

#### 2.1 PlaceholderCompressor
```python
# experimental/vpas/agent/chat_history_compression/placeholder_compressor.py
class PlaceholderCompressor(ChatHistoryCompressor):
    """Replace tool outputs with placeholder messages."""

    def compress(self, chat_history: List[Exchange]) -> List[Exchange]:
        compressed_history = []

        for exchange in chat_history:
            compressed_exchange = self._compress_exchange(exchange)
            compressed_history.append(compressed_exchange)

        return compressed_history

    def _compress_exchange(self, exchange: Exchange) -> Exchange:
        # Deep copy the exchange
        compressed = copy.deepcopy(exchange)

        # Compress request nodes (tool results)
        if compressed.request_nodes:
            compressed.request_nodes = self._compress_request_nodes(
                compressed.request_nodes
            )

        # Track tool uses in response for context
        tool_uses = self._extract_tool_uses(exchange.response_nodes)

        return compressed

    def _compress_request_nodes(self, nodes: List[ChatRequestNode]) -> List[ChatRequestNode]:
        compressed_nodes = []

        for node in nodes:
            if is_tool_output_node(node) and node.tool_result_node:
                # Find corresponding tool use to get tool name
                tool_name = self._find_tool_name_for_result(node.tool_result_node.tool_use_id)

                if should_compress_tool(tool_name):
                    compressed_node = self._create_placeholder_node(node, tool_name)
                    compressed_nodes.append(compressed_node)
                else:
                    compressed_nodes.append(node)
            else:
                compressed_nodes.append(node)

        return compressed_nodes

    def _create_placeholder_node(self, node: ChatRequestNode, tool_name: str) -> ChatRequestNode:
        placeholder_text = f"[Tool output compressed: {tool_name} - {node.tool_result_node.tool_use_id}]"

        compressed_node = copy.deepcopy(node)
        compressed_node.tool_result_node.content = placeholder_text
        compressed_node.tool_result_node.content_nodes = None

        return compressed_node
```

#### 2.2 GeminiFlashCompressor
```python
# experimental/vpas/agent/chat_history_compression/gemini_flash_compressor.py
from base.third_party_clients.google_genai_client import GoogleGenaiClient

class GeminiFlashCompressor(ChatHistoryCompressor):
    """Use Gemini Flash to intelligently summarize tool outputs."""

    def __init__(self, client: GoogleGenaiClient = None):
        self.client = client or GoogleGenaiClient(model="gemini-1.5-flash-latest")

    def compress(self, chat_history: List[Exchange]) -> List[Exchange]:
        # Put whole chat history in context
        context = self._build_context(chat_history)

        compressed_history = []
        for i, exchange in enumerate(chat_history):
            compressed_exchange = self._compress_exchange_with_llm(
                exchange, context, i
            )
            compressed_history.append(compressed_exchange)

        return compressed_history

    def _compress_exchange_with_llm(self, exchange: Exchange, context: str, index: int) -> Exchange:
        # Implementation details for LLM-based compression
        pass
```

### Phase 3: Evaluation Framework

#### 3.1 Data Structures
```python
# experimental/vpas/agent/chat_history_compression/eval_types.py
from dataclasses import dataclass, field
from typing import List, Dict, Optional
from datetime import datetime

@dataclass
class ToolCompressionResult:
    """Result of compressing a single tool output."""
    tool_name: str
    tool_use_id: str
    original_tokens: int
    compressed_tokens: int
    compression_method: str
    preserved_info: Dict[str, Any]

@dataclass
class ExchangeCompressionResult:
    """Result of compressing a single exchange."""
    request_id: str
    original_exchange: Exchange
    compressed_exchange: Exchange
    tool_compressions: List[ToolCompressionResult]
    original_tokens: int
    compressed_tokens: int

@dataclass
class ChatHistoryCompressionEvalResult:
    """Complete evaluation result for a conversation."""
    conversation_id: str
    timestamp: datetime
    compressor_name: str

    # Compression results
    exchange_results: List[ExchangeCompressionResult]

    # Overall metrics
    total_original_tokens: int
    total_compressed_tokens: int
    compression_rate: float

    # Tool-specific metrics
    tool_compression_stats: Dict[str, Dict[str, float]]  # tool_name -> {metric: value}

    # Quality evaluation
    llm_judge_score: Optional[float] = None
    llm_judge_feedback: Optional[str] = None
    information_retention_score: Optional[float] = None

    # Performance metrics
    compression_time_seconds: float = 0.0
    evaluation_time_seconds: float = 0.0

#### 3.2 Compression Metrics
```python
# experimental/vpas/agent/chat_history_compression/metrics.py
from base.prompt_format_chat.lib.token_counter_claude import ClaudeTokenCounter
from typing import List, Dict, Tuple
import time

class CompressionMetrics:
    def __init__(self):
        self.token_counter = ClaudeTokenCounter()

    def evaluate_compression(
        self,
        original: List[Exchange],
        compressed: List[Exchange],
        compressor_name: str,
        conversation_id: str
    ) -> ChatHistoryCompressionEvalResult:
        """Evaluate compression results and generate metrics."""
        start_time = time.time()

        # Build exchange-level results
        exchange_results = []
        for orig, comp in zip(original, compressed):
            exchange_result = self._evaluate_exchange(orig, comp)
            exchange_results.append(exchange_result)

        # Calculate overall metrics
        total_original = sum(r.original_tokens for r in exchange_results)
        total_compressed = sum(r.compressed_tokens for r in exchange_results)
        compression_rate = (total_original - total_compressed) / total_original if total_original > 0 else 0

        # Calculate tool-specific stats
        tool_stats = self._calculate_tool_stats(exchange_results)

        eval_result = ChatHistoryCompressionEvalResult(
            conversation_id=conversation_id,
            timestamp=datetime.now(),
            compressor_name=compressor_name,
            exchange_results=exchange_results,
            total_original_tokens=total_original,
            total_compressed_tokens=total_compressed,
            compression_rate=compression_rate,
            tool_compression_stats=tool_stats,
            compression_time_seconds=time.time() - start_time
        )

        return eval_result

    def _count_exchange_tokens(self, exchange: Exchange) -> int:
        """Count total tokens in an exchange."""
        total = 0

        # Count request nodes
        if exchange.request_nodes:
            for node in exchange.request_nodes:
                if node.type == ChatRequestNodeType.TEXT and node.text_node:
                    total += self.token_counter.count_tokens(node.text_node.content)
                elif node.type == ChatRequestNodeType.TOOL_RESULT and node.tool_result_node:
                    total += self.token_counter.count_tokens(node.tool_result_node.content)

        # Count response nodes
        if exchange.response_nodes:
            for node in exchange.response_nodes:
                total += self.token_counter.count_tokens(node.content)
                if node.tool_use and node.tool_use.input_json:
                    total += self.token_counter.count_tokens(node.tool_use.input_json)

        return total
```

#### 3.3 LLM Judge Evaluation
```python
# experimental/vpas/agent/chat_history_compression/llm_judge.py
from typing import List, Tuple
import json
from base.third_party_clients.anthropic_client import AnthropicClient

class CompressionJudge:
    """Use an LLM to evaluate compression quality."""

    EVALUATION_PROMPT = """You are evaluating the quality of chat history compression.

Original conversation:
{original_text}

Compressed conversation:
{compressed_text}

Please evaluate the compression based on:
1. Information Retention: Are all important details preserved?
2. Context Preservation: Can the conversation flow still be understood?
3. Tool Output Relevance: Are key results from tools maintained?

Provide your evaluation in JSON format:
{{
    "information_retention_score": <float 0-1>,
    "context_preservation_score": <float 0-1>,
    "tool_output_relevance_score": <float 0-1>,
    "overall_score": <float 0-1>,
    "feedback": "<detailed feedback about what was preserved well and what was lost>",
    "critical_losses": ["<list of critical information that was lost>"]
}}
"""

    def __init__(self, model_client=None):
        self.client = model_client or AnthropicClient(model="claude-3-haiku-20240307")

    def evaluate_compression(
        self,
        original: List[Exchange],
        compressed: List[Exchange]
    ) -> Tuple[float, str]:
        """
        Evaluate compression quality using LLM judge.

        Returns:
            Tuple of (score, feedback)
        """
        original_text = self._format_exchanges_for_prompt(original)
        compressed_text = self._format_exchanges_for_prompt(compressed)

        prompt = self.EVALUATION_PROMPT.format(
            original_text=original_text,
            compressed_text=compressed_text
        )

        response = self.client.generate(prompt, temperature=0.1)

        try:
            evaluation = json.loads(response)
            return evaluation["overall_score"], evaluation["feedback"]
        except (json.JSONDecodeError, KeyError):
            # Fallback if parsing fails
            return 0.5, "Failed to parse LLM evaluation"

    def _format_exchanges_for_prompt(self, exchanges: List[Exchange]) -> str:
        """Format exchanges into readable text for the prompt."""
        formatted_parts = []

        for i, exchange in enumerate(exchanges):
            formatted_parts.append(f"\n--- Exchange {i+1} ---")

            # Format request
            if exchange.request_nodes:
                for node in exchange.request_nodes:
                    if node.type == ChatRequestNodeType.TEXT and node.text_node:
                        formatted_parts.append(f"User: {node.text_node.content}")
                    elif node.type == ChatRequestNodeType.TOOL_RESULT and node.tool_result_node:
                        tool_name = find_tool_name_for_result(node.tool_result_node.tool_use_id)
                        formatted_parts.append(
                            f"Tool Result [{tool_name}]: {node.tool_result_node.content[:500]}..."
                            if len(node.tool_result_node.content) > 500
                            else f"Tool Result [{tool_name}]: {node.tool_result_node.content}"
                        )

            # Format response
            if exchange.response_nodes:
                agent_text = []
                tool_uses = []

                for node in exchange.response_nodes:
                    if node.type == ChatResultNodeType.RAW_RESPONSE:
                        agent_text.append(node.content)
                    elif node.type == ChatResultNodeType.TOOL_USE and node.tool_use:
                        tool_uses.append(f"{node.tool_use.tool_name}({node.tool_use.tool_use_id})")

                if agent_text:
                    formatted_parts.append(f"Agent: {''.join(agent_text)}")
                if tool_uses:
                    formatted_parts.append(f"Tool Uses: {', '.join(tool_uses)}")

        return '\n'.join(formatted_parts)
```

### Phase 4: Report Generation

#### 4.1 React Components Structure
```
experimental/vpas/agent/chat_history_compression/html_report/react/
├── src/
│   ├── components/
│   │   ├── CompressionSummary/
│   │   │   ├── CompressionSummary.tsx
│   │   │   └── CompressionSummary.css
│   │   ├── ExchangeComparison/
│   │   │   ├── ExchangeComparison.tsx
│   │   │   └── ExchangeComparison.css
│   │   └── MetricsDisplay/
│   │       ├── MetricsDisplay.tsx
│   │       └── MetricsDisplay.css
│   ├── types/
│   │   └── index.ts
│   └── App.tsx
```

#### 4.2 HTML Report Generator
```python
# experimental/vpas/agent/chat_history_compression/html_report/html_report_generator.py
def generate_compression_report(eval_results: List[ChatHistoryCompressionEvalResult]) -> str:
    """Generate HTML report for compression evaluation results."""
    # Implementation following the pattern from replay_eval
    pass
```

## Implementation Phases

### Phase 1: Foundation (Week 1)
1. Create directory structure
2. Implement ChatHistoryCompressor interface
3. Implement utility functions for node manipulation
4. Set up basic testing framework

### Phase 2: Compression Strategies (Week 2)
1. Implement PlaceholderCompressor
2. Implement GeminiFlashCompressor
3. Add unit tests for both compressors
4. Create compression runner script

### Phase 3: Evaluation (Week 3)
1. Implement compression metrics calculator
2. Implement LLM judge for quality evaluation
3. Create ChatHistoryCompressionEvalResult dataclass
4. Build evaluation pipeline

### Phase 4: Reporting (Week 4)
1. Set up React project structure
2. Implement React components for visualization
3. Create HTML report generator
4. Add interactive features to reports

### Phase 5: Integration & Testing (Week 5)
1. Integration with existing conversation analysis tools
2. End-to-end testing
3. Performance optimization
4. Documentation

## Scripts and Entry Points

### Main Compression Runner
```python
# experimental/vpas/agent/chat_history_compression/run_compression.py
import argparse
import logging
from typing import List
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

from experimental.vpas.agent.analytics.big_query_utils import get_agent_conv_last_request_ids
from experimental.vpas.agent.analytics.conversation import Conversation
from .chat_history_compressor import ChatHistoryCompressor
from .placeholder_compressor import PlaceholderCompressor
from .gemini_flash_compressor import GeminiFlashCompressor
from .metrics import CompressionMetrics
from .llm_judge import CompressionJudge
from .html_report.html_report_generator import generate_compression_report

logger = logging.getLogger(__name__)

def compress_conversation(
    conversation: Conversation,
    compressor: ChatHistoryCompressor,
    metrics: CompressionMetrics,
    judge: CompressionJudge
) -> ChatHistoryCompressionEvalResult:
    """Compress a single conversation and evaluate results."""
    # Convert Conversation to list of Exchange
    exchanges = conversation_to_exchanges(conversation)

    # Register tool uses for lookup
    register_tool_uses(exchanges)

    # Compress
    compressed = compressor.compress(exchanges)

    # Evaluate
    eval_result = metrics.evaluate_compression(
        exchanges, compressed, compressor.get_name(), conversation.last_request_id
    )

    # LLM judge evaluation
    score, feedback = judge.evaluate_compression(exchanges, compressed)
    eval_result.llm_judge_score = score
    eval_result.llm_judge_feedback = feedback

    return eval_result

def main():
    parser = argparse.ArgumentParser(description="Run chat history compression")
    parser.add_argument("--last-hours", type=int, help="Process data from last N hours")
    parser.add_argument("--last-days", type=int, help="Process data from last N days")
    parser.add_argument("--from-date", help="Start date in ISO format")
    parser.add_argument("--to-date", help="End date in ISO format")
    parser.add_argument("--limit", type=int, default=100, help="Max conversations to process")
    parser.add_argument("--compressor", choices=["placeholder", "gemini", "all"],
                       default="all", help="Which compressor to use")
    parser.add_argument("--thread-count", type=int, default=20, help="Number of threads")
    parser.add_argument("--output-dir", default="./compression_results",
                       help="Directory for output files")

    args = parser.parse_args()

    # Set up date range
    if args.last_hours:
        to_date = datetime.now()
        from_date = to_date - timedelta(hours=args.last_hours)
    elif args.last_days:
        to_date = datetime.now()
        from_date = to_date - timedelta(days=args.last_days)
    else:
        from_date = datetime.fromisoformat(args.from_date) if args.from_date else None
        to_date = datetime.fromisoformat(args.to_date) if args.to_date else None

    # Get conversations
    logger.info(f"Fetching conversations from {from_date} to {to_date}")
    request_ids = get_agent_conv_last_request_ids(
        from_date=from_date,
        to_date=to_date,
        limit=args.limit
    )

    # Set up compressors
    compressors = []
    if args.compressor in ["placeholder", "all"]:
        compressors.append(PlaceholderCompressor())
    if args.compressor in ["gemini", "all"]:
        compressors.append(GeminiFlashCompressor())

    # Process conversations
    all_results = {compressor.get_name(): [] for compressor in compressors}

    with ThreadPoolExecutor(max_workers=args.thread_count) as executor:
        for compressor in compressors:
            logger.info(f"Running {compressor.get_name()} compression")

            futures = []
            for request_id in request_ids:
                future = executor.submit(
                    process_conversation,
                    request_id,
                    compressor
                )
                futures.append(future)

            for future in tqdm(as_completed(futures), total=len(futures)):
                try:
                    result = future.result()
                    if result:
                        all_results[compressor.get_name()].append(result)
                except Exception as e:
                    logger.error(f"Error processing conversation: {e}")

    # Generate reports
    for compressor_name, results in all_results.items():
        if results:
            report_path = generate_compression_report(results, args.output_dir)
            logger.info(f"Report generated: {report_path}")

if __name__ == "__main__":
    main()
```

## Testing Strategy

### Unit Tests
```python
# experimental/vpas/agent/chat_history_compression/tests/test_compressors.py
import pytest
from ..placeholder_compressor import PlaceholderCompressor
from ..utils import should_compress_tool, extract_tool_content_summary

class TestPlaceholderCompressor:
    def test_compress_view_tool_output(self):
        """Test compression of view tool output."""
        # Create test exchange with view tool result
        exchange = create_test_exchange_with_tool_result("view", "file content...")
        compressor = PlaceholderCompressor()

        compressed = compressor.compress([exchange])

        assert len(compressed) == 1
        assert "[Tool output compressed: view" in compressed[0].request_nodes[0].tool_result_node.content

    def test_preserve_non_compressible_tools(self):
        """Test that non-compressible tool outputs are preserved."""
        exchange = create_test_exchange_with_tool_result("some-other-tool", "content")
        compressor = PlaceholderCompressor()

        compressed = compressor.compress([exchange])

        assert compressed[0].request_nodes[0].tool_result_node.content == "content"

class TestUtils:
    def test_should_compress_tool(self):
        assert should_compress_tool("view") == True
        assert should_compress_tool("codebase-retrieval") == True
        assert should_compress_tool("unknown-tool") == False

    def test_extract_tool_content_summary(self):
        node = create_test_tool_result_node("view", "Line 1\nLine 2\nLine 3")
        summary = extract_tool_content_summary(node)

        assert summary["line_count"] == 3
        assert summary["content_length"] == 17
```

### Integration Tests
```python
# experimental/vpas/agent/chat_history_compression/tests/test_integration.py
import pytest
from ..run_compression import compress_conversation
from experimental.vpas.agent.analytics.conversation import Conversation

class TestCompressionIntegration:
    def test_end_to_end_compression(self, sample_conversation):
        """Test full compression pipeline."""
        compressor = PlaceholderCompressor()
        metrics = CompressionMetrics()
        judge = CompressionJudge()

        result = compress_conversation(
            sample_conversation,
            compressor,
            metrics,
            judge
        )

        assert result.compression_rate > 0
        assert result.llm_judge_score is not None
        assert len(result.exchange_results) == len(sample_conversation.agent_rounds)
```

## Success Metrics

1. **Compression Rate**: Target 50-70% token reduction
2. **Information Retention**: LLM judge score > 0.8
3. **Processing Speed**: < 1 second per conversation
4. **Report Generation**: < 5 seconds for 100 conversations

## Directory Structure

```
experimental/vpas/agent/chat_history_compression/
├── __init__.py
├── README.md
├── plan.md (this file)
├── task.md
│
├── chat_history_compressor.py          # Base interface
├── placeholder_compressor.py           # Placeholder implementation
├── gemini_flash_compressor.py         # Gemini Flash implementation
├── utils.py                           # Utility functions
├── metrics.py                         # Compression metrics
├── llm_judge.py                       # LLM-based evaluation
├── eval_types.py                      # Data structures
├── run_compression.py                 # Main runner script
│
├── tests/
│   ├── __init__.py
│   ├── test_compressors.py
│   ├── test_utils.py
│   ├── test_metrics.py
│   ├── test_integration.py
│   └── test_performance.py
│
└── html_report/
    ├── __init__.py
    ├── html_report_generator.py
    └── react/
        ├── package.json
        ├── tsconfig.json
        ├── vite.config.ts
        ├── index.html
        └── src/
            ├── main.tsx
            ├── App.tsx
            ├── types/
            │   └── index.ts
            └── components/
                ├── CompressionSummary/
                ├── ExchangeComparison/
                └── MetricsDisplay/
```

## Configuration

### Configuration File
```python
# experimental/vpas/agent/chat_history_compression/config.py
from dataclasses import dataclass
from typing import Set

@dataclass
class CompressionConfig:
    """Configuration for chat history compression."""

    # Tools to compress
    compressible_tools: Set[str] = field(default_factory=lambda: {
        "view", "codebase-retrieval", "launch-process",
        "str-replace-editor", "save-file"
    })

    # Compression thresholds
    min_content_length_for_compression: int = 100  # Don't compress if < 100 chars
    max_placeholder_length: int = 200  # Max length of placeholder text

    # Gemini Flash settings
    gemini_model: str = "gemini-1.5-flash-latest"
    gemini_temperature: float = 0.3
    gemini_max_output_tokens: int = 500

    # Evaluation settings
    llm_judge_model: str = "claude-3-haiku-20240307"
    llm_judge_temperature: float = 0.1

    # Performance settings
    max_concurrent_compressions: int = 20
    compression_timeout_seconds: float = 30.0
```

## Monitoring and Metrics

### Key Metrics to Track
1. **Compression Performance**
   - Average compression rate per tool type
   - Compression time per conversation
   - Token savings distribution

2. **Quality Metrics**
   - LLM judge scores distribution
   - Information retention rates
   - Critical information loss incidents


### Logging
```python
# Use structured logging throughout
import structlog

logger = structlog.get_logger()

logger.info(
    "compression_completed",
    conversation_id=conversation_id,
    compressor=compressor_name,
    original_tokens=original_tokens,
    compressed_tokens=compressed_tokens,
    compression_rate=compression_rate,
    duration_seconds=duration
)
```

## Future Enhancements

1. **Additional Compression Strategies**
   - Semantic clustering of similar tool outputs
   - Progressive summarization based on recency
   - Tool-specific compression algorithms
   - Hybrid approaches combining multiple strategies

2. **Advanced Evaluation**
   - Human evaluation interface
   - A/B testing framework
   - Downstream task performance metrics
   - Automatic quality threshold adjustment

3. **Production Integration**
   - Real-time compression during conversations
   - Configurable compression policies per user/tenant
   - Monitoring and alerting dashboards
   - Compression cache for frequently seen patterns

4. **Tool-Specific Optimizations**
   - Custom compression for each tool type
   - Preserve critical information patterns
   - Learn from user feedback on compression quality

## Next Steps

1. **Week 1**: Set up project structure and implement base interface
2. **Week 2**: Implement PlaceholderCompressor with full test coverage
3. **Week 3**: Implement GeminiFlashCompressor and evaluation framework
4. **Week 4**: Build React report interface

## References

- Conversation Analysis: `experimental/vpas/agent/analytics/`
- Replay Evaluation: `experimental/vpas/agent/replay_eval/`
- Token Counting: `base/prompt_format_chat/lib/token_counter_claude.py`
- Google GenAI Client: `base/third_party_clients/google_genai_client.py`
