"""Data structures for compression evaluation results.

This module defines all the dataclasses used to store and analyze
compression evaluation results, including tool-specific metrics
and overall conversation statistics.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime
from base.prompt_format.common import Exchange


@dataclass
class ToolCompressionResult:
    """Result of compressing a single tool output."""

    tool_name: str
    """Name of the tool that was compressed."""

    tool_use_id: str
    """Unique identifier for the tool use."""

    original_tokens: int
    """Number of tokens in the original tool output."""

    compressed_tokens: int
    """Number of tokens in the compressed tool output."""

    compression_method: str
    """Method used for compression (e.g., 'placeholder', 'llm')."""

    preserved_info: Dict[str, Any]
    """Information about what was preserved during compression."""

    @property
    def compression_rate(self) -> float:
        """Calculate the compression rate for this tool output."""
        if self.original_tokens == 0:
            return 0.0
        return (self.original_tokens - self.compressed_tokens) / self.original_tokens

    @property
    def token_savings(self) -> int:
        """Calculate the number of tokens saved."""
        return self.original_tokens - self.compressed_tokens


@dataclass
class ExchangeCompressionResult:
    """Result of compressing a single exchange."""

    request_id: str
    """Unique identifier for the exchange."""

    original_exchange: Exchange
    """The original exchange before compression."""

    compressed_exchange: Exchange
    """The exchange after compression."""

    tool_compressions: List[ToolCompressionResult]
    """List of tool-specific compression results."""

    original_tokens: int
    """Total tokens in the original exchange."""

    compressed_tokens: int
    """Total tokens in the compressed exchange."""

    @property
    def compression_rate(self) -> float:
        """Calculate the compression rate for this exchange."""
        if self.original_tokens == 0:
            return 0.0
        return (self.original_tokens - self.compressed_tokens) / self.original_tokens

    @property
    def token_savings(self) -> int:
        """Calculate the number of tokens saved."""
        return self.original_tokens - self.compressed_tokens


@dataclass
class ChatHistoryCompressionEvalResult:
    """Complete evaluation result for a conversation."""

    conversation_id: str
    """Unique identifier for the conversation."""

    timestamp: datetime
    """When the evaluation was performed."""

    compressor_name: str
    """Name of the compression strategy used."""

    # Compression results
    exchange_results: List[ExchangeCompressionResult]
    """Results for each exchange in the conversation."""

    # Overall metrics
    total_original_tokens: int
    """Total tokens in the original conversation."""

    total_compressed_tokens: int
    """Total tokens in the compressed conversation."""

    compression_rate: float
    """Overall compression rate for the conversation."""

    # Tool-specific metrics
    tool_compression_stats: Dict[str, Dict[str, float]]
    """Statistics by tool type. Maps tool_name -> {metric: value}."""

    # Quality evaluation (optional, filled by LLM judge)
    llm_judge_score: Optional[float] = None
    """Score from LLM judge evaluation (0-1)."""

    llm_judge_feedback: Optional[str] = None
    """Detailed feedback from LLM judge."""

    information_retention_score: Optional[float] = None
    """Score for information retention quality."""

    # Performance metrics
    compression_time_seconds: float = 0.0
    """Time taken to perform compression."""

    evaluation_time_seconds: float = 0.0
    """Time taken to evaluate compression quality."""

    @property
    def token_savings(self) -> int:
        """Calculate total tokens saved."""
        return self.total_original_tokens - self.total_compressed_tokens

    @property
    def exchanges_count(self) -> int:
        """Get the number of exchanges in the conversation."""
        return len(self.exchange_results)

    @property
    def tools_compressed_count(self) -> int:
        """Get the total number of tool outputs that were compressed."""
        return sum(len(er.tool_compressions) for er in self.exchange_results)


@dataclass
class CompressionBenchmarkResult:
    """Results from benchmarking multiple compression strategies."""

    benchmark_id: str
    """Unique identifier for the benchmark run."""

    timestamp: datetime
    """When the benchmark was performed."""

    dataset_info: Dict[str, Any]
    """Information about the dataset used for benchmarking."""

    compressor_results: Dict[str, List[ChatHistoryCompressionEvalResult]]
    """Results by compressor name. Maps compressor_name -> [eval_results]."""

    summary_stats: Dict[str, Dict[str, Any]]
    """Summary statistics by compressor. Maps compressor_name -> stats."""

    benchmark_time_seconds: float = 0.0
    """Total time taken for the benchmark."""

    @property
    def compressor_names(self) -> List[str]:
        """Get list of compressor names that were benchmarked."""
        return list(self.compressor_results.keys())

    @property
    def total_conversations(self) -> int:
        """Get total number of conversations processed."""
        if not self.compressor_results:
            return 0
        return len(next(iter(self.compressor_results.values())))


@dataclass
class CompressionQualityMetrics:
    """Metrics for evaluating compression quality."""

    information_retention_score: float
    """Score for how well important information was retained (0-1)."""

    context_preservation_score: float
    """Score for how well conversation context was preserved (0-1)."""

    tool_output_relevance_score: float
    """Score for how well key tool outputs were maintained (0-1)."""

    overall_quality_score: float
    """Overall quality score combining all metrics (0-1)."""

    critical_losses: List[str] = field(default_factory=list)
    """List of critical information that was lost during compression."""

    feedback: str = ""
    """Detailed feedback about compression quality."""

    @property
    def average_score(self) -> float:
        """Calculate average of all quality scores."""
        scores = [
            self.information_retention_score,
            self.context_preservation_score,
            self.tool_output_relevance_score,
            self.overall_quality_score,
        ]
        return sum(scores) / len(scores)


@dataclass
class ToolCompressionStats:
    """Statistics for a specific tool's compression performance."""

    tool_name: str
    """Name of the tool."""

    total_compressions: int
    """Total number of times this tool was compressed."""

    total_original_tokens: int
    """Total original tokens for this tool across all compressions."""

    total_compressed_tokens: int
    """Total compressed tokens for this tool across all compressions."""

    average_compression_rate: float
    """Average compression rate for this tool."""

    method_distribution: Dict[str, int]
    """Distribution of compression methods used. Maps method -> count."""

    quality_scores: List[float] = field(default_factory=list)
    """Quality scores for compressions of this tool."""

    @property
    def total_token_savings(self) -> int:
        """Calculate total tokens saved for this tool."""
        return self.total_original_tokens - self.total_compressed_tokens

    @property
    def average_quality_score(self) -> float:
        """Calculate average quality score for this tool."""
        if not self.quality_scores:
            return 0.0
        return sum(self.quality_scores) / len(self.quality_scores)


@dataclass
class CompressionAnalysisSummary:
    """High-level summary of compression analysis results."""

    analysis_id: str
    """Unique identifier for the analysis."""

    timestamp: datetime
    """When the analysis was performed."""

    total_conversations: int
    """Total number of conversations analyzed."""

    total_exchanges: int
    """Total number of exchanges analyzed."""

    compressor_summaries: Dict[str, Dict[str, Any]]
    """Summary statistics by compressor."""

    tool_summaries: Dict[str, ToolCompressionStats]
    """Summary statistics by tool type."""

    quality_analysis: Dict[str, CompressionQualityMetrics]
    """Quality analysis by compressor."""

    recommendations: List[str] = field(default_factory=list)
    """Recommendations based on the analysis."""

    @property
    def best_compressor_by_rate(self) -> Optional[str]:
        """Get the compressor with the best compression rate."""
        if not self.compressor_summaries:
            return None

        best_compressor = None
        best_rate = 0.0

        for compressor, stats in self.compressor_summaries.items():
            rate = stats.get("overall_compression_rate", 0.0)
            if rate > best_rate:
                best_rate = rate
                best_compressor = compressor

        return best_compressor

    @property
    def best_compressor_by_quality(self) -> Optional[str]:
        """Get the compressor with the best quality score."""
        if not self.quality_analysis:
            return None

        best_compressor = None
        best_score = 0.0

        for compressor, quality in self.quality_analysis.items():
            score = quality.overall_quality_score
            if score > best_score:
                best_score = score
                best_compressor = compressor

        return best_compressor
