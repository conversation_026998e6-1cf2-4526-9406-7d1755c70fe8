"""Placeholder-based compression strategy.

This module implements a compression strategy that replaces tool outputs
with placeholder messages containing summary information.
"""

import copy
from typing import List, Dict
from .chat_history_compression import ChatHistoryCompressor, CompressionStats
from .utils import (
    register_tool_uses,
    is_tool_output_node,
    should_compress_tool,
    find_tool_name_for_result,
    extract_tool_content_summary,
)
from base.prompt_format.common import Exchange, ChatRequestNode


class PlaceholderCompressor(ChatHistoryCompressor):
    """Replace tool outputs with placeholder messages."""

    def __init__(self):
        super().__init__()
        self.original_tokens = 0
        self.compressed_tokens = 0
        self.tool_compressions: Dict[str, int] = {}

    def compress(self, chat_history: List[Exchange]) -> List[Exchange]:
        """
        Compress the chat history by replacing tool outputs with placeholders.

        Args:
            chat_history: List of Exchange objects representing the conversation

        Returns:
            Compressed list of Exchange objects
        """
        if not chat_history:
            # Create empty stats for empty input
            stats = CompressionStats(
                original_tokens=0,
                compressed_tokens=0,
                compression_rate=0,
                exchanges_compressed=0,
                tool_outputs_compressed={},
            )
            self._update_stats(stats)
            return chat_history

        # Register tool uses for lookup
        register_tool_uses(chat_history)

        # Reset compression tracking
        self.original_tokens = 0
        self.compressed_tokens = 0
        self.tool_compressions = {}

        compressed_history = []
        for exchange in chat_history:
            compressed_exchange = self._compress_exchange(exchange)
            compressed_history.append(compressed_exchange)

        # Calculate and store stats
        compression_rate = (
            (self.original_tokens - self.compressed_tokens) / self.original_tokens
            if self.original_tokens > 0
            else 0
        )

        stats = CompressionStats(
            original_tokens=self.original_tokens,
            compressed_tokens=self.compressed_tokens,
            compression_rate=compression_rate,
            exchanges_compressed=len(compressed_history),
            tool_outputs_compressed=self.tool_compressions.copy(),
        )
        self._update_stats(stats)

        return compressed_history

    def _compress_exchange(self, exchange: Exchange) -> Exchange:
        """Compress a single exchange.

        Args:
            exchange: Exchange to compress

        Returns:
            Compressed exchange
        """
        # Compress request nodes (tool results)
        if isinstance(exchange.request_message, list):
            compressed_request_message = self._compress_request_nodes(
                exchange.request_message
            )
            # Create a new Exchange instance since the dataclass is frozen
            return Exchange(
                request_message=compressed_request_message,
                response_text=exchange.response_text,
                request_id=exchange.request_id,
            )
        else:
            # No compression needed, return the original exchange
            return exchange

    def _compress_request_nodes(
        self, nodes: List[ChatRequestNode]
    ) -> List[ChatRequestNode]:
        """Compress request nodes by replacing tool outputs with placeholders.

        Args:
            nodes: List of request nodes to compress

        Returns:
            List of compressed request nodes
        """
        compressed_nodes = []

        for node in nodes:
            if is_tool_output_node(node) and node.tool_result_node:
                # Find corresponding tool use to get tool name
                tool_name = find_tool_name_for_result(node.tool_result_node.tool_use_id)

                if tool_name and should_compress_tool(tool_name):
                    # Track original content length
                    original_content = node.tool_result_node.content
                    self.original_tokens += len(original_content.split())

                    # Create compressed node
                    compressed_node = self._create_placeholder_node(node, tool_name)
                    compressed_nodes.append(compressed_node)

                    # Track compressed content length
                    if compressed_node.tool_result_node:
                        compressed_content = compressed_node.tool_result_node.content
                        self.compressed_tokens += len(compressed_content.split())

                    # Track tool compression count
                    self.tool_compressions[tool_name] = (
                        self.tool_compressions.get(tool_name, 0) + 1
                    )
                else:
                    # Don't compress this tool output
                    if node.tool_result_node:
                        content_tokens = len(node.tool_result_node.content.split())
                        self.original_tokens += content_tokens
                        self.compressed_tokens += content_tokens
                    compressed_nodes.append(node)
            else:
                # Not a tool output node, keep as-is
                compressed_nodes.append(node)

        return compressed_nodes

    def _create_placeholder_node(
        self, node: ChatRequestNode, tool_name: str
    ) -> ChatRequestNode:
        """Create a placeholder node for a tool result.

        Args:
            node: Original tool result node
            tool_name: Name of the tool

        Returns:
            Compressed node with placeholder content
        """
        if not node.tool_result_node:
            return node

        # Extract summary information
        summary = extract_tool_content_summary(node)

        # Create placeholder text based on tool type and summary
        placeholder_parts = [f"[{tool_name}"]

        if summary.get("is_error"):
            placeholder_parts.append("ERROR")

        if "content_length" in summary:
            placeholder_parts.append(f"{summary['content_length']}c")

        if "line_count" in summary and summary["line_count"] > 1:
            placeholder_parts.append(f"{summary['line_count']}L")

        # Add tool-specific information (keep it short)
        if tool_name == "view" and "file_info" in summary:
            # Extract just the filename from the file_info
            file_info = summary["file_info"]
            if "on " in file_info:
                filename = file_info.split("on ")[-1].split(":")[0]
                placeholder_parts.append(filename)
        elif tool_name == "str-replace-editor":
            if summary.get("success"):
                placeholder_parts.append("OK")
        elif tool_name == "codebase-retrieval" and summary.get("sections_retrieved"):
            placeholder_parts.append("sections")
        elif tool_name == "grep-search" and "match_count" in summary:
            placeholder_parts.append(f"{summary['match_count']}m")

        placeholder_parts.append(f"#{node.tool_result_node.tool_use_id}]")
        placeholder_text = " ".join(placeholder_parts)

        # Create a new tool result node with compressed content
        from base.prompt_format.common import ChatRequestToolResult

        # Assert for type checker (we already checked this at the beginning)
        assert node.tool_result_node is not None

        compressed_tool_result = ChatRequestToolResult(
            tool_use_id=node.tool_result_node.tool_use_id,
            content=placeholder_text,
            is_error=node.tool_result_node.is_error,
            request_id=node.tool_result_node.request_id,
            content_nodes=None,
        )

        # Create a new ChatRequestNode with the compressed tool result
        from base.prompt_format.common import ChatRequestNode

        compressed_node = ChatRequestNode(
            id=node.id,
            type=node.type,
            text_node=node.text_node,
            tool_result_node=compressed_tool_result,
            image_node=node.image_node,
            ide_state_node=node.ide_state_node,
            edit_events_node=node.edit_events_node,
        )

        return compressed_node
