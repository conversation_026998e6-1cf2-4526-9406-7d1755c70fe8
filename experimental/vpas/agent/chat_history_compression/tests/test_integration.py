"""Integration tests for the complete compression pipeline."""

import pytest
import tempfile
import pickle
from pathlib import Path
from unittest.mock import MagicMock, patch

from experimental.vpas.agent.chat_history_compression.placeholder_compressor import (
    PlaceholderCompressor,
)
from experimental.vpas.agent.chat_history_compression.gemini_flash_compressor import (
    GeminiFlashCompressor,
)
from experimental.vpas.agent.chat_history_compression.evaluation_pipeline import (
    CompressionEvaluationPipeline,
)
from experimental.vpas.agent.chat_history_compression.html_report.report_generator import (
    CompressionReportGenerator,
)
from experimental.vpas.agent.chat_history_compression.tests.test_utils import (
    create_test_conversation,
)


class TestCompressionPipelineIntegration:
    """Integration tests for the complete compression pipeline."""

    def test_end_to_end_placeholder_compression(self):
        """Test complete pipeline with PlaceholderCompressor."""
        # Create test data
        conversation = create_test_conversation()

        # Initialize compressor
        compressor = PlaceholderCompressor()

        # Initialize evaluation pipeline
        pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

        # Run compression and evaluation
        result = pipeline.evaluate_single_conversation(
            original_exchanges=conversation,
            compressed_exchanges=compressor.compress(conversation),
            compressor_name="PlaceholderCompressor",
            conversation_id="test_conv_1",
        )

        # Verify results
        assert result is not None
        assert result.conversation_id == "test_conv_1"
        assert result.compressor_name == "PlaceholderCompressor"
        assert result.total_original_tokens > 0
        assert result.total_compressed_tokens > 0
        assert result.compression_rate > 0
        assert result.exchanges_count == len(conversation)

    @patch(
        "experimental.vpas.agent.chat_history_compression.gemini_flash_compressor.GoogleGenaiClient"
    )
    def test_end_to_end_gemini_compression(self, mock_client_class):
        """Test complete pipeline with GeminiFlashCompressor."""
        # Mock the Gemini client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        mock_response = MagicMock()
        mock_response.text = "Compressed content summary"
        mock_client.generate_response_stream.return_value = [mock_response]

        # Create test data
        conversation = create_test_conversation()

        # Initialize compressor
        compressor = GeminiFlashCompressor(client=mock_client)

        # Initialize evaluation pipeline
        pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

        # Run compression and evaluation
        result = pipeline.evaluate_single_conversation(
            original_exchanges=conversation,
            compressed_exchanges=compressor.compress(conversation),
            compressor_name="GeminiFlashCompressor",
            conversation_id="test_conv_2",
        )

        # Verify results
        assert result is not None
        assert result.conversation_id == "test_conv_2"
        assert result.compressor_name == "GeminiFlashCompressor"
        assert result.total_original_tokens > 0
        assert result.total_compressed_tokens > 0

    def test_benchmark_multiple_compressors(self):
        """Test benchmarking multiple compression strategies."""
        # Create test conversations
        conversations = [create_test_conversation() for _ in range(3)]
        conversation_ids = [f"conv_{i}" for i in range(3)]

        # Initialize compressors
        placeholder_compressor = PlaceholderCompressor()

        # Mock Gemini compressor
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_response.text = "LLM compressed content"
        mock_client.generate_response_stream.return_value = [mock_response]
        gemini_compressor = GeminiFlashCompressor(client=mock_client)

        compressors = [placeholder_compressor, gemini_compressor]

        # Initialize evaluation pipeline
        pipeline = CompressionEvaluationPipeline(
            enable_quality_evaluation=False, max_workers=2
        )

        # Run benchmark
        benchmark_result = pipeline.benchmark_compressors(
            compressors=compressors,
            test_conversations=conversations,
            conversation_ids=conversation_ids,
            benchmark_id="test_benchmark",
        )

        # Verify benchmark results
        assert benchmark_result is not None
        assert benchmark_result.benchmark_id == "test_benchmark"
        assert len(benchmark_result.compressor_results) == 2
        assert "PlaceholderCompressor" in benchmark_result.compressor_results
        assert "GeminiFlashCompressor" in benchmark_result.compressor_results
        assert benchmark_result.total_conversations == 3

        # Verify each compressor has results for all conversations
        for compressor_name, results in benchmark_result.compressor_results.items():
            assert len(results) == 3
            for result in results:
                assert result.compressor_name == compressor_name
                assert result.total_original_tokens > 0

    def test_analysis_pipeline(self):
        """Test the complete analysis pipeline."""
        # Create test evaluation results
        conversations = [create_test_conversation() for _ in range(2)]

        placeholder_compressor = PlaceholderCompressor()
        pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

        eval_results = []
        for i, conversation in enumerate(conversations):
            compressed = placeholder_compressor.compress(conversation)
            result = pipeline.evaluate_single_conversation(
                original_exchanges=conversation,
                compressed_exchanges=compressed,
                compressor_name="PlaceholderCompressor",
                conversation_id=f"conv_{i}",
            )
            eval_results.append(result)

        # Run analysis
        analysis_summary = pipeline.analyze_compression_results(
            eval_results=eval_results, analysis_id="test_analysis"
        )

        # Verify analysis results
        assert analysis_summary is not None
        assert analysis_summary.analysis_id == "test_analysis"
        assert analysis_summary.total_conversations == 2
        assert "PlaceholderCompressor" in analysis_summary.compressor_summaries
        assert len(analysis_summary.recommendations) > 0

    def test_report_generation(self):
        """Test HTML report generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test data
            conversation = create_test_conversation()
            compressor = PlaceholderCompressor()
            pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

            # Generate evaluation result
            eval_result = pipeline.evaluate_single_conversation(
                original_exchanges=conversation,
                compressed_exchanges=compressor.compress(conversation),
                compressor_name="PlaceholderCompressor",
                conversation_id="test_conv",
            )

            # Generate analysis summary
            analysis_summary = pipeline.analyze_compression_results(
                eval_results=[eval_result], analysis_id="test_analysis"
            )

            # Initialize report generator
            report_generator = CompressionReportGenerator(output_dir=temp_dir)

            # Generate summary report
            summary_report_path = report_generator.generate_summary_report(
                analysis_summary=analysis_summary, filename="test_summary.html"
            )

            # Verify report was created
            assert Path(summary_report_path).exists()

            # Read and verify report content
            with open(summary_report_path, "r") as f:
                content = f.read()
                assert "Compression Analysis Summary" in content
                assert "test_analysis" in content
                assert "PlaceholderCompressor" in content

    def test_data_persistence(self):
        """Test saving and loading compression results."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test data
            conversation = create_test_conversation()
            compressor = PlaceholderCompressor()
            pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

            # Generate results
            eval_result = pipeline.evaluate_single_conversation(
                original_exchanges=conversation,
                compressed_exchanges=compressor.compress(conversation),
                compressor_name="PlaceholderCompressor",
                conversation_id="test_conv",
            )

            # Save results
            results_file = Path(temp_dir) / "test_results.pkl"
            with open(results_file, "wb") as f:
                pickle.dump([eval_result], f)

            # Load results
            with open(results_file, "rb") as f:
                loaded_results = pickle.load(f)

            # Verify loaded results
            assert len(loaded_results) == 1
            loaded_result = loaded_results[0]
            assert loaded_result.conversation_id == "test_conv"
            assert loaded_result.compressor_name == "PlaceholderCompressor"
            assert (
                loaded_result.total_original_tokens == eval_result.total_original_tokens
            )

    def test_error_handling(self):
        """Test error handling in the pipeline."""
        # Test with empty conversation
        pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

        result = pipeline.evaluate_single_conversation(
            original_exchanges=[],
            compressed_exchanges=[],
            compressor_name="PlaceholderCompressor",
            conversation_id="empty_conv",
        )

        # Should handle empty conversations gracefully
        assert result is not None
        assert result.total_original_tokens == 0
        assert result.total_compressed_tokens == 0
        assert result.compression_rate == 0

    def test_performance_metrics(self):
        """Test that performance metrics are captured."""
        conversation = create_test_conversation()
        compressor = PlaceholderCompressor()
        pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

        result = pipeline.evaluate_single_conversation(
            original_exchanges=conversation,
            compressed_exchanges=compressor.compress(conversation),
            compressor_name="PlaceholderCompressor",
            conversation_id="perf_test",
        )

        # Verify timing metrics are captured
        assert result.compression_time_seconds >= 0
        assert result.evaluation_time_seconds >= 0

        # Verify compression stats are available
        compressor_stats = compressor.get_stats()
        assert compressor_stats is not None
        assert compressor_stats.original_tokens > 0
        assert compressor_stats.compressed_tokens >= 0

    def test_tool_specific_analysis(self):
        """Test tool-specific compression analysis."""
        conversation = create_test_conversation()
        compressor = PlaceholderCompressor()
        pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)

        result = pipeline.evaluate_single_conversation(
            original_exchanges=conversation,
            compressed_exchanges=compressor.compress(conversation),
            compressor_name="PlaceholderCompressor",
            conversation_id="tool_test",
        )

        # Verify tool-specific stats are captured
        assert len(result.tool_compression_stats) > 0

        # Check that view tool stats are present (from test conversation)
        assert "view" in result.tool_compression_stats
        view_stats = result.tool_compression_stats["view"]
        assert view_stats["count"] > 0
        assert view_stats["total_original_tokens"] > 0
