"""Tests for the utils module."""

import pytest
from experimental.vpas.agent.chat_history_compression.utils import (
    register_tool_uses,
    is_tool_output_node,
    is_tool_use_node,
    get_tool_name_from_use,
    find_tool_name_for_result,
    should_compress_tool,
    extract_tool_content_summary,
    get_exchange_tool_uses,
    get_exchange_tool_results,
)
from experimental.vpas.agent.chat_history_compression.tests.test_utils import (
    create_test_exchange_with_tool_result,
    create_test_exchange_with_tool_use,
    create_test_conversation,
)
from base.prompt_format.common import ChatRequestNodeType, ChatResultNodeType


class TestUtilsFunctions:
    """Test cases for utility functions."""

    def test_should_compress_tool(self):
        """Test tool compression decision logic."""
        # Compressible tools
        assert should_compress_tool("view")
        assert should_compress_tool("codebase-retrieval")
        assert should_compress_tool("str-replace-editor")
        assert should_compress_tool("launch-process")
        assert should_compress_tool("save-file")
        assert should_compress_tool("grep-search")

        # Non-compressible tools
        assert not should_compress_tool("unknown-tool")
        assert not should_compress_tool("linear")
        assert not should_compress_tool("github-api")

    def test_is_tool_output_node(self):
        """Test tool output node detection."""
        # Create a tool result exchange
        exchange = create_test_exchange_with_tool_result("view", "file content")

        for node in exchange.request_nodes:
            if node.type == ChatRequestNodeType.TOOL_RESULT:
                assert is_tool_output_node(node)
            else:
                assert not is_tool_output_node(node)

    def test_is_tool_use_node(self):
        """Test tool use node detection."""
        # Create a tool use exchange
        exchange = create_test_exchange_with_tool_use("view", {"path": "test.py"})

        for node in exchange.response_nodes:
            if node.type == ChatResultNodeType.TOOL_USE:
                assert is_tool_use_node(node)
            else:
                assert not is_tool_use_node(node)

    def test_get_tool_name_from_use(self):
        """Test extracting tool name from tool use node."""
        exchange = create_test_exchange_with_tool_use("view", {"path": "test.py"})

        for node in exchange.response_nodes:
            if is_tool_use_node(node):
                tool_name = get_tool_name_from_use(node)
                assert tool_name == "view"

    def test_register_and_find_tool_uses(self):
        """Test tool use registry functionality."""
        # Create exchanges with tool uses
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py"}, "tool_123"
        )

        exchanges = [tool_use_exchange]
        register_tool_uses(exchanges)

        # Test finding tool name
        tool_name = find_tool_name_for_result("tool_123")
        assert tool_name == "view"

        # Test non-existent tool use ID
        assert find_tool_name_for_result("nonexistent") is None

    def test_extract_tool_content_summary(self):
        """Test tool content summary extraction."""
        # Test view tool
        view_content = "Here's the result of running `cat -n` on test.py:\n     1\tprint('hello')\n"
        exchange = create_test_exchange_with_tool_result(
            "view", view_content, "tool_123"
        )

        # Register the tool use first
        tool_use_exchange = create_test_exchange_with_tool_use("view", {}, "tool_123")
        register_tool_uses([tool_use_exchange])

        for node in exchange.request_nodes:
            if is_tool_output_node(node):
                summary = extract_tool_content_summary(node)
                assert summary["tool_name"] == "view"
                assert summary["content_length"] == len(view_content)
                assert summary["line_count"] == 3
                assert "file_info" in summary

    def test_get_exchange_tool_uses(self):
        """Test getting tool uses from an exchange."""
        exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py"}, "tool_123"
        )

        tool_uses = get_exchange_tool_uses(exchange)
        assert len(tool_uses) == 1
        assert tool_uses[0] == ("view", "tool_123")

    def test_get_exchange_tool_results(self):
        """Test getting tool results from an exchange."""
        # Create tool use first to register it
        tool_use_exchange = create_test_exchange_with_tool_use("view", {}, "tool_123")
        register_tool_uses([tool_use_exchange])

        # Create tool result exchange
        exchange = create_test_exchange_with_tool_result("view", "content", "tool_123")

        tool_results = get_exchange_tool_results(exchange)
        assert len(tool_results) == 1
        assert tool_results[0] == ("view", "tool_123")

    def test_conversation_workflow(self):
        """Test the complete workflow with a conversation."""
        conversation = create_test_conversation()

        # Register all tool uses
        register_tool_uses(conversation)

        # Verify we can find tool names for results
        for exchange in conversation:
            for node in exchange.request_nodes:
                if is_tool_output_node(node) and node.tool_result_node:
                    tool_name = find_tool_name_for_result(
                        node.tool_result_node.tool_use_id
                    )
                    if (
                        tool_name
                    ):  # Some exchanges might not have corresponding tool uses
                        assert tool_name in [
                            "view",
                            "codebase-retrieval",
                            "str-replace-editor",
                        ]
