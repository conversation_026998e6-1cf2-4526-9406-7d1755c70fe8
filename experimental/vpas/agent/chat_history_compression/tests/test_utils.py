"""Test utilities for chat history compression tests."""

from typing import List
from base.prompt_format.common import (
    Exchange,
    ChatRequestNode,
    ChatResultNode,
    ChatRequestNodeType,
    ChatResultNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultToolUse,
)


def create_test_exchange_with_text(
    user_text: str, agent_text: str, request_id: str = "test_request_id"
) -> Exchange:
    """Create a simple test exchange with text messages.

    Args:
        user_text: User message text
        agent_text: Agent response text
        request_id: Request ID for the exchange

    Returns:
        Exchange object with text nodes
    """
    # Create request node with text
    request_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=user_text),
        tool_result_node=None,
    )

    # Create response node with text
    response_node = ChatResultNode(
        id=1, type=ChatResultNodeType.RAW_RESPONSE, content=agent_text, tool_use=None
    )

    return Exchange(
        request_message=[request_node],
        response_text=[response_node],
        request_id=request_id,
    )


def create_test_exchange_with_tool_result(
    tool_name: str,
    tool_content: str,
    tool_use_id: str = "test_tool_use_id",
    is_error: bool = False,
    request_id: str = "test_request_id",
) -> Exchange:
    """Create a test exchange with a tool result.

    Args:
        tool_name: Name of the tool
        tool_content: Content of the tool result
        tool_use_id: Tool use ID
        is_error: Whether the tool result is an error
        request_id: Request ID for the exchange

    Returns:
        Exchange object with tool result
    """
    # Create tool result node
    tool_result_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id=tool_use_id,
            content=tool_content,
            is_error=is_error,
            request_id=request_id,
        ),
    )

    # Create response node (empty for this test)
    response_node = ChatResultNode(
        id=1, type=ChatResultNodeType.RAW_RESPONSE, content="", tool_use=None
    )

    return Exchange(
        request_message=[tool_result_node],
        response_text=[response_node],
        request_id=request_id,
    )


def create_test_exchange_with_tool_use(
    tool_name: str,
    tool_input: dict,
    tool_use_id: str = "test_tool_use_id",
    request_id: str = "test_request_id",
) -> Exchange:
    """Create a test exchange with a tool use.

    Args:
        tool_name: Name of the tool
        tool_input: Input parameters for the tool
        tool_use_id: Tool use ID
        request_id: Request ID for the exchange

    Returns:
        Exchange object with tool use
    """
    # Create request node (empty for this test)
    request_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=""),
        tool_result_node=None,
    )

    # Create tool use node
    tool_use_node = ChatResultNode(
        id=1,
        type=ChatResultNodeType.TOOL_USE,
        content="",
        tool_use=ChatResultToolUse(
            name=tool_name, input=tool_input, tool_use_id=tool_use_id
        ),
    )

    return Exchange(
        request_message=[request_node],
        response_text=[tool_use_node],
        request_id=request_id,
    )


def create_test_conversation() -> List[Exchange]:
    """Create a test conversation with multiple exchanges.

    Returns:
        List of Exchange objects representing a conversation
    """
    exchanges = []

    # Exchange 1: User asks a question
    exchanges.append(
        create_test_exchange_with_text(
            user_text="Can you show me the contents of file.py?",
            agent_text="I'll help you view the file contents.",
            request_id="req_1",
        )
    )

    # Exchange 2: Tool use for viewing file
    tool_use_exchange = create_test_exchange_with_tool_use(
        tool_name="view",
        tool_input={"path": "file.py", "type": "file"},
        tool_use_id="tool_use_1",
        request_id="req_2",
    )
    exchanges.append(tool_use_exchange)

    # Exchange 3: Tool result with file contents
    tool_result_exchange = create_test_exchange_with_tool_result(
        tool_name="view",
        tool_content="Here's the result of running `cat -n` on file.py:\n     1\tdef hello():\n     2\t    print('Hello, world!')\n",
        tool_use_id="tool_use_1",
        request_id="req_3",
    )
    exchanges.append(tool_result_exchange)

    # Exchange 4: Agent response with analysis
    exchanges.append(
        create_test_exchange_with_text(
            user_text="",
            agent_text="I can see the file contains a simple hello function that prints 'Hello, world!'.",
            request_id="req_4",
        )
    )

    return exchanges


def assert_exchange_structure(exchange: Exchange) -> None:
    """Assert that an exchange has the expected structure.

    Args:
        exchange: Exchange to validate

    Raises:
        AssertionError: If the exchange structure is invalid
    """
    assert exchange.request_id is not None, "Exchange must have a request_id"
    assert exchange.request_message is not None, "Exchange must have a request_message"
    assert exchange.response_text is not None, "Exchange must have a response_text"

    # Validate request nodes if present
    if isinstance(exchange.request_message, list):
        for node in exchange.request_message:
            assert isinstance(
                node, ChatRequestNode
            ), "Request nodes must be ChatRequestNode instances"
            assert node.type in ChatRequestNodeType, "Request node must have valid type"

    # Validate response nodes if present
    if isinstance(exchange.response_text, list):
        for node in exchange.response_text:
            assert isinstance(
                node, ChatResultNode
            ), "Response nodes must be ChatResultNode instances"
            assert node.type in ChatResultNodeType, "Response node must have valid type"
