"""Tests for compression strategies."""

import pytest
from unittest.mock import MagicMock, patch
from experimental.vpas.agent.chat_history_compression.placeholder_compressor import (
    PlaceholderCompressor,
)
from experimental.vpas.agent.chat_history_compression.gemini_flash_compressor import (
    GeminiFlashCompressor,
)
from experimental.vpas.agent.chat_history_compression.utils import register_tool_uses
from experimental.vpas.agent.chat_history_compression.tests.test_utils import (
    create_test_exchange_with_tool_result,
    create_test_exchange_with_tool_use,
    create_test_conversation,
    assert_exchange_structure,
)


class TestPlaceholderCompressor:
    """Test cases for PlaceholderCompressor."""

    def test_compress_empty_history(self):
        """Test compression of empty chat history."""
        compressor = PlaceholderCompressor()
        result = compressor.compress([])
        assert result == []
        assert compressor.get_stats() is not None

    def test_compress_view_tool_output(self):
        """Test compression of view tool output."""
        # Create tool use first
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py", "type": "file"}, "tool_123"
        )

        # Create tool result
        view_content = "Here's the result of running `cat -n` on test.py:\n     1\tdef hello():\n     2\t    print('Hello, world!')\n"
        tool_result_exchange = create_test_exchange_with_tool_result(
            "view", view_content, "tool_123"
        )

        exchanges = [tool_use_exchange, tool_result_exchange]
        compressor = PlaceholderCompressor()

        compressed = compressor.compress(exchanges)

        assert len(compressed) == 2

        # Check that tool result was compressed
        compressed_result = compressed[1]
        assert_exchange_structure(compressed_result)

        for node in compressed_result.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                assert "[view" in content
                assert "#tool_123" in content
                assert len(content) < len(view_content)

    def test_preserve_non_compressible_tools(self):
        """Test that non-compressible tool outputs are preserved."""
        # Create tool use first
        tool_use_exchange = create_test_exchange_with_tool_use(
            "linear", {"query": "test"}, "tool_456"
        )

        # Create tool result
        linear_content = "Linear ticket information..."
        tool_result_exchange = create_test_exchange_with_tool_result(
            "linear", linear_content, "tool_456"
        )

        exchanges = [tool_use_exchange, tool_result_exchange]
        compressor = PlaceholderCompressor()

        compressed = compressor.compress(exchanges)

        # Check that linear tool result was NOT compressed
        compressed_result = compressed[1]
        for node in compressed_result.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                assert content == linear_content  # Should be unchanged

    def test_compression_stats(self):
        """Test that compression statistics are calculated correctly."""
        # Create a conversation with compressible tools
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py"}, "tool_123"
        )

        view_content = "Here's a long file content that should be compressed..."
        tool_result_exchange = create_test_exchange_with_tool_result(
            "view", view_content, "tool_123"
        )

        exchanges = [tool_use_exchange, tool_result_exchange]
        compressor = PlaceholderCompressor()

        compressor.compress(exchanges)
        stats = compressor.get_stats()

        assert stats is not None
        assert stats.original_tokens > 0
        assert stats.compressed_tokens > 0
        assert stats.compressed_tokens < stats.original_tokens
        assert stats.compression_rate > 0
        assert stats.exchanges_compressed == 2
        assert "view" in stats.tool_outputs_compressed
        assert stats.tool_outputs_compressed["view"] == 1

    def test_error_tool_results(self):
        """Test compression of error tool results."""
        # Create tool use first
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "nonexistent.py"}, "tool_error"
        )

        # Create error tool result
        error_content = "Error: File not found: nonexistent.py"
        tool_result_exchange = create_test_exchange_with_tool_result(
            "view", error_content, "tool_error", is_error=True
        )

        exchanges = [tool_use_exchange, tool_result_exchange]
        compressor = PlaceholderCompressor()

        compressed = compressor.compress(exchanges)

        # Check that error information is preserved in placeholder
        compressed_result = compressed[1]
        for node in compressed_result.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                assert "ERROR" in content
                assert "tool_error" in content


class TestGeminiFlashCompressor:
    """Test cases for GeminiFlashCompressor."""

    @patch(
        "experimental.vpas.agent.chat_history_compression.gemini_flash_compressor.GoogleGenaiClient"
    )
    def test_compress_with_mock_client(self, mock_client_class):
        """Test compression with mocked Gemini client."""
        # Mock the client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock the response
        mock_response = MagicMock()
        mock_response.text = "Compressed tool output summary"
        mock_client.generate_response_stream.return_value = [mock_response]

        # Create test data
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py"}, "tool_123"
        )

        long_content = (
            "Here's a very long file content that should be compressed by the LLM..."
            * 10
        )
        tool_result_exchange = create_test_exchange_with_tool_result(
            "view", long_content, "tool_123"
        )

        exchanges = [tool_use_exchange, tool_result_exchange]

        # Test compression
        compressor = GeminiFlashCompressor(client=mock_client)
        compressed = compressor.compress(exchanges)

        assert len(compressed) == 2

        # Verify LLM was called
        mock_client.generate_response_stream.assert_called()

        # Check compression result
        compressed_result = compressed[1]
        for node in compressed_result.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                assert content == "Compressed tool output summary"

    @patch(
        "experimental.vpas.agent.chat_history_compression.gemini_flash_compressor.GoogleGenaiClient"
    )
    def test_fallback_on_llm_failure(self, mock_client_class):
        """Test fallback to original content when LLM fails."""
        # Mock the client to raise an exception
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client
        mock_client.generate_response_stream.side_effect = Exception("LLM failed")

        # Create test data
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py"}, "tool_123"
        )

        original_content = "Original content that should be preserved on LLM failure"
        tool_result_exchange = create_test_exchange_with_tool_result(
            "view", original_content, "tool_123"
        )

        exchanges = [tool_use_exchange, tool_result_exchange]

        # Test compression
        compressor = GeminiFlashCompressor(client=mock_client)
        compressed = compressor.compress(exchanges)

        # Check that original content is preserved
        compressed_result = compressed[1]
        for node in compressed_result.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                assert content == original_content

    def test_skip_short_content(self):
        """Test that short content is not sent to LLM."""
        # Create test data with short content
        tool_use_exchange = create_test_exchange_with_tool_use(
            "view", {"path": "test.py"}, "tool_123"
        )

        short_content = "Short content"  # Less than 200 chars
        tool_result_exchange = create_test_exchange_with_tool_result(
            "view", short_content, "tool_123"
        )

        exchanges = [tool_use_exchange, tool_result_exchange]

        # Mock client that should not be called
        mock_client = MagicMock()

        compressor = GeminiFlashCompressor(client=mock_client)
        compressed = compressor.compress(exchanges)

        # Verify LLM was not called for short content
        mock_client.generate_response_stream.assert_not_called()

        # Check that content is unchanged
        compressed_result = compressed[1]
        for node in compressed_result.request_nodes:
            if hasattr(node, "tool_result_node") and node.tool_result_node:
                content = node.tool_result_node.content
                assert content == short_content

    def test_conversation_context_building(self):
        """Test that conversation context is built correctly."""
        conversation = create_test_conversation()

        mock_client = MagicMock()
        compressor = GeminiFlashCompressor(client=mock_client)

        context = compressor._build_conversation_context(conversation)

        assert isinstance(context, str)
        assert len(context) > 0
        # Should contain user and agent messages
        assert "User:" in context or "Agent:" in context


class TestCompressionIntegration:
    """Integration tests for compression strategies."""

    def test_both_compressors_on_same_data(self):
        """Test both compressors on the same conversation."""
        # Create a test conversation
        conversation = create_test_conversation()

        # Test PlaceholderCompressor
        placeholder_compressor = PlaceholderCompressor()
        placeholder_result = placeholder_compressor.compress(conversation)
        placeholder_stats = placeholder_compressor.get_stats()

        # Test GeminiFlashCompressor with mock
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_response.text = "LLM compressed content"
        mock_client.generate_response_stream.return_value = [mock_response]

        gemini_compressor = GeminiFlashCompressor(client=mock_client)
        gemini_result = gemini_compressor.compress(conversation)
        gemini_stats = gemini_compressor.get_stats()

        # Both should return same number of exchanges
        assert len(placeholder_result) == len(gemini_result) == len(conversation)

        # Both should have valid stats
        assert placeholder_stats is not None
        assert gemini_stats is not None

        # Both should preserve exchange structure
        for exchange in placeholder_result:
            assert_exchange_structure(exchange)

        for exchange in gemini_result:
            assert_exchange_structure(exchange)
