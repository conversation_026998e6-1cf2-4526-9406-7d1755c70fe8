#!/usr/bin/env python3
"""Main script for running chat history compression analysis.

This script fetches agent conversations from BigQuery, applies compression
strategies, and generates evaluation reports.
"""

import argparse
import logging
import pickle
import random
import string
import sys
import time
import uuid
from datetime import datetime, timedelta
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import List, Optional
from tqdm import tqdm

from base.datasets.tenants import get_tenant
from experimental.vpas.agent.analytics.big_query_utils import (
    get_agent_conv_last_request_ids,
)
from experimental.vpas.agent.analytics.conversation import Conversation
from experimental.vpas.utils.ri_utils import (
    get_chat_host_request_factory,
    get_chat_host_response_factory,
)

from .placeholder_compressor import PlaceholderCompressor
from .gemini_flash_compressor import GeminiFlashCompressor
from .utils import register_tool_uses
from .chat_history_compression import ChatHistoryCompressor
from .evaluation_pipeline import CompressionEvaluationPipeline
from .eval_types import (
    ChatHistoryCompressionEvalResult,
    ExchangeCompressionResult,
    ToolCompressionResult,
)
from .html_report.react_report_generator import save_react_compression_html_report

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")


def conversation_to_exchanges(conversation: Conversation):
    """Convert a Conversation object to a list of Exchange objects.

    Args:
        conversation: Conversation object from analytics

    Returns:
        List of Exchange objects compatible with compression
    """
    from base.prompt_format.common import Exchange

    exchanges = []

    for round_obj in conversation.agent_rounds:
        for turn in round_obj.agent_turns:
            # Get the chat request and response
            request = turn.request

            # Create exchange from the request/response data
            if hasattr(request, "chat_history") and request.chat_history:
                # Use the chat history from the request
                for hist_exchange in request.chat_history:
                    exchange = Exchange(
                        request_message=hist_exchange.request_message,
                        response_text=hist_exchange.response_text,
                        request_id=hist_exchange.request_id,
                    )
                    exchanges.append(exchange)

    return exchanges


def process_conversation(
    request_id: str,
    compressor: ChatHistoryCompressor,
    tenant_name: str = "dogfood-shard",
    min_chat_history_length: int = 100,
) -> Optional[dict]:
    """Process a single conversation with compression.

    Args:
        request_id: The request ID to process
        compressor: The compression strategy to use
        tenant_name: Tenant name for data access
        min_chat_history_length: Minimum number of exchanges required to process conversation

    Returns:
        Dictionary with compression results or None if failed
    """
    try:
        # Get the conversation
        tenant = get_tenant(tenant_name)
        get_chat_host_request = get_chat_host_request_factory(tenant.name)
        get_chat_host_response = get_chat_host_response_factory(tenant.name)

        chat_request = get_chat_host_request(request_id)
        chat_response = get_chat_host_response(request_id)  # type: ignore

        if not chat_request or not chat_response:
            logger.warning(f"Could not fetch request/response for {request_id}")
            return None

        # Create a wrapper function that handles None case for Conversation.from_chat_request
        def get_chat_host_request_wrapper(req_id: str):
            result = get_chat_host_request(req_id)
            if result is None:
                raise ValueError(f"Could not fetch chat request for {req_id}")
            return result

        # Create conversation object
        conversation = Conversation.from_chat_request(
            request_id, chat_request, chat_response, get_chat_host_request_wrapper
        )

        # Convert to exchanges
        exchanges = conversation_to_exchanges(conversation)

        if not exchanges:
            logger.warning(f"No exchanges found for conversation {request_id}")
            return None

        # Filter by chat history length
        if len(exchanges) < min_chat_history_length:
            logger.debug(
                f"Skipping conversation {request_id}: chat history length {len(exchanges)} < {min_chat_history_length}"
            )
            return None

        # Register tool uses for compression
        register_tool_uses(exchanges)

        # Compress the conversation
        compressed_exchanges = compressor.compress(exchanges)
        stats = compressor.get_stats()

        return {
            "request_id": request_id,
            "original_exchanges": exchanges,
            "compressed_exchanges": compressed_exchanges,
            "stats": stats,
            "compressor_name": compressor.get_name(),
        }

    except Exception as e:
        # print stack trace
        import traceback

        traceback.print_exc()
        logger.error(f"Error processing conversation {request_id}: {e}")
        return None


def convert_to_eval_results(
    all_results: dict,
) -> List[ChatHistoryCompressionEvalResult]:
    """Convert raw compression results to evaluation results format.

    Args:
        all_results: Dictionary of compression results by compressor name

    Returns:
        List of ChatHistoryCompressionEvalResult objects
    """
    eval_results = []

    for compressor_name, results in all_results.items():
        for result in results:
            if not result:
                continue

            # Create exchange results from the compression stats
            exchange_results = []
            stats = result.get("stats")
            if stats and hasattr(stats, "tool_outputs_compressed"):
                # Create a single exchange result for this conversation
                tool_compressions = []
                for tool_name, tool_count in stats.tool_outputs_compressed.items():
                    tool_compression = ToolCompressionResult(
                        tool_name=tool_name,
                        tool_use_id=f"{result['request_id']}_{tool_name}",
                        original_tokens=0,  # Not available in basic compression stats
                        compressed_tokens=0,  # Not available in basic compression stats
                        compression_method="placeholder",
                        preserved_info={"compression_count": tool_count},
                    )
                    tool_compressions.append(tool_compression)

                # Create dummy exchanges for the result (we don't have the actual exchanges here)
                from base.prompt_format.common import Exchange

                dummy_original = Exchange(
                    request_message="Original conversation",
                    response_text="Original response",
                    request_id=result["request_id"],
                )
                dummy_compressed = Exchange(
                    request_message="Compressed conversation",
                    response_text="Compressed response",
                    request_id=result["request_id"],
                )

                exchange_result = ExchangeCompressionResult(
                    request_id=result["request_id"],
                    original_exchange=dummy_original,
                    compressed_exchange=dummy_compressed,
                    tool_compressions=tool_compressions,
                    original_tokens=stats.original_tokens,
                    compressed_tokens=stats.compressed_tokens,
                )
                exchange_results.append(exchange_result)

            # Calculate compression rate
            original_tokens = stats.original_tokens if stats else 0
            compressed_tokens = stats.compressed_tokens if stats else 0
            compression_rate = (
                (original_tokens - compressed_tokens) / original_tokens
                if original_tokens > 0
                else 0.0
            )

            # Create the evaluation result
            eval_result = ChatHistoryCompressionEvalResult(
                conversation_id=result["request_id"],
                timestamp=datetime.now(),
                compressor_name=compressor_name,
                exchange_results=exchange_results,
                total_original_tokens=original_tokens,
                total_compressed_tokens=compressed_tokens,
                compression_rate=compression_rate,
                tool_compression_stats=stats.tool_outputs_compressed if stats else {},
                llm_judge_score=None,  # Not available in basic compression
                llm_judge_feedback=None,
                information_retention_score=None,
                compression_time_seconds=0.0,  # Not tracked in current implementation
                evaluation_time_seconds=0.0,
            )

            eval_results.append(eval_result)

    return eval_results


def main():
    """Main function for the compression runner."""
    parser = argparse.ArgumentParser(
        description="Run chat history compression analysis"
    )

    # Time range arguments
    parser.add_argument("--last-hours", type=int, help="Process data from last N hours")
    parser.add_argument("--last-days", type=int, help="Process data from last N days")
    parser.add_argument(
        "--from-date", help="Start date in ISO format (YYYY-MM-DDTHH:MM:SS)"
    )
    parser.add_argument(
        "--to-date", help="End date in ISO format (YYYY-MM-DDTHH:MM:SS)"
    )

    # Processing arguments
    parser.add_argument(
        "--limit", type=int, default=100, help="Max conversations to process"
    )
    parser.add_argument(
        "--min-chat-history-length",
        type=int,
        default=100,
        help="Minimum chat history length (number of exchanges) to include conversations (default: 100)",
    )
    parser.add_argument(
        "--compressor",
        choices=["placeholder", "gemini", "all"],
        default="placeholder",
        help="Which compressor to use",
    )
    parser.add_argument(
        "--thread-count", type=int, default=20, help="Number of threads"
    )
    parser.add_argument("--tenant", default="dogfood-shard", help="Tenant name")

    # Output arguments
    parser.add_argument(
        "--output-dir",
        default="/mnt/efs/augment/user/vpas/chat_history_compression/",
        help="Directory for output files",
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Set up date range
    if args.last_hours:
        to_date = datetime.now()
        from_date = to_date - timedelta(hours=args.last_hours)
    elif args.last_days:
        to_date = datetime.now()
        from_date = to_date - timedelta(days=args.last_days)
    else:
        from_date = datetime.fromisoformat(args.from_date) if args.from_date else None
        to_date = datetime.fromisoformat(args.to_date) if args.to_date else None

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Get conversations
    logger.info(f"Fetching conversations from {from_date} to {to_date}")
    tenant = get_tenant(args.tenant)
    request_ids = get_agent_conv_last_request_ids(
        tenant=tenant,
        from_datetime=from_date,
        to_datetime=to_date,
    )

    if args.limit:
        request_ids = request_ids[: args.limit]

    logger.info(f"Found {len(request_ids)} conversations to process")

    # Set up compressors
    compressors: List[ChatHistoryCompressor] = []
    if args.compressor in ["placeholder", "all"]:
        compressors.append(PlaceholderCompressor())
    if args.compressor in ["gemini", "all"]:
        compressors.append(GeminiFlashCompressor())

    # Process conversations
    all_results = {compressor.get_name(): [] for compressor in compressors}

    for compressor in compressors:
        logger.info(f"Running {compressor.get_name()} compression")

        with ThreadPoolExecutor(max_workers=args.thread_count) as executor:
            # Submit all tasks
            futures = []
            for request_id in request_ids:
                future = executor.submit(
                    process_conversation,
                    request_id,
                    compressor,
                    args.tenant,
                    args.min_chat_history_length,
                )
                futures.append(future)

            # Collect results with progress bar
            for future in tqdm(
                as_completed(futures),
                total=len(futures),
                desc=f"Processing with {compressor.get_name()}",
            ):
                try:
                    result = future.result()
                    if result:
                        all_results[compressor.get_name()].append(result)
                except Exception as e:
                    logger.error(f"Error processing conversation: {e}")

        # Log filtering statistics for this compressor
        total_conversations = len(request_ids)
        compressor_processed = len(all_results[compressor.get_name()])
        compressor_filtered = total_conversations - compressor_processed
        logger.info(f"{compressor.get_name()} filtering stats:")
        logger.info(f"  Total conversations: {total_conversations}")
        logger.info(f"  Processed: {compressor_processed}")
        logger.info(
            f"  Filtered out (chat history < {args.min_chat_history_length}): {compressor_filtered}"
        )

    # Save results
    # Generate random suffix to avoid overwriting previous results
    random_suffix = "".join(random.choices(string.ascii_lowercase + string.digits, k=8))

    for compressor_name, results in all_results.items():
        if results:
            output_file = output_dir / f"{compressor_name}_results_{random_suffix}.pkl"
            with open(output_file, "wb") as f:
                pickle.dump(results, f)

            logger.info(f"Saved {len(results)} results to {output_file}")

            # Print summary statistics
            total_original_tokens = sum(
                r["stats"].original_tokens for r in results if r["stats"]
            )
            total_compressed_tokens = sum(
                r["stats"].compressed_tokens for r in results if r["stats"]
            )
            avg_compression_rate = sum(
                r["stats"].compression_rate for r in results if r["stats"]
            ) / len(results)

            logger.info(f"{compressor_name} Summary:")
            logger.info(f"  Total original tokens: {total_original_tokens:,}")
            logger.info(f"  Total compressed tokens: {total_compressed_tokens:,}")
            logger.info(f"  Average compression rate: {avg_compression_rate:.2%}")
            logger.info(
                f"  Token savings: {total_original_tokens - total_compressed_tokens:,}"
            )

    # Generate React report
    if any(all_results.values()):
        logger.info("Generating interactive React report...")

        # Convert results to evaluation format
        eval_results = convert_to_eval_results(all_results)

        if eval_results:
            # Create evaluation pipeline and generate analysis summary
            pipeline = CompressionEvaluationPipeline(enable_quality_evaluation=False)
            analysis_summary = pipeline.analyze_compression_results(
                eval_results=eval_results,
                analysis_id=f"compression_analysis_{random_suffix}",
            )

            # Set up web server output directory
            import os

            git_username = os.environ.get("USER", "unknown_user")
            rel_path = (
                f"{git_username}/chat_history_compression/analysis_{random_suffix}"
            )
            web_server_output_dir = WEB_SERVER_DIR / rel_path
            web_server_output_dir.mkdir(parents=True, exist_ok=True)

            # Generate interactive React report
            logger.debug("Generating React report")
            react_start = time.time()
            html_path = save_react_compression_html_report(
                output_dir=web_server_output_dir,
                analysis_summary=analysis_summary,
                report_type="summary",
                filename="index.html",
            )
            react_time = time.time() - react_start
            logger.debug(f"Generated React report in {react_time:.4f} seconds")

            if html_path is None:
                logger.error("Failed to generate React report")
            else:
                # Print URL to the react report
                URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
                filename = Path(html_path).name
                url = URL_TEMPLATE.format(f"{rel_path}/{filename}")
                print(f"\nCompression analysis React report available at: {url}")
        else:
            logger.warning("No evaluation results available for report generation")
    else:
        logger.warning("No compression results available for report generation")

    logger.info("Compression analysis complete!")


if __name__ == "__main__":
    main()
