"""Configuration settings for chat history compression.

This module provides centralized configuration management for all
compression strategies, evaluation settings, and report generation options.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pathlib import Path


@dataclass
class CompressionConfig:
    """Configuration for compression strategies."""

    # Tool compression settings
    compressible_tools: List[str] = field(
        default_factory=lambda: [
            "view",
            "codebase-retrieval",
            "launch-process",
            "str-replace-editor",
            "save-file",
            "grep-search",
        ]
    )

    # Placeholder compression settings
    placeholder_max_content_length: int = 1000
    placeholder_include_error_info: bool = True
    placeholder_include_file_info: bool = True
    placeholder_include_line_count: bool = True

    # LLM compression settings
    llm_compression_enabled: bool = True
    llm_min_content_length: int = 200
    llm_max_input_length: int = 4000
    llm_max_context_length: int = 1000
    llm_temperature: float = 0.3
    llm_max_output_tokens: int = 1000
    llm_fallback_on_failure: bool = True

    # Gemini Flash specific settings
    gemini_project_id: str = "augment-research-gsc"
    gemini_region: str = "us-central1"
    gemini_model_name: str = "gemini-2.0-flash"


@dataclass
class EvaluationConfig:
    """Configuration for evaluation pipeline."""

    # Quality evaluation settings
    enable_quality_evaluation: bool = True
    quality_evaluation_sample_size: Optional[int] = None  # None = evaluate all

    # LLM judge settings
    judge_model_name: str = "claude-3-5-sonnet-20241022"
    judge_max_retries: int = 3
    judge_temperature: float = 0.1
    judge_max_tokens: int = 2000

    # Performance settings
    max_workers: int = 5
    evaluation_timeout_seconds: int = 300

    # Metrics settings
    token_counter_model: str = "claude"  # or "gpt-4"
    include_detailed_metrics: bool = True
    calculate_tool_statistics: bool = True


@dataclass
class ReportConfig:
    """Configuration for HTML report generation."""

    # Output settings
    output_directory: str = "./reports"
    include_timestamps: bool = True
    include_interactive_features: bool = True

    # React component settings
    enable_react_components: bool = True
    react_build_path: Optional[str] = None  # Auto-detect if None

    # Report content settings
    include_quality_metrics: bool = True
    include_detailed_breakdown: bool = True
    max_exchange_samples: int = 5
    max_content_preview_length: int = 500

    # Chart settings
    chart_color_scheme: List[str] = field(
        default_factory=lambda: ["#8884d8", "#82ca9d", "#ffc658", "#ff7c7c", "#8dd1e1"]
    )
    chart_height: int = 300
    enable_chart_animations: bool = True


@dataclass
class DataConfig:
    """Configuration for data access and processing."""

    # BigQuery settings
    default_tenant: str = "dogfood-shard"
    default_limit: int = 100
    default_thread_count: int = 20

    # Time range settings
    default_hours_back: int = 24
    max_conversation_age_days: int = 30

    # Caching settings
    enable_caching: bool = True
    cache_directory: str = "./cache"
    cache_expiry_hours: int = 24

    # Output settings
    save_intermediate_results: bool = True
    results_format: str = "pickle"  # "pickle" or "json"


@dataclass
class CompressionSystemConfig:
    """Complete configuration for the compression system."""

    compression: CompressionConfig = field(default_factory=CompressionConfig)
    evaluation: EvaluationConfig = field(default_factory=EvaluationConfig)
    reporting: ReportConfig = field(default_factory=ReportConfig)
    data: DataConfig = field(default_factory=DataConfig)

    # System-wide settings
    debug_mode: bool = False
    log_level: str = "INFO"
    enable_progress_bars: bool = True

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "CompressionSystemConfig":
        """Create configuration from dictionary."""
        compression_config = CompressionConfig(**config_dict.get("compression", {}))
        evaluation_config = EvaluationConfig(**config_dict.get("evaluation", {}))
        reporting_config = ReportConfig(**config_dict.get("reporting", {}))
        data_config = DataConfig(**config_dict.get("data", {}))

        system_config = config_dict.get("system", {})

        return cls(
            compression=compression_config,
            evaluation=evaluation_config,
            reporting=reporting_config,
            data=data_config,
            debug_mode=system_config.get("debug_mode", False),
            log_level=system_config.get("log_level", "INFO"),
            enable_progress_bars=system_config.get("enable_progress_bars", True),
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "compression": {
                "compressible_tools": self.compression.compressible_tools,
                "placeholder_max_content_length": self.compression.placeholder_max_content_length,
                "placeholder_include_error_info": self.compression.placeholder_include_error_info,
                "placeholder_include_file_info": self.compression.placeholder_include_file_info,
                "placeholder_include_line_count": self.compression.placeholder_include_line_count,
                "llm_compression_enabled": self.compression.llm_compression_enabled,
                "llm_min_content_length": self.compression.llm_min_content_length,
                "llm_max_input_length": self.compression.llm_max_input_length,
                "llm_max_context_length": self.compression.llm_max_context_length,
                "llm_temperature": self.compression.llm_temperature,
                "llm_max_output_tokens": self.compression.llm_max_output_tokens,
                "llm_fallback_on_failure": self.compression.llm_fallback_on_failure,
                "gemini_project_id": self.compression.gemini_project_id,
                "gemini_region": self.compression.gemini_region,
                "gemini_model_name": self.compression.gemini_model_name,
            },
            "evaluation": {
                "enable_quality_evaluation": self.evaluation.enable_quality_evaluation,
                "quality_evaluation_sample_size": self.evaluation.quality_evaluation_sample_size,
                "judge_model_name": self.evaluation.judge_model_name,
                "judge_max_retries": self.evaluation.judge_max_retries,
                "judge_temperature": self.evaluation.judge_temperature,
                "judge_max_tokens": self.evaluation.judge_max_tokens,
                "max_workers": self.evaluation.max_workers,
                "evaluation_timeout_seconds": self.evaluation.evaluation_timeout_seconds,
                "token_counter_model": self.evaluation.token_counter_model,
                "include_detailed_metrics": self.evaluation.include_detailed_metrics,
                "calculate_tool_statistics": self.evaluation.calculate_tool_statistics,
            },
            "reporting": {
                "output_directory": self.reporting.output_directory,
                "include_timestamps": self.reporting.include_timestamps,
                "include_interactive_features": self.reporting.include_interactive_features,
                "enable_react_components": self.reporting.enable_react_components,
                "react_build_path": self.reporting.react_build_path,
                "include_quality_metrics": self.reporting.include_quality_metrics,
                "include_detailed_breakdown": self.reporting.include_detailed_breakdown,
                "max_exchange_samples": self.reporting.max_exchange_samples,
                "max_content_preview_length": self.reporting.max_content_preview_length,
                "chart_color_scheme": self.reporting.chart_color_scheme,
                "chart_height": self.reporting.chart_height,
                "enable_chart_animations": self.reporting.enable_chart_animations,
            },
            "data": {
                "default_tenant": self.data.default_tenant,
                "default_limit": self.data.default_limit,
                "default_thread_count": self.data.default_thread_count,
                "default_hours_back": self.data.default_hours_back,
                "max_conversation_age_days": self.data.max_conversation_age_days,
                "enable_caching": self.data.enable_caching,
                "cache_directory": self.data.cache_directory,
                "cache_expiry_hours": self.data.cache_expiry_hours,
                "save_intermediate_results": self.data.save_intermediate_results,
                "results_format": self.data.results_format,
            },
            "system": {
                "debug_mode": self.debug_mode,
                "log_level": self.log_level,
                "enable_progress_bars": self.enable_progress_bars,
            },
        }

    def save_to_file(self, file_path: str) -> None:
        """Save configuration to JSON file."""
        import json

        config_dict = self.to_dict()

        with open(file_path, "w") as f:
            json.dump(config_dict, f, indent=2)

    @classmethod
    def load_from_file(cls, file_path: str) -> "CompressionSystemConfig":
        """Load configuration from JSON file."""
        import json

        with open(file_path, "r") as f:
            config_dict = json.load(f)

        return cls.from_dict(config_dict)

    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []

        # Validate compression settings
        if self.compression.llm_min_content_length < 0:
            issues.append("llm_min_content_length must be non-negative")

        if self.compression.llm_temperature < 0 or self.compression.llm_temperature > 1:
            issues.append("llm_temperature must be between 0 and 1")

        # Validate evaluation settings
        if self.evaluation.max_workers < 1:
            issues.append("max_workers must be at least 1")

        if (
            self.evaluation.judge_temperature < 0
            or self.evaluation.judge_temperature > 1
        ):
            issues.append("judge_temperature must be between 0 and 1")

        # Validate data settings
        if self.data.default_limit < 1:
            issues.append("default_limit must be at least 1")

        if self.data.default_thread_count < 1:
            issues.append("default_thread_count must be at least 1")

        # Validate paths
        if self.reporting.react_build_path:
            if not Path(self.reporting.react_build_path).exists():
                issues.append(
                    f"react_build_path does not exist: {self.reporting.react_build_path}"
                )

        return issues


# Default configuration instance
DEFAULT_CONFIG = CompressionSystemConfig()


def get_config() -> CompressionSystemConfig:
    """Get the default configuration."""
    return DEFAULT_CONFIG


def load_config(config_path: str) -> CompressionSystemConfig:
    """Load configuration from file."""
    return CompressionSystemConfig.load_from_file(config_path)


def create_sample_config(output_path: str) -> None:
    """Create a sample configuration file."""
    config = CompressionSystemConfig()
    config.save_to_file(output_path)
    print(f"Sample configuration saved to: {output_path}")


if __name__ == "__main__":
    # Create sample configuration when run as script
    import sys

    if len(sys.argv) > 1:
        output_path = sys.argv[1]
    else:
        output_path = "compression_config.json"

    create_sample_config(output_path)
