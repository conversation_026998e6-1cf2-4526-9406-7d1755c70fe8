#!/usr/bin/env python3
"""Simple test script to verify basic functionality without external dependencies."""

import sys
import os

# Add the repository root to the path
sys.path.insert(0, "/home/<USER>/augment")


def test_basic_imports():
    """Test basic imports work."""
    print("Testing basic imports...")

    try:
        # Test basic Python functionality
        from typing import List, Dict, Optional

        print("✓ Basic typing imports work")
    except Exception as e:
        print(f"✗ Basic typing imports failed: {e}")
        return False

    try:
        # Test dataclass import
        from dataclasses import dataclass

        print("✓ Dataclass import works")
    except Exception as e:
        print(f"✗ Dataclass import failed: {e}")
        return False

    return True


def test_compression_config():
    """Test the configuration module."""
    print("\nTesting configuration module...")

    try:
        from experimental.vpas.agent.chat_history_compression.config import (
            CompressionSystemConfig,
        )

        # Create a default config
        config = CompressionSystemConfig()
        print("✓ Configuration creation works")

        # Test validation
        issues = config.validate()
        print(f"✓ Configuration validation works (found {len(issues)} issues)")

        # Test serialization
        config_dict = config.to_dict()
        print("✓ Configuration serialization works")

        # Test deserialization
        CompressionSystemConfig.from_dict(config_dict)
        print("✓ Configuration deserialization works")

        return True

    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_utils_basic():
    """Test basic utility functions."""
    print("\nTesting basic utility functions...")

    try:
        # Import the utils module
        sys.path.insert(
            0, "/home/<USER>/augment/experimental/vpas/agent/chat_history_compression"
        )
        import utils

        # Test should_compress_tool function
        assert utils.should_compress_tool("view")
        assert not utils.should_compress_tool("unknown-tool")
        print("✓ should_compress_tool function works")

        return True

    except Exception as e:
        print(f"✗ Utils test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_placeholder_compressor_basic():
    """Test basic PlaceholderCompressor functionality."""
    print("\nTesting PlaceholderCompressor basic functionality...")

    try:
        from experimental.vpas.agent.chat_history_compression.placeholder_compressor import (
            PlaceholderCompressor,
        )

        # Create compressor
        compressor = PlaceholderCompressor()
        print("✓ PlaceholderCompressor creation works")

        # Test with empty list
        result = compressor.compress([])
        assert result == []
        print("✓ Empty compression works")

        # Test get_name
        name = compressor.get_name()
        assert name == "PlaceholderCompressor"
        print("✓ get_name works")

        return True

    except Exception as e:
        print(f"✗ PlaceholderCompressor test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("RUNNING SIMPLE TESTS FOR CHAT HISTORY COMPRESSION")
    print("=" * 60)

    tests = [
        test_basic_imports,
        test_compression_config,
        test_utils_basic,
        test_placeholder_compressor_basic,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} PASSED")
            else:
                failed += 1
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} FAILED with exception: {e}")

    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)

    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
