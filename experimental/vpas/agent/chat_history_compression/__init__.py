"""Chat History Compression Module

This module provides tools for compressing Agent chat history to reduce token usage
while preserving important information for conversation continuity.

Main components:
- ChatHistoryCompressor: Base interface for compression strategies
- PlaceholderCompressor: Replace tool outputs with placeholders
- GeminiFlashCompressor: Use Gemini Flash LLM for intelligent summarization
- Evaluation framework for measuring compression quality
- HTML report generation for analysis
"""

from .chat_history_compression import ChatHistoryCompressor, CompressionStats

__all__ = [
    "ChatHistoryCompressor",
    "CompressionStats",
]
