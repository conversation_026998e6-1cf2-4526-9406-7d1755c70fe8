# Remote Agent Startup Performance Analyzer

A command line tool to analyze remote agent startup performance by examining logs with `_elapsed_sec` fields from Google Cloud Logging. Reports median elapsed times for specific startup events.

## Features

- Analyzes startup performance metrics from remote agent logs
- Reports median, mean, min, max, and 95th percentile times
- Groups performance by event type and component
- Supports filtering by specific events
- Multiple output formats: summary, JSON, CSV
- Defaults to last 24 hours of logs for performance

## Usage

### Basic Usage

```bash
# Analyze startup performance from the last 24 hours (default)
./remote_agent_startup_analyzer.py

# Analyze startup performance from the last 6 hours
./remote_agent_startup_analyzer.py --hours 6

# Analyze startup performance from a specific time range
./remote_agent_startup_analyzer.py --start "2024-01-15T10:00:00Z" --end "2024-01-15T18:00:00Z"
```

### Event Filtering

```bash
# Filter to specific startup events
./remote_agent_startup_analyzer.py --event "networking_setup"
./remote_agent_startup_analyzer.py --event "prep_root"
./remote_agent_startup_analyzer.py --event "ssh-connect"
./remote_agent_startup_analyzer.py --event "beachhead_connect"
```

### Output Formats

```bash
# Detailed summary (default)
./remote_agent_startup_analyzer.py --verbose

# JSON output for programmatic processing
./remote_agent_startup_analyzer.py --format json

# CSV output for spreadsheet analysis
./remote_agent_startup_analyzer.py --format csv
```

## Startup Events Tracked

The tool tracks several key startup events:

1. **networking_setup** - Network bridge and routing setup
2. **prep_root** - Root filesystem preparation (disk image copy)
3. **prep_persist** - Persistent storage setup
4. **ssh_connect** - SSH connectivity to VM
5. **beachhead_connect** - Beachhead service health check
6. **startup_complete** - Overall startup completion

## Performance Metrics

For each event type, the tool reports:

- **Count**: Number of occurrences
- **Median**: 50th percentile time (most representative)
- **Mean**: Average time
- **Min/Max**: Fastest and slowest times
- **95th percentile**: Performance threshold for outliers

## Example Output

```
Remote Agent Startup Performance Analysis
Time Range: 2025-06-09 18:00:00 UTC to 2025-06-10 18:00:00 UTC
Total Events: 45

Overall Elapsed Time Statistics:
  Count: 45
  Median: 0.12s
  Mean: 0.15s
  Min: 0.01s
  Max: 0.85s
  95th percentile: 0.45s

Performance by Event Type:
  networking_setup:
    Count: 15
    Median: 0.08s
    Mean: 0.09s
    95th percentile: 0.15s

  prep_root:
    Count: 15
    Median: 12.5s
    Mean: 15.2s
    95th percentile: 25.8s

  ssh_connect:
    Count: 15
    Median: 45.2s
    Mean: 52.1s
    95th percentile: 85.3s
```

## Requirements

- Python 3.6+
- `gcloud` CLI tool configured with access to `agent-sandbox-prod` project
- Appropriate GCP permissions to read Cloud Logging

## Log Filter Details

The tool searches for logs with:
- `jsonPayload.elapsed_sec` field present
- `resource.type="k8s_container"`
- Within the specified time range
- Optionally filtered by event message content

This provides a more restricted search compared to the beachhead failure analyzer, improving query performance while focusing on startup timing data.
