#!/usr/bin/env python3
"""
Remote Agent Startup Performance Analyzer

A command line tool to analyze remote agent startup performance by examining logs
with _elapsed_sec fields from Google Cloud Logging. Reports median elapsed times
for specific startup events.
"""

import argparse
import json
import sys
import subprocess
import statistics
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze remote agent startup performance from Google Cloud Logging",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze startup performance from the last 24 hours (default)
  %(prog)s

  # Analyze startup performance from the last 6 hours
  %(prog)s --hours 6

  # Analyze startup performance from a specific time range
  %(prog)s --start "2024-01-15T10:00:00Z" --end "2024-01-15T18:00:00Z"

  # Include detailed log entries
  %(prog)s --verbose

  # Export to JSON format
  %(prog)s --format json
        """,
    )

    # Time range options
    time_group = parser.add_mutually_exclusive_group()
    time_group.add_argument(
        "--hours",
        type=int,
        default=24,
        help="Number of hours to look back from now (default: 24)",
    )
    time_group.add_argument(
        "--start",
        type=str,
        help="Start time in ISO format (e.g., 2024-01-15T10:00:00Z)",
    )

    parser.add_argument(
        "--end", type=str, help="End time in ISO format (only used with --start)"
    )

    # Query options
    parser.add_argument(
        "--limit",
        type=int,
        default=2000,
        help="Maximum number of log entries to retrieve (default: 2000)",
    )

    # Output options
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Include detailed log entries in output",
    )

    parser.add_argument(
        "--format",
        choices=["summary", "json", "csv"],
        default="summary",
        help="Output format (default: summary)",
    )

    # Event filtering
    parser.add_argument(
        "--event",
        type=str,
        help="Filter to specific event type (e.g., networking_setup, prep_root, ssh-connect)",
    )

    return parser.parse_args()


def get_time_range(args) -> tuple[datetime, datetime]:
    """Get the time range for the query based on arguments."""
    if args.start:
        start_time = datetime.fromisoformat(args.start.replace("Z", "+00:00"))
        if args.end:
            end_time = datetime.fromisoformat(args.end.replace("Z", "+00:00"))
        else:
            end_time = datetime.now(timezone.utc)
    else:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=args.hours)

    return start_time, end_time


def get_projects_to_query() -> List[str]:
    """Get the list of GCP projects to query."""
    # Default to production agent cluster only
    return ["agent-sandbox-prod"]


def build_log_filter(
    start_time: datetime, end_time: datetime, event_filter: Optional[str] = None
) -> str:
    """Build the Google Cloud Logging filter for remote agent startup events."""
    start_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    end_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

    # Build filter for logs with _augment_event or event fields
    base_filter = f"""
        resource.type="k8s_container"
        AND timestamp >= "{start_str}"
        AND timestamp <= "{end_str}"
        AND (jsonPayload._augment_event != "" OR jsonPayload.event != "")
    """.strip()

    # Add event-specific filter if provided
    if event_filter:
        base_filter += f' AND (jsonPayload._augment_event:"{event_filter}" OR jsonPayload.event:"{event_filter}" OR jsonPayload.msg:"{event_filter}")'

    return base_filter


def query_logs(project: str, log_filter: str, limit: int) -> List[Dict[str, Any]]:
    """Query logs from a specific project using gcloud command."""
    try:
        # Build gcloud command
        cmd = [
            "gcloud",
            "logging",
            "read",
            log_filter,
            f"--project={project}",
            f"--limit={limit}",
            "--format=json",
            "--freshness=1d",  # Only look at recent logs for performance
        ]

        # Execute gcloud command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600,  # 5 minute timeout for larger queries
        )

        if result.returncode != 0:
            print(f"Error querying project {project}: {result.stderr}", file=sys.stderr)
            return []

        # Parse JSON output
        if not result.stdout.strip():
            return []

        log_entries = json.loads(result.stdout)

        # Convert to our format
        results = []
        for entry in log_entries:
            log_data = {
                "timestamp": entry.get("timestamp", ""),
                "project": project,
                "severity": entry.get("severity", ""),
                "resource": entry.get("resource", {}),
                "labels": entry.get("labels", {}),
                "payload": entry.get("jsonPayload", {}),
            }
            results.append(log_data)

        return results

    except subprocess.TimeoutExpired:
        print(f"Timeout querying project {project}", file=sys.stderr)
        return []
    except json.JSONDecodeError as e:
        print(
            f"Error parsing JSON response from project {project}: {e}", file=sys.stderr
        )
        return []
    except Exception as e:
        print(f"Error querying project {project}: {e}", file=sys.stderr)
        return []


def extract_event_info(log_entry: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Extract event information from a log entry."""
    payload = log_entry.get("payload", {})
    if not isinstance(payload, dict):
        return None

    # Extract elapsed_sec - skip entries without timing info
    elapsed_sec = payload.get("elapsed_sec")
    if elapsed_sec is None:
        return None

    try:
        elapsed_sec = float(elapsed_sec)
    except (ValueError, TypeError):
        return None

    # Extract message and component info
    message = payload.get("msg", "unknown")
    component = payload.get("component", "unknown")

    # Check for _augment_event first, then fall back to event
    event = payload.get("_augment_event")
    if not event:
        event = payload.get("event", "unknown")

    startup_cost = payload.get("startup_cost")

    # Extract pod info
    resource = log_entry.get("resource", {})
    labels = resource.get("labels", {}) if isinstance(resource, dict) else {}

    pod_name = labels.get("pod_name", "unknown")
    namespace = labels.get("namespace_name", "unknown")
    cluster = labels.get("cluster_name", "unknown")

    return {
        "timestamp": log_entry["timestamp"],
        "elapsed_sec": elapsed_sec,
        "startup_cost": startup_cost,
        "message": message,
        "component": component,
        "event": event,
        "pod_name": pod_name,
        "namespace": namespace,
        "cluster": cluster,
        "project": log_entry["project"],
    }


def analyze_startup_performance(log_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze startup performance from log entries."""
    if not log_entries:
        return {
            "total_events": 0,
            "event_stats": {},
            "component_stats": {},
            "overall_stats": {},
            "actual_time_range": None,
        }

    # Process all log entries
    events = []
    timestamps = []
    for entry in log_entries:
        event_info = extract_event_info(entry)
        if event_info:
            events.append(event_info)
            timestamps.append(event_info["timestamp"])

    if not events:
        return {
            "total_events": 0,
            "event_stats": {},
            "component_stats": {},
            "overall_stats": {},
            "actual_time_range": None,
        }

    # Calculate actual time range from log entries
    actual_time_range = None
    if timestamps:
        # Parse timestamps and find min/max
        parsed_timestamps = []
        for ts in timestamps:
            try:
                parsed_ts = datetime.fromisoformat(ts.replace("Z", "+00:00"))
                parsed_timestamps.append(parsed_ts)
            except (ValueError, AttributeError):
                continue

        if parsed_timestamps:
            actual_time_range = {
                "start": min(parsed_timestamps),
                "end": max(parsed_timestamps),
            }

    # Group by event type (based on message patterns)
    event_groups = defaultdict(list)
    component_groups = defaultdict(list)
    startup_cost_events = []

    for event in events:
        # Group by actual event field from JSON payload, with fallback to message patterns
        event_type = event["event"]
        component = event["component"]

        # Use event field if it's meaningful, otherwise fall back to message patterns
        if event_type and event_type != "unknown":
            if component and component != "unknown":
                event_key = f"{component}_{event_type}"
            else:
                event_key = event_type
            event_groups[event_key].append(event["elapsed_sec"])
        else:
            continue

        # Group by component (handle None values)
        component = event["component"]
        if component and component != "unknown":
            component_groups[component].append(event["elapsed_sec"])

        # Collect startup_cost values
        if event["startup_cost"] is not None:
            try:
                startup_cost_events.append(float(event["startup_cost"]))
            except (ValueError, TypeError):
                pass

    # Calculate statistics for each group
    def calc_stats(values):
        if not values:
            return None
        return {
            "count": len(values),
            "median": statistics.median(values),
            "mean": statistics.mean(values),
            "min": min(values),
            "max": max(values),
            "p95": statistics.quantiles(values, n=20)[18]
            if len(values) >= 20
            else max(values),
        }

    event_stats = {
        event_type: calc_stats(times) for event_type, times in event_groups.items()
    }
    component_stats = {
        comp: calc_stats(times) for comp, times in component_groups.items()
    }

    # Overall statistics
    all_elapsed_times = [event["elapsed_sec"] for event in events]
    overall_stats = calc_stats(all_elapsed_times)

    return {
        "total_events": len(events),
        "event_stats": {k: v for k, v in event_stats.items() if v is not None},
        "component_stats": {k: v for k, v in component_stats.items() if v is not None},
        "overall_stats": overall_stats,
        "startup_cost_stats": calc_stats(startup_cost_events)
        if startup_cost_events
        else None,
        "events": events
        if len(events) <= 100
        else events[:100],  # Limit for verbose output
        "actual_time_range": actual_time_range,
    }


def format_summary_output(
    analysis: Dict[str, Any],
    start_time: datetime,
    end_time: datetime,
):
    """Format the analysis results as a human-readable summary."""
    print("Remote Agent Startup Performance Analysis")

    # Use actual time range from logs if available, otherwise fall back to query range
    if analysis.get("actual_time_range"):
        actual_start = analysis["actual_time_range"]["start"]
        actual_end = analysis["actual_time_range"]["end"]
        print(
            f"Time Range: {actual_start.strftime('%Y-%m-%d %H:%M:%S UTC')} to {actual_end.strftime('%Y-%m-%d %H:%M:%S UTC')} (actual log range)"
        )
    else:
        print(
            f"Time Range: {start_time.strftime('%Y-%m-%d %H:%M:%S UTC')} to {end_time.strftime('%Y-%m-%d %H:%M:%S UTC')} (query range)"
        )

    print(f"Total Events: {analysis['total_events']}")
    print()

    if analysis["total_events"] == 0:
        print("No startup events found in the specified time range.")
        return

    # Event-specific statistics as a table
    if analysis["event_stats"]:
        print("Performance by Event Type:")
        print()

        # Filter out None keys and sort by median
        valid_events = {
            k: v
            for k, v in analysis["event_stats"].items()
            if k is not None and v is not None
        }
        sorted_events = sorted(valid_events.items(), key=lambda x: x[1]["median"])

        if sorted_events:
            # Table header
            print(
                f"{'Event Type':<35} {'Count':<8} {'Min':<8} {'Median':<8} {'Mean':<8} {'95%':<8} {'Max':<8}"
            )
            print("-" * 83)

            # Table rows
            for event_type, stats in sorted_events:
                print(
                    f"{event_type:<35} {stats['count']:<8} {stats['min']:<8.2f} {stats['median']:<8.2f} {stats['mean']:<8.2f} {stats['p95']:<8.2f} {stats['max']:<8.2f}"
                )
        print()


def format_json_output(analysis: Dict[str, Any]):
    """Format the analysis results as JSON."""
    print(json.dumps(analysis, indent=2, default=str))


def format_csv_output(analysis: Dict[str, Any]):
    """Format the event details as CSV."""
    events = analysis.get("events", [])

    if not events:
        print(
            "timestamp,elapsed_sec,startup_cost,message,component,event,pod_name,namespace,cluster,project"
        )
        return

    # CSV header
    print(
        "timestamp,elapsed_sec,startup_cost,message,component,event,pod_name,namespace,cluster,project"
    )

    # CSV rows
    for event in events:
        # Escape commas and quotes in message
        message = str(event["message"]).replace('"', '""')
        if "," in message:
            message = f'"{message}"'

        startup_cost = event.get("startup_cost", "")
        if startup_cost is None:
            startup_cost = ""

        print(
            f"{event['timestamp']},{event['elapsed_sec']},{startup_cost},{message},"
            f"{event['component']},{event['event']},{event['pod_name']},"
            f"{event['namespace']},{event['cluster']},{event['project']}"
        )


def main():
    """Main function."""
    args = parse_args()

    start_time, end_time = get_time_range(args)
    projects = get_projects_to_query()
    log_filter = build_log_filter(start_time, end_time, args.event)

    if args.verbose:
        print(f"Querying projects: {', '.join(projects)}", file=sys.stderr)
        print(f"Time range: {start_time} to {end_time}", file=sys.stderr)
        print(f"Log filter: {log_filter}", file=sys.stderr)
        if args.event:
            print(f"Event filter: {args.event}", file=sys.stderr)
        print(file=sys.stderr)

    # Query all projects
    all_entries = []
    for project in projects:
        if args.verbose:
            print(f"Querying project: {project}...", file=sys.stderr)

        entries = query_logs(project, log_filter, args.limit)
        all_entries.extend(entries)

        if args.verbose:
            print(f"Found {len(entries)} entries in {project}", file=sys.stderr)

    # Analyze the results
    analysis = analyze_startup_performance(all_entries)

    # Output results
    if args.format == "json":
        format_json_output(analysis)
    elif args.format == "csv":
        format_csv_output(analysis)
    else:
        format_summary_output(analysis, start_time, end_time)

        if args.verbose and analysis.get("events"):
            print("\nDetailed Event Entries (first 20):")
            for event in analysis["events"][:20]:
                timestamp = datetime.fromisoformat(
                    event["timestamp"].replace("Z", "+00:00")
                )
                print(
                    f"  {timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')} | "
                    f"{event['elapsed_sec']:6.2f}s | {event['component']:<15} | "
                    f"{event['pod_name']:<30} | {event['message']}"
                )
            if len(analysis["events"]) > 20:
                print(f"  ... and {len(analysis['events']) - 20} more entries")


if __name__ == "__main__":
    main()
