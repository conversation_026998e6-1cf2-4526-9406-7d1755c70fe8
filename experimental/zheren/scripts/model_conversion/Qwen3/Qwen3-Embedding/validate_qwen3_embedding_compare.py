#!/usr/bin/env python3
"""Compare embeddings from converted FastBackward model with official Transformers implementation."""

import argparse
import json
import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from typing import Optional, List, Union

from research.fastbackward.model import Transformer, ModelArgs
from base.tokenizers.tokenizer import <PERSON><PERSON><PERSON><PERSON><PERSON> as TOKENIZER_REGISTRY
from research.fastbackward import distributed
from transformers import AutoTokenizer, AutoModel
from transformers.utils import is_flash_attn_2_available


class Qwen3EmbeddingTransformers:
    """Official Transformers implementation of Qwen3-Embedding."""

    def __init__(
        self,
        model_name_or_path,
        instruction=None,
        use_fp16: bool = True,
        use_cuda: bool = True,
        max_length=8192,
    ):
        if instruction is None:
            instruction = "Given a web search query, retrieve relevant passages that answer the query"
        self.instruction = instruction
        if is_flash_attn_2_available() and use_cuda:
            self.model = AutoModel.from_pretrained(
                model_name_or_path,
                trust_remote_code=True,
                attn_implementation="flash_attention_2",
                torch_dtype=torch.float16,
            )
        else:
            self.model = AutoModel.from_pretrained(
                model_name_or_path, trust_remote_code=True, torch_dtype=torch.float16
            )
        if use_cuda:
            self.model = self.model.cuda()
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name_or_path, trust_remote_code=True, padding_side="left"
        )
        self.max_length = max_length

    def last_token_pool(
        self, last_hidden_states: torch.Tensor, attention_mask: torch.Tensor
    ) -> torch.Tensor:
        left_padding = attention_mask[:, -1].sum() == attention_mask.shape[0]
        if left_padding:
            return last_hidden_states[:, -1]
        else:
            sequence_lengths = attention_mask.sum(dim=1) - 1
            batch_size = last_hidden_states.shape[0]
            return last_hidden_states[
                torch.arange(batch_size, device=last_hidden_states.device),
                sequence_lengths,
            ]

    def get_detailed_instruct(self, task_description: str, query: str) -> str:
        if task_description is None:
            task_description = self.instruction
        return f"Instruct: {task_description}\nQuery:{query}"  # No space after Query:

    def encode(
        self,
        sentences: Union[List[str], str],
        is_query: bool = False,
        instruction=None,
        normalize: bool = True,
    ):
        if isinstance(sentences, str):
            sentences = [sentences]
        if is_query:
            sentences = [
                self.get_detailed_instruct(instruction, sent) for sent in sentences
            ]
        inputs = self.tokenizer(
            sentences,
            padding=True,
            truncation=True,
            max_length=self.max_length,
            return_tensors="pt",
        )
        inputs.to(self.model.device)
        with torch.no_grad():
            model_outputs = self.model(**inputs)
            output = self.last_token_pool(
                model_outputs.last_hidden_state, inputs["attention_mask"]
            )
            if normalize:
                output = F.normalize(output, p=2, dim=1)
        return output


def load_fbwd_model_with_hack(
    checkpoint_path: str, device: str = "cuda"
) -> tuple[Transformer, ModelArgs]:
    """Load a FastBackward model from checkpoint with a hack for embedding models."""
    checkpoint_path = Path(checkpoint_path)

    # Load model args
    with open(checkpoint_path / "params.json", "r") as f:
        model_args_dict = json.load(f)

    model_args = ModelArgs.schema().load(model_args_dict)

    # Create model
    model = Transformer(model_args)

    # Load checkpoint
    checkpoint = torch.load(
        checkpoint_path / "consolidated.00.pth", map_location=device
    )

    # HACK: Add a dummy output.weight if it's missing (for embedding models)
    if "output.weight" not in checkpoint and "tok_embeddings.weight" in checkpoint:
        checkpoint["output.weight"] = checkpoint["tok_embeddings.weight"].clone()

    model.load_state_dict(checkpoint, strict=True)

    model = model.to(device)
    model.eval()

    return model, model_args


def get_fbwd_embeddings(
    model: Transformer,
    tokenizer,
    sentences: Union[List[str], str],
    is_query: bool = False,
    instruction: str = None,
    normalize: bool = True,
    device: str = "cuda",
) -> torch.Tensor:
    """Get embeddings from FastBackward model."""
    if isinstance(sentences, str):
        sentences = [sentences]

    # Format queries with instruction
    if is_query:
        if instruction is None:
            instruction = "Given a web search query, retrieve relevant passages that answer the query"
        sentences = [
            f"Instruct: {instruction}\nQuery:{sent}" for sent in sentences
        ]  # No space after Query:

    all_embeddings = []

    for sentence in sentences:
        # Tokenize
        tokens = tokenizer.tokenize_unsafe(sentence)
        tokens_tensor = torch.tensor([tokens], dtype=torch.long, device=device)

        with torch.no_grad():
            # Get hidden states before output projection
            hidden_states = model.forward(tokens_tensor, skip_output=True)
            # Extract from last token position (EOS)
            embedding = hidden_states[:, -1, :]

            if normalize:
                embedding = F.normalize(embedding, p=2, dim=1)

            all_embeddings.append(embedding)

    # Concatenate all embeddings
    return torch.cat(all_embeddings, dim=0)


def main(
    fbwd_checkpoint_path: str,
    hf_model_path: str,
    tokenizer_name: str = "qwen3-embedding-4b",
):
    """Compare embeddings from FastBackward and Transformers implementations.

    Args:
        fbwd_checkpoint_path: Path to the converted FastBackward checkpoint
        hf_model_path: Path to the HuggingFace model (e.g., "Qwen/Qwen3-Embedding-4B")
        tokenizer_name: Name of the tokenizer to use for FastBackward
    """
    # Initialize distributed for FastBackward
    distributed.init_distributed_for_training(1)

    print("Loading models...")
    print(f"FastBackward checkpoint: {fbwd_checkpoint_path}")
    print(f"HuggingFace model: {hf_model_path}")

    # Load FastBackward model
    fbwd_model, model_args = load_fbwd_model_with_hack(fbwd_checkpoint_path)
    fbwd_tokenizer = TOKENIZER_REGISTRY.get(tokenizer_name)()

    # Load Transformers model
    hf_model = Qwen3EmbeddingTransformers(hf_model_path)

    print("\nModel loaded successfully!")
    print(f"Hidden size: {model_args.dim}")
    print(f"Num layers: {model_args.n_layers}")

    # Test data
    queries = [
        "What is the capital of China?",
        "Explain gravity",
        "How do I implement a binary search tree in Python?",
    ]
    documents = [
        "The capital of China is Beijing.",
        "Gravity is a force that attracts two bodies towards each other. It gives weight to physical objects and is responsible for the movement of planets around the sun.",
        "To implement a binary search tree in Python, create a Node class with left and right pointers, then implement insert, search, and delete methods.",
    ]

    print("\n" + "=" * 60)
    print("Comparing embeddings...")
    print("=" * 60)

    # Get embeddings from both models
    print("\nGenerating query embeddings...")
    fbwd_query_emb = get_fbwd_embeddings(
        fbwd_model, fbwd_tokenizer, queries, is_query=True
    )
    hf_query_emb = hf_model.encode(queries, is_query=True)

    print("Generating document embeddings...")
    fbwd_doc_emb = get_fbwd_embeddings(
        fbwd_model, fbwd_tokenizer, documents, is_query=False
    )
    hf_doc_emb = hf_model.encode(documents)

    # Move to CPU for comparison
    fbwd_query_emb = fbwd_query_emb.cpu()
    fbwd_doc_emb = fbwd_doc_emb.cpu()
    hf_query_emb = hf_query_emb.cpu()
    hf_doc_emb = hf_doc_emb.cpu()

    # Compare embeddings
    print("\n" + "=" * 60)
    print("EMBEDDING COMPARISON")
    print("=" * 60)

    for i, query in enumerate(queries):
        print(f"\nQuery {i+1}: {query[:50]}...")

        # Cosine similarity between FastBackward and HF embeddings
        cos_sim = F.cosine_similarity(
            fbwd_query_emb[i : i + 1], hf_query_emb[i : i + 1], dim=1
        ).item()
        l2_dist = torch.norm(fbwd_query_emb[i] - hf_query_emb[i]).item()

        print(f"  Cosine similarity (FBWD vs HF): {cos_sim:.6f}")
        print(f"  L2 distance (FBWD vs HF): {l2_dist:.6f}")

        # Show first few dimensions
        print(f"  FBWD first 5 dims: {fbwd_query_emb[i, :5].numpy()}")
        print(f"  HF first 5 dims: {hf_query_emb[i, :5].numpy()}")

    print("\n" + "-" * 60)

    for i, doc in enumerate(documents):
        print(f"\nDocument {i+1}: {doc[:50]}...")

        # Cosine similarity between FastBackward and HF embeddings
        cos_sim = F.cosine_similarity(
            fbwd_doc_emb[i : i + 1], hf_doc_emb[i : i + 1], dim=1
        ).item()
        l2_dist = torch.norm(fbwd_doc_emb[i] - hf_doc_emb[i]).item()

        print(f"  Cosine similarity (FBWD vs HF): {cos_sim:.6f}")
        print(f"  L2 distance (FBWD vs HF): {l2_dist:.6f}")

    # Compute similarity matrices
    print("\n" + "=" * 60)
    print("SIMILARITY MATRICES (Query x Document)")
    print("=" * 60)

    fbwd_scores = (fbwd_query_emb @ fbwd_doc_emb.T) * 100
    hf_scores = (hf_query_emb @ hf_doc_emb.T) * 100

    print("\nFastBackward similarity scores:")
    for i, query in enumerate(queries):
        print(f"  Query {i+1}: {fbwd_scores[i].numpy()}")

    print("\nHuggingFace similarity scores:")
    for i, query in enumerate(queries):
        print(f"  Query {i+1}: {hf_scores[i].numpy()}")

    # Compare the similarity matrices
    score_diff = torch.abs(fbwd_scores - hf_scores)
    print(f"\nMax absolute difference in scores: {score_diff.max().item():.6f}")
    print(f"Mean absolute difference in scores: {score_diff.mean().item():.6f}")

    # Overall assessment
    print("\n" + "=" * 60)
    print("OVERALL ASSESSMENT")
    print("=" * 60)

    all_cos_sims = []
    for i in range(len(queries)):
        cos_sim = F.cosine_similarity(
            fbwd_query_emb[i : i + 1], hf_query_emb[i : i + 1], dim=1
        ).item()
        all_cos_sims.append(cos_sim)
    for i in range(len(documents)):
        cos_sim = F.cosine_similarity(
            fbwd_doc_emb[i : i + 1], hf_doc_emb[i : i + 1], dim=1
        ).item()
        all_cos_sims.append(cos_sim)

    mean_cos_sim = np.mean(all_cos_sims)
    min_cos_sim = np.min(all_cos_sims)

    print(f"Mean cosine similarity: {mean_cos_sim:.6f}")
    print(f"Min cosine similarity: {min_cos_sim:.6f}")

    if mean_cos_sim > 0.99:
        print("\n✅ EXCELLENT: Embeddings are nearly identical!")
    elif mean_cos_sim > 0.95:
        print("\n✅ GOOD: Embeddings are highly similar!")
    elif mean_cos_sim > 0.90:
        print("\n⚠️  OK: Embeddings are similar but with some differences")
    else:
        print("\n❌ POOR: Embeddings show significant differences")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Compare FastBackward and Transformers Qwen3-Embedding models"
    )
    parser.add_argument(
        "--fbwd_checkpoint_path",
        type=str,
        required=True,
        help="Path to the converted FastBackward checkpoint",
    )
    parser.add_argument(
        "--hf_model_path",
        type=str,
        required=True,
        help="Path or name of the HuggingFace model (e.g., 'Qwen/Qwen3-Embedding-4B')",
    )
    parser.add_argument(
        "--tokenizer_name",
        type=str,
        default="qwen3-embedding-4b",
        help="Name of the tokenizer to use for FastBackward",
    )

    args = parser.parse_args()
    main(
        fbwd_checkpoint_path=args.fbwd_checkpoint_path,
        hf_model_path=args.hf_model_path,
        tokenizer_name=args.tokenizer_name,
    )
