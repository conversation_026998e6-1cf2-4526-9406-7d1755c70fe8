"""
Query BigQuery for remote agent workspace uptime events and display them in a formatted table.

This tool retrieves uptime events for a specific remote agent from the last 7
days, coalesces nearby events to reduce noise, and displays the results without
microseconds.
"""

import argparse
import datetime
from dataclasses import dataclass

from google.cloud import bigquery


TABLE_SEPARATOR = "    "

UPTIME_TABLE = "remote_agent_workspace_uptime"
QUERY = """
SELECT
  DATETIME(uptime_start, 'America/Los_Angeles') AS uptime_start,
  DATETIME(uptime_end, 'America/Los_Angeles') AS uptime_end
FROM
  `{}.{}.{}`
WHERE
  session_id = @agent_id
  AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
ORDER BY uptime_start
"""


@dataclass
class WorkspaceUptimeEvent:
    uptime_start: datetime.datetime
    uptime_end: datetime.datetime


def get_workspace_uptime_events(
    agent_id: str, project: str, dataset: str
) -> list[WorkspaceUptimeEvent]:
    client = bigquery.Client(project="system-services-dev")

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("agent_id", "STRING", agent_id),
        ]
    )
    query = QUERY.format(project, dataset, UPTIME_TABLE)

    query_job = client.query(query, job_config=job_config)
    results = query_job.result()

    events = [WorkspaceUptimeEvent(row.uptime_start, row.uptime_end) for row in results]
    return events


def coalesce_events(
    events: list[WorkspaceUptimeEvent],
    max_gap: datetime.timedelta = datetime.timedelta(seconds=5),
) -> list[WorkspaceUptimeEvent]:
    if not events:
        return []

    # Test for monotonicity
    for event in events:
        assert (
            event.uptime_start <= event.uptime_end
        ), f"Event start {event.uptime_start} after end {event.uptime_end}"
    for first, second in zip(events, events[1:]):
        assert (
            first.uptime_end <= second.uptime_start
            or abs(first.uptime_end - second.uptime_start) < max_gap
        ), f"Events not monotonic: {first.uptime_end} after {second.uptime_start}"

    # Coalesce
    coalesced = [events[0]]
    for event in events[1:]:
        if abs(coalesced[-1].uptime_end - event.uptime_start) < max_gap:
            coalesced[-1].uptime_end = event.uptime_end
        else:
            coalesced.append(event)
    return coalesced


def main():
    parser = argparse.ArgumentParser(
        description="Query BigQuery for remote agent workspace uptime"
    )
    parser.add_argument(
        "agent_id",
        type=str,
        help="The ID of the remote agent to query for",
    )
    parser.add_argument(
        "--project",
        type=str,
        default="system-services-prod",
        help="The GCP project to query",
    )
    parser.add_argument(
        "--dataset",
        type=str,
        default="us_cross_env_request_insight_analytics_dataset",
        help="The BigQuery dataset to query",
    )
    args = parser.parse_args()

    print(
        f"Querying {args.project}.{args.dataset}.{UPTIME_TABLE} for agent {args.agent_id}"
    )

    events = get_workspace_uptime_events(args.agent_id, args.project, args.dataset)
    events = coalesce_events(events)

    rows = [
        (
            event.uptime_start.strftime("%Y-%m-%d %H:%M:%S"),
            event.uptime_end.strftime("%Y-%m-%d %H:%M:%S"),
        )
        for event in events
    ]

    print("start_time             end_time")
    for row in rows:
        print(TABLE_SEPARATOR.join([str(item) for item in row]))


if __name__ == "__main__":
    main()
