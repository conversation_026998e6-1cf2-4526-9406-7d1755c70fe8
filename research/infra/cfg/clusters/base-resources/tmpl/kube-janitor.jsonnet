function(
  k8s,
  name='kube-janitor',
  namespace='kube-janitor',
  version='23.7.0',
  cpu='500m',
  ram='500Mi',
  args=[],
  rbac_rules=[],
  config_rules=null,
) k8s + {

  BaseLabels+:: {
    'aug.service': name,
    'aug.version': version,
  },
  BaseObject+:: {
    name:: name,
  },
  Object+:: {
    namespace:: $.ns.metadata.name,
  },

  ns: $.Namespace + {
    name:: namespace,
  },

  cfg: if config_rules != null then $.ConfigMap + {
    data: {
      'kube-janitor.rules.yaml': std.manifestYamlDoc(config_rules),
    },
    mountpoint:: '/run/config',
    config_path:: self.mountpoint + '/kube-janitor.rules.yaml',
  },

  sa: $.ServiceAccount + {},

  deploy: $.Deployment + {
    spec+: {
      replicas: 1,
      template+: {
        spec+: {
          local pod = self,
          serviceAccountName: $.sa.metadata.name,
          containers: [
            $.Container + {
              name: name,
              image: 'hjacobs/kube-janitor:' + version,
              volumeMounts: pod.volmount_mounts,
              args: (
                if $.cfg != null then ['--rules-file=' + $.cfg.config_path] else []
              ) + args,
              resources: {
                limits: self.requests,
                requests: {
                  cpu: cpu,
                  memory: ram,
                },
              },
            },
          ],
          volmounts:: if $.cfg != null then [
            {
              name:: $.cfg.metadata.name,
              volume:: {
                configMap: {
                  name: $.cfg.metadata.name,
                },
              },
              mount:: {
                mountPath: $.cfg.mountpoint,
              },
            },
          ] else [],
        },
      },
    },
  },

  role: $.ClusterRole + {
    rules: [
      self.Rule + {
        apiGroups: [''],
        resources: ['events'],
        verbs: [self.CREATE],
      },
    ] + rbac_rules,
  },

  rb: $.ClusterRoleBinding + {
    role_name: $.role.metadata.name,
    sas: [$.sa],
  },
}
